import { Hono } from 'hono';
import { IServiceProvider } from '../di/type';
import {
  CorsOptions,
  createCorsMiddleware,
} from '../middlewares/cors.middleware';
import {
  RequestLoggerOptions,
  createRequestLoggerMiddleware,
} from '../middlewares/request-logger.middleware';
import { AppEnv, AppMiddlewareHandler } from './interfaces';

export interface IApplication {
  /**
   * Use middleware
   */
  UseMiddleware(middleware: AppMiddlewareHandler): IApplication;

  /**
   * Configure CORS
   */
  UseCors(options?: CorsOptions): IApplication;

  /**
   * Configure request logger
   */
  UseRequestLogger(options?: RequestLoggerOptions): IApplication;

  /**
   * Build the application
   */
  build(): Hono<AppEnv>;
}

// AppEnv is now imported from interfaces.ts

export class Application implements IApplication {
  private readonly _app: Hono<AppEnv>;
  private readonly _serviceProvider: IServiceProvider;

  constructor(serviceProvider: IServiceProvider) {
    this._app = new Hono<AppEnv>();
    this._serviceProvider = serviceProvider;

    // Add service provider to context
    this._app.use('*', async (c, next) => {
      c.set('serviceProvider', this._serviceProvider);
      await next();
    });
  }

  /**
   * Use middleware
   */
  public UseMiddleware(middleware: AppMiddlewareHandler): IApplication {
    this._app.use('*', middleware);
    return this;
  }

  /**
   * Configure CORS
   */
  public UseCors(options?: CorsOptions): IApplication {
    const corsMiddleware = createCorsMiddleware(options);
    this._app.use('*', corsMiddleware);
    return this;
  }

  /**
   * Configure request logger
   */
  public UseRequestLogger(options?: RequestLoggerOptions): IApplication {
    const requestLoggerMiddleware = createRequestLoggerMiddleware(options);
    this._app.use('*', requestLoggerMiddleware);
    return this;
  }

  /**
   * Build the application
   */
  public build(): Hono<AppEnv> {
    return this._app;
  }
}
