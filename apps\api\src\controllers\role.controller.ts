import { Context } from 'hono';
import {
  Controller,
  ControllerBase,
  HttpContext,
  HttpGet,
  HttpPost,
  HttpPut,
  HttpDelete,
  Inject,
  Param,
  Query,
  UnauthorizedError,
} from '@c-cam/core';
import RoleService from '../services/RoleService';

@Controller('/api/roles')
export class RoleController extends ControllerBase {
  constructor(
    @Inject(RoleService) private roleService: RoleService,
  ) {
    super();
  }

  /**
   * Helper method to get authenticated user ID
   */
  private getAuthenticatedUserId(c: Context): string {
    const userId = c.get('userId');
    if (!userId) {
      throw new UnauthorizedError('You must be authenticated to access this resource');
    }
    return userId;
  }

  /**
   * Get all roles
   */
  @HttpGet('/')
  async getRoles(
    @HttpContext() c: Context,
    @Query('limit') limit?: string,
    @Query('skip') skip?: string,
    @Query('sortBy') sortBy?: string,
    @Query('sortDirection') sortDirection?: string,
  ): Promise<Response> {
    this.getAuthenticatedUserId(c);

    const roles = await this.roleService.find({
      limit: limit ? parseInt(limit) : undefined,
      skip: skip ? parseInt(skip) : undefined,
      sortBy,
      sortDirection,
    });

    return this.success(c, { roles });
  }

  /**
   * Get a role by ID
   */
  @HttpGet('/:id')
  async getRoleById(
    @HttpContext() c: Context,
    @Param('id') id: string,
  ): Promise<Response> {
    this.getAuthenticatedUserId(c);
    this.validateIf(!id, 'Role ID is required');

    const role = await this.roleService.findById(id);
    this.notFoundIf(!role, 'Role not found');

    return this.success(c, { role });
  }

  /**
   * Create a new role
   */
  @HttpPost('/')
  async createRole(@HttpContext() c: Context): Promise<Response> {
    const userId = this.getAuthenticatedUserId(c);
    const { name, memberRoleId, permissionId } = await c.req.json();

    // Validate required fields
    this.validateRequiredFields({ name }, ['name']);

    const role = await this.roleService.createRole(
      name,
      userId,
      memberRoleId,
      permissionId
    );

    return this.created(c, { role }, 'Role created successfully');
  }

  /**
   * Update a role
   */
  @HttpPut('/:id')
  async updateRole(
    @HttpContext() c: Context,
    @Param('id') id: string,
  ): Promise<Response> {
    this.getAuthenticatedUserId(c);
    this.validateIf(!id, 'Role ID is required');

    const { name, memberRoleId, permissionId } = await c.req.json();

    // Sanitize the data
    const updateData = this.sanitizeData({
      name,
      memberRoleId,
      permissionId
    });

    const success = await this.roleService.updateRole(id, updateData);
    this.validateIf(!success, 'Failed to update role');

    return this.success(c, { success: true }, 'Role updated successfully');
  }

  /**
   * Delete a role
   */
  @HttpDelete('/:id')
  async deleteRole(
    @HttpContext() c: Context,
    @Param('id') id: string,
  ): Promise<Response> {
    this.getAuthenticatedUserId(c);
    this.validateIf(!id, 'Role ID is required');

    const success = await this.roleService.delete(id);
    this.validateIf(!success, 'Failed to delete role');

    return this.success(c, { success: true }, 'Role deleted successfully');
  }

  /**
   * Find a role by name
   */
  @HttpGet('/name/:name')
  async getRoleByName(
    @HttpContext() c: Context,
    @Param('name') name: string,
  ): Promise<Response> {
    this.getAuthenticatedUserId(c);
    this.validateIf(!name, 'Role name is required');

    const role = await this.roleService.findByName(name);
    this.notFoundIf(!role, 'Role not found');

    return this.success(c, { role });
  }

  /**
   * Find roles by member role ID
   */
  @HttpGet('/member-role/:memberRoleId')
  async getRolesByMemberRoleId(
    @HttpContext() c: Context,
    @Param('memberRoleId') memberRoleId: string,
  ): Promise<Response> {
    this.getAuthenticatedUserId(c);
    this.validateIf(!memberRoleId, 'Member role ID is required');

    const roles = await this.roleService.findByMemberRoleId(memberRoleId);
    return this.success(c, { roles });
  }

  /**
   * Find roles by permission ID
   */
  @HttpGet('/permission/:permissionId')
  async getRolesByPermissionId(
    @HttpContext() c: Context,
    @Param('permissionId') permissionId: string,
  ): Promise<Response> {
    this.getAuthenticatedUserId(c);
    this.validateIf(!permissionId, 'Permission ID is required');

    const roles = await this.roleService.findByPermissionId(permissionId);
    return this.success(c, { roles });
  }

  /**
   * Find roles by creator
   */
  @HttpGet('/created-by/:createdBy')
  async getRolesByCreator(
    @HttpContext() c: Context,
    @Param('createdBy') createdBy: string,
  ): Promise<Response> {
    this.getAuthenticatedUserId(c);
    this.validateIf(!createdBy, 'Creator ID is required');

    const roles = await this.roleService.findByCreatedBy(createdBy);
    return this.success(c, { roles });
  }
}
