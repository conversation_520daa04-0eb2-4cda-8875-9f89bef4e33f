import { Document } from 'mongoose';
import { createSchema, createModel } from '@c-cam/core';
import { RoleAttributes } from '@c-cam/types';

/**
 * Role Document Interface
 * Extends the RoleAttributes (excluding id) and Document
 */
export interface RoleDocument extends Omit<RoleAttributes, 'id'>, Document {}

/**
 * Role Schema
 * Defines the MongoDB schema for roles
 */
const RoleSchema = createSchema({
  member_role_id: {
    type: String,
    ref: 'member_role',
    required: false,
  },
  permission_id: {
    type: String,
    ref: 'permission',
    required: false,
  },
  name: { type: String, required: true },
  created_by: { type: String, required: true },
});

// Add indexes
RoleSchema.index({ name: 1 }, { unique: true });

// Create and export the model
const RoleModel = createModel<RoleDocument>('role', RoleSchema);

export default RoleModel;
