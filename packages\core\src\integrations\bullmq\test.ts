import { BullMQConnection, BullMQClient } from './index.js';

/**
 * Simple test to verify BullMQ integration works
 */
async function testBullMQIntegration() {
  console.log('🚀 Testing BullMQ Integration...');

  try {
    // Initialize connection
    const connection = BullMQConnection.getInstance();
    await connection.initialize({
      connection: {
        host: 'localhost',
        port: 6379,
        db: 0,
      },
      healthCheck: {
        enabled: true,
        interval: 30000,
      },
    });

    console.log('✅ BullMQ connection initialized');

    // Check health
    const isHealthy = await connection.healthCheck();
    console.log(`✅ Health check: ${isHealthy ? 'HEALTHY' : 'UNHEALTHY'}`);

    // Create client
    const client = new BullMQClient({
      queuePrefix: 'test',
    });

    // Create queue
    await client.createQueue('simple-jobs');
    console.log('✅ Queue created');

    // Create worker
    await client.createWorker(
      'simple-jobs',
      async (job) => {
        console.log(`Processing job ${job.id}:`, job.data);
        await new Promise(resolve => setTimeout(resolve, 1000));
        return { processed: true, timestamp: new Date().toISOString() };
      },
      { concurrency: 1 },
      {
        onCompleted: async (job, result) => {
          console.log(`✅ Job ${job.id} completed:`, result);
        },
        onFailed: async (job, error) => {
          console.error(`❌ Job ${job?.id} failed:`, error.message);
        },
      }
    );

    console.log('✅ Worker created');

    // Add a test job
    const job = await client.addJob('simple-jobs', {
      type: 'test-job',
      payload: { message: 'Hello BullMQ!' },
    });

    console.log(`✅ Job added with ID: ${job.id}`);

    // Wait for processing
    await new Promise(resolve => setTimeout(resolve, 3000));

    // Get stats
    const stats = await client.getQueueStats('simple-jobs');
    console.log('📊 Queue stats:', stats);

    // Clean up
    await connection.close();
    console.log('✅ Connection closed');

    console.log('🎉 BullMQ integration test completed successfully!');
  } catch (error) {
    console.error('❌ BullMQ integration test failed:', error);
    throw error;
  }
}

// Export test function
export { testBullMQIntegration };
