/**
 * DateTime prototype extensions for improved date handling
 */

declare global {
  interface Date {
    /**
     * Format date to YYYY-MM-DD
     */
    toDateString(): string;

    /**
     * Format date to HH:MM:SS
     */
    toTimeString(): string;

    /**
     * Format date to YYYY-MM-DD HH:MM:SS
     */
    toDateTimeString(): string;

    /**
     * Add days to current date
     * @param days Number of days to add
     */
    addDays(days: number): Date;

    /**
     * Check if date is today
     */
    isToday(): boolean;

    /**
     * Get start of day (00:00:00)
     */
    startOfDay(): Date;

    /**
     * Get end of day (23:59:59)
     */
    endOfDay(): Date;
  }
}

// Implementation
Date.prototype.toDateString = function(): string {
  return `${this.getFullYear()}-${String(this.getMonth() + 1).padStart(2, '0')}-${String(this.getDate()).padStart(2, '0')}`;
};

Date.prototype.toTimeString = function(): string {
  return `${String(this.getHours()).padStart(2, '0')}:${String(this.getMinutes()).padStart(2, '0')}:${String(this.getSeconds()).padStart(2, '0')}`;
};

Date.prototype.toDateTimeString = function(): string {
  return `${this.toDateString()} ${this.toTimeString()}`;
};

Date.prototype.addDays = function(days: number): Date {
  const date = new Date(this.valueOf());
  date.setDate(date.getDate() + days);
  return date;
};

Date.prototype.isToday = function(): boolean {
  const today = new Date();
  return this.getDate() === today.getDate() &&
    this.getMonth() === today.getMonth() &&
    this.getFullYear() === today.getFullYear();
};

Date.prototype.startOfDay = function(): Date {
  const date = new Date(this.valueOf());
  date.setHours(0, 0, 0, 0);
  return date;
};

Date.prototype.endOfDay = function(): Date {
  const date = new Date(this.valueOf());
  date.setHours(23, 59, 59, 999);
  return date;
};

export {}; // This export is needed to make the file a module