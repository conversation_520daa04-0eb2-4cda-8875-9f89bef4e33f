import { Context } from 'hono';
import {
  Controller,
  ControllerBase,
  HttpContext,
  HttpGet,
  HttpPost,
  HttpPut,
  HttpDelete,
  Inject,
} from '@c-cam/core';
import DailyAttendanceSummariesService from '../services/DailyAttendanceSummariesService';

@Controller('/api/attendance-summaries')
export class DailyAttendanceSummariesController extends ControllerBase {
  constructor(
    @Inject(DailyAttendanceSummariesService) private attendanceService: DailyAttendanceSummariesService,
  ) {
    super();
  }

  /**
   * Get all attendance summaries
   */
  @HttpGet('/')
  async getAttendanceSummaries(@HttpContext() c: Context): Promise<Response> {
    try {
      const userId = c.get('userId');
      if (!userId) {
        return c.json({ error: 'Unauthorized' }, 401);
      }

      const { limit, skip, sortBy, sortDirection } = c.req.query();

      const summaries = await this.attendanceService.find({
        limit: limit ? parseInt(limit) : undefined,
        skip: skip ? parseInt(skip) : undefined,
        sortBy,
        sortDirection,
      });

      return c.json({ summaries });
    } catch (error: any) {
      return c.json({ error: error.message }, 500);
    }
  }

  /**
   * Find late attendance summaries
   */
  @HttpGet('/late')
  async getLateAttendanceSummaries(@HttpContext() c: Context): Promise<Response> {
    try {
      const userId = c.get('userId');
      if (!userId) {
        return c.json({ error: 'Unauthorized' }, 401);
      }

      const summaries = await this.attendanceService.findLateAttendances();

      return c.json({ summaries });
    } catch (error: any) {
      return c.json({ error: error.message }, 500);
    }
  }

  /**
   * Find early leave attendance summaries
   */
  @HttpGet('/early-leave')
  async getEarlyLeaveAttendanceSummaries(@HttpContext() c: Context): Promise<Response> {
    try {
      const userId = c.get('userId');
      if (!userId) {
        return c.json({ error: 'Unauthorized' }, 401);
      }

      const summaries = await this.attendanceService.findEarlyLeaveAttendances();

      return c.json({ summaries });
    } catch (error: any) {
      return c.json({ error: error.message }, 500);
    }
  }

  /**
   * Find attendance summaries by user ID
   */
  @HttpGet('/user/:userId')
  async getAttendanceSummariesByUserId(@HttpContext() c: Context): Promise<Response> {
    try {
      const userId = c.get('userId');
      if (!userId) {
        return c.json({ error: 'Unauthorized' }, 401);
      }

      const { userId: targetUserId } = c.req.param();
      if (!targetUserId) {
        return c.json({ error: 'User ID is required' }, 400);
      }
      const summaries = await this.attendanceService.findByUserId(targetUserId);

      return c.json({ summaries });
    } catch (error: any) {
      return c.json({ error: error.message }, 500);
    }
  }

  /**
   * Find attendance summaries by user ID and date range
   */
  @HttpGet('/user/:userId/date-range/:startDate/:endDate')
  async getAttendanceSummariesByUserIdAndDateRange(@HttpContext() c: Context): Promise<Response> {
    try {
      const authUserId = c.get('userId');
      if (!authUserId) {
        return c.json({ error: 'Unauthorized' }, 401);
      }

      const { userId, startDate, endDate } = c.req.param();
      if (!userId || !startDate || !endDate) {
        return c.json({ error: 'User ID, start date, and end date are required' }, 400);
      }
      const start = new Date(startDate);
      const end = new Date(endDate);

      if (isNaN(start.getTime()) || isNaN(end.getTime())) {
        return c.json({ error: 'Invalid date format' }, 400);
      }

      const summaries = await this.attendanceService.findByUserIdAndDateRange(userId, start, end);

      return c.json({ summaries });
    } catch (error: any) {
      return c.json({ error: error.message }, 500);
    }
  }

  /**
   * Find attendance summaries by shift ID
   */
  @HttpGet('/shift/:shiftId')
  async getAttendanceSummariesByShiftId(@HttpContext() c: Context): Promise<Response> {
    try {
      const userId = c.get('userId');
      if (!userId) {
        return c.json({ error: 'Unauthorized' }, 401);
      }

      const { shiftId } = c.req.param();
      if (!shiftId) {
        return c.json({ error: 'Shift ID is required' }, 400);
      }
      const summaries = await this.attendanceService.findByShiftId(shiftId);

      return c.json({ summaries });
    } catch (error: any) {
      return c.json({ error: error.message }, 500);
    }
  }

  /**
   * Find attendance summaries by holiday ID
   */
  @HttpGet('/holiday/:holidayId')
  async getAttendanceSummariesByHolidayId(@HttpContext() c: Context): Promise<Response> {
    try {
      const userId = c.get('userId');
      if (!userId) {
        return c.json({ error: 'Unauthorized' }, 401);
      }

      const { holidayId } = c.req.param();
      if (!holidayId) {
        return c.json({ error: 'Holiday ID is required' }, 400);
      }
      const summaries = await this.attendanceService.findByHolidayId(holidayId);

      return c.json({ summaries });
    } catch (error: any) {
      return c.json({ error: error.message }, 500);
    }
  }

  /**
   * Find attendance summaries by work date
   */
  @HttpGet('/date/:workDate')
  async getAttendanceSummariesByWorkDate(@HttpContext() c: Context): Promise<Response> {
    try {
      const userId = c.get('userId');
      if (!userId) {
        return c.json({ error: 'Unauthorized' }, 401);
      }

      const { workDate } = c.req.param();
      if (!workDate) {
        return c.json({ error: 'Work date is required' }, 400);
      }
      const date = new Date(workDate);

      if (isNaN(date.getTime())) {
        return c.json({ error: 'Invalid date format' }, 400);
      }

      const summaries = await this.attendanceService.findByWorkDate(date);

      return c.json({ summaries });
    } catch (error: any) {
      return c.json({ error: error.message }, 500);
    }
  }

  /**
   * Find attendance summaries by date range
   */
  @HttpGet('/date-range/:startDate/:endDate')
  async getAttendanceSummariesByDateRange(@HttpContext() c: Context): Promise<Response> {
    try {
      const userId = c.get('userId');
      if (!userId) {
        return c.json({ error: 'Unauthorized' }, 401);
      }

      const { startDate, endDate } = c.req.param();
      if (!startDate || !endDate) {
        return c.json({ error: 'Start date and end date are required' }, 400);
      }
      const start = new Date(startDate);
      const end = new Date(endDate);

      if (isNaN(start.getTime()) || isNaN(end.getTime())) {
        return c.json({ error: 'Invalid date format' }, 400);
      }

      const summaries = await this.attendanceService.findByDateRange(start, end);

      return c.json({ summaries });
    } catch (error: any) {
      return c.json({ error: error.message }, 500);
    }
  }

  /**
   * Get an attendance summary by ID
   */
  @HttpGet('/:id')
  async getAttendanceSummaryById(@HttpContext() c: Context): Promise<Response> {
    try {
      const userId = c.get('userId');
      if (!userId) {
        return c.json({ error: 'Unauthorized' }, 401);
      }

      const { id } = c.req.param();
      if (!id) {
        return c.json({ error: 'Attendance summary ID is required' }, 400);
      }
      const summary = await this.attendanceService.findById(id);

      if (!summary) {
        return c.json({ error: 'Attendance summary not found' }, 404);
      }

      return c.json({ summary });
    } catch (error: any) {
      return c.json({ error: error.message }, 500);
    }
  }

  /**
   * Create a new attendance summary
   */
  @HttpPost('/')
  async createAttendanceSummary(@HttpContext() c: Context): Promise<Response> {
    try {
      const userId = c.get('userId');
      if (!userId) {
        return c.json({ error: 'Unauthorized' }, 401);
      }

      const attendanceData = await c.req.json();

      // Add the creator ID
      attendanceData.created_by = userId;

      // Parse dates
      if (attendanceData.work_date) {
        attendanceData.work_date = new Date(attendanceData.work_date);
      }

      if (attendanceData.checkin_time) {
        attendanceData.checkin_time = new Date(attendanceData.checkin_time);
      }

      if (attendanceData.checkout_time) {
        attendanceData.checkout_time = new Date(attendanceData.checkout_time);
      }

      // Validate required fields
      if (!attendanceData.user_id || !attendanceData.shift_id || !attendanceData.work_date) {
        return c.json({ error: 'User ID, Shift ID, and Work Date are required' }, 400);
      }

      const summary = await this.attendanceService.createAttendanceSummary(attendanceData);
      return c.json({ summary }, 201);
    } catch (error: any) {
      return c.json({ error: error.message }, 500);
    }
  }

  /**
   * Update an attendance summary
   */
  @HttpPut('/:id')
  async updateAttendanceSummary(@HttpContext() c: Context): Promise<Response> {
    try {
      const userId = c.get('userId');
      if (!userId) {
        return c.json({ error: 'Unauthorized' }, 401);
      }

      const { id } = c.req.param();
      if (!id) {
        return c.json({ error: 'Attendance summary ID is required' }, 400);
      }
      const attendanceData = await c.req.json();

      // Parse dates
      if (attendanceData.work_date) {
        attendanceData.work_date = new Date(attendanceData.work_date);
      }

      if (attendanceData.checkin_time) {
        attendanceData.checkin_time = new Date(attendanceData.checkin_time);
      }

      if (attendanceData.checkout_time) {
        attendanceData.checkout_time = new Date(attendanceData.checkout_time);
      }

      const success = await this.attendanceService.updateAttendanceSummary(id, attendanceData);

      if (!success) {
        return c.json({ error: 'Failed to update attendance summary' }, 400);
      }

      return c.json({ success: true });
    } catch (error: any) {
      return c.json({ error: error.message }, 500);
    }
  }

  /**
   * Delete an attendance summary
   */
  @HttpDelete('/:id')
  async deleteAttendanceSummary(@HttpContext() c: Context): Promise<Response> {
    try {
      const userId = c.get('userId');
      if (!userId) {
        return c.json({ error: 'Unauthorized' }, 401);
      }

      const { id } = c.req.param();
      if (!id) {
        return c.json({ error: 'Attendance summary ID is required' }, 400);
      }
      const success = await this.attendanceService.delete(id);

      if (!success) {
        return c.json({ error: 'Failed to delete attendance summary' }, 400);
      }

      return c.json({ success: true });
    } catch (error: any) {
      return c.json({ error: error.message }, 500);
    }
  }
}
