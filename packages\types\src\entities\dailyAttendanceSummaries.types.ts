/**
 * Daily Attendance Summaries Attributes Interface
 * Defines the core data structure for daily attendance summaries
 */
export interface DailyAttendanceSummariesAttributes {
  id: string;
  user_id: string;
  shift_id: string;
  holiday_id: string;
  work_date: Date;
  is_late: boolean;
  is_early_leave: boolean;
  checkin_time: Date;
  checkout_time: Date;
  late_minutes: number;
  early_leave_minutes: number;
  expected_work_minutes: number;
  total_work_minutes: number;
  note: string;
  created_by: string;
  created_at: Date;
}
