# Refresh Token Duplicate Call Fix

## Problem Description

The application was experiencing **multiple unnecessary calls** to `/api/identity/refresh-token` endpoint, causing:

1. **Performance Issues**: Multiple simultaneous API calls
2. **Race Conditions**: Competing refresh attempts
3. **User Experience**: Potential token conflicts and errors
4. **Server Load**: Unnecessary backend processing

## Root Cause Analysis

### Multiple Refresh Triggers

1. **Axios Interceptor** (Primary): Automatically refreshes on 401 errors
2. **useIdentityActions Hook**: Manual refresh on currentUserQuery 401 errors  
3. **Scheduled Refresh**: Timer-based refresh before token expiry
4. **Component Re-renders**: Hook re-execution triggering duplicate calls

### Race Condition Scenarios

```typescript
// Scenario 1: Multiple 401s at the same time
Promise.all([
  api.get('/api/users'),     // 401 → triggers refresh
  api.get('/api/profile'),   // 401 → triggers refresh  
  api.get('/api/settings'),  // 401 → triggers refresh
])

// Scenario 2: Hook + Interceptor conflict
useEffect(() => {
  if (currentUserQuery.error?.status === 401) {
    refreshAccessToken() // Manual refresh
  }
}, [currentUserQuery.error])
// + Axios interceptor also refreshing the same 401
```

## Solution Implementation

### 1. **Global Refresh Flag** (`useIdentityActions.ts`)

```typescript
// Global flag to prevent multiple refresh attempts
let isRefreshingGlobal = false

const refreshAccessToken = useCallback(async () => {
  // Prevent multiple simultaneous refresh attempts
  if (isRefreshingGlobal) {
    console.debug('Token refresh already in progress, skipping')
    return
  }

  try {
    isRefreshingGlobal = true
    // ... refresh logic
  } finally {
    isRefreshingGlobal = false
  }
}, [])
```

### 2. **Improved Axios Interceptor** (`axios.ts`)

```typescript
// Promise to track ongoing refresh
let refreshPromise: Promise<string> | null = null

// Create refresh promise if not exists
if (!refreshPromise) {
  refreshPromise = (async () => {
    try {
      // Single refresh attempt
      const refreshResponse = await axios.post('/api/identity/refresh-token', ...)
      return refreshResponse.data.access_token
    } finally {
      refreshPromise = null
    }
  })()
}

// All 401 requests wait for the same promise
const access_token = await refreshPromise
```

### 3. **Removed Duplicate Logic**

**Before:**
```typescript
// useIdentityActions.ts - REMOVED
useEffect(() => {
  if (currentUserQuery.error?.response?.status === 401) {
    refreshAccessToken() // Duplicate with axios interceptor
  }
}, [currentUserQuery.error, refreshAccessToken])
```

**After:**
```typescript
// useIdentityActions.ts - FIXED
useEffect(() => {
  if (currentUserQuery.error?.response?.status === 401) {
    console.debug('User query failed with 401, axios interceptor will handle token refresh')
    // Don't call refreshAccessToken here to avoid duplicate refresh attempts
  }
}, [currentUserQuery.error])
```

### 4. **Smart Scheduling**

```typescript
const scheduleTokenRefresh = useCallback((token: string) => {
  // Don't schedule if already refreshing
  if (isRefreshingGlobal) return

  const timeUntilRefresh = expiryTime - currentTime - (2 * 60 * 1000)
  
  if (timeUntilRefresh <= 0) {
    // Let axios interceptor handle it when needed
    console.debug('Token expiring soon, will be refreshed by interceptor when needed')
  } else {
    // Schedule refresh 2 minutes before expiry
    setTimeout(() => {
      if (!isRefreshingGlobal && !isTokenExpired(getAccessToken() || '')) {
        refreshAccessToken()
      }
    }, timeUntilRefresh)
  }
}, [])
```

## Benefits

### ✅ **Performance Improvements**
- **Single API Call**: Only one refresh request per token expiry
- **Request Queuing**: Failed requests wait for refresh completion
- **Reduced Server Load**: Eliminates unnecessary API calls

### ✅ **Better User Experience**
- **No Token Conflicts**: Consistent token state across app
- **Faster Response**: Queued requests retry immediately after refresh
- **Silent Refresh**: No spam notifications for automatic refreshes

### ✅ **Improved Reliability**
- **Race Condition Prevention**: Global coordination of refresh attempts
- **Error Handling**: Graceful fallback when refresh fails
- **Debug Logging**: Better visibility into refresh behavior

## Testing Strategy

### Unit Tests
```typescript
it('should only call refresh token API once for multiple simultaneous 401 errors', async () => {
  const promises = [
    axiosInstance.get('/api/test1').catch(() => {}),
    axiosInstance.get('/api/test2').catch(() => {}),
    axiosInstance.get('/api/test3').catch(() => {}),
  ]
  
  await Promise.all(promises)
  
  expect(mockPost).toHaveBeenCalledTimes(1) // Only one refresh call
})
```

### Manual Testing
1. **Multiple Tabs**: Open app in multiple browser tabs
2. **Token Expiry**: Wait for token to expire naturally
3. **Simultaneous Requests**: Make API calls from different tabs
4. **Network Monitoring**: Verify only one refresh call in DevTools

## Monitoring & Debugging

### Console Logs
```typescript
console.debug('Starting token refresh...')           // Hook refresh start
console.debug('Axios interceptor: Starting token refresh...') // Interceptor refresh
console.debug('Token refresh already in progress, skipping')  // Duplicate prevention
console.debug('Token refreshed successfully')        // Success confirmation
```

### Network Tab Verification
- **Before Fix**: Multiple `/api/identity/refresh-token` calls
- **After Fix**: Single refresh call, followed by retried requests

## Migration Notes

### Breaking Changes
- None - all changes are internal optimizations

### Backward Compatibility
- All existing functionality preserved
- API contracts unchanged
- User experience improved

### Configuration
No configuration changes required. The fix is automatic and transparent.

## Future Improvements

1. **Cross-Tab Coordination**: Use localStorage events for multi-tab sync
2. **Refresh Prediction**: Proactive refresh based on usage patterns  
3. **Retry Strategies**: Exponential backoff for failed refreshes
4. **Metrics Collection**: Track refresh success rates and timing

## Conclusion

This fix eliminates the refresh token duplicate call issue by:
- **Centralizing refresh logic** in axios interceptor
- **Adding global coordination** to prevent race conditions
- **Removing duplicate triggers** from React hooks
- **Implementing smart scheduling** to avoid unnecessary refreshes

The result is a more efficient, reliable, and user-friendly authentication system.
