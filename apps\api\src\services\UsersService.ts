import { Injectable, BaseModel, Inject } from '@c-cam/core';
import { UsersDocument } from '@/database/entities/UsersModel';
import UsersRepository from '@/repositories/UsersRepository';

/**
 * Service for managing users
 * Extends the BaseModel with UsersDocument type
 */
@Injectable()
class UsersService extends BaseModel<UsersDocument> {
  /**
   * Create a new UsersService
   * @param usersRepository The users repository
   * @param tenantId Optional tenant ID for multi-tenant operations
   */
  constructor(@Inject(UsersRepository) private usersRepository: UsersRepository) {
    super(usersRepository);
  }

  /**
   * Create a new user
   * @param userData The user data
   * @returns The newly created user
   */
  async createUser(userData: {
    unit_id: string;
    code: string;
    name: string;
    email?: string;
    phone?: string;
    dob?: Date;
    gender?: string;
    username: string;
    password: string;
    created_by: string;
    face_id?: string;
    member_role_id?: string;
  }): Promise<UsersDocument> {
    // Validate email format if provided
    if (userData.email && !userData.email.includes('@')) {
      throw new Error('Invalid email format');
    }

    // Check if username already exists
    const existingUsername = await this.usersRepository.findByUsername(userData.username);
    if (existingUsername) {
      throw new Error(`Username '${userData.username}' is already taken`);
    }

    // Check if email already exists (if provided)
    if (userData.email) {
      const existingEmail = await this.usersRepository.findByEmail(userData.email);
      if (existingEmail) {
        throw new Error(`Email '${userData.email}' is already registered`);
      }
    }

    // Check if code already exists
    const existingCode = await this.usersRepository.findByCode(userData.code);
    if (existingCode) {
      throw new Error(`User code '${userData.code}' is already in use`);
    }

    // Create the new user
    return this.create(userData);
  }

  /**
   * Update a user
   * @param id The user ID
   * @param userData The data to update
   * @returns True if the user was updated, false otherwise
   */
  async updateUser(
    id: string,
    userData: Partial<{
      unit_id: string;
      code: string;
      name: string;
      email: string;
      phone: string;
      dob: Date;
      gender: string;
      username: string;
      password: string;
      face_id: string;
      member_role_id: string;
    }>,
  ): Promise<boolean> {
    // Check if the user exists
    const user = await this.findById(id);
    if (!user) {
      throw new Error(`User with ID '${id}' not found`);
    }

    // Validate email format if provided
    if (userData.email && !userData.email.includes('@')) {
      throw new Error('Invalid email format');
    }

    // Check for unique constraints
    if (userData.username && userData.username !== user.username) {
      const existingUsername = await this.usersRepository.findByUsername(userData.username);
      if (existingUsername && existingUsername.id !== id) {
        throw new Error(`Username '${userData.username}' is already taken`);
      }
    }

    if (userData.email && userData.email !== user.email) {
      const existingEmail = await this.usersRepository.findByEmail(userData.email);
      if (existingEmail && existingEmail.id !== id) {
        throw new Error(`Email '${userData.email}' is already registered`);
      }
    }

    if (userData.code && userData.code !== user.code) {
      const existingCode = await this.usersRepository.findByCode(userData.code);
      if (existingCode && existingCode.id !== id) {
        throw new Error(`User code '${userData.code}' is already in use`);
      }
    }

    // Update the user
    return this.update(id, userData);
  }

  /**
   * Authenticate a user
   * @param username The username
   * @param password The password
   * @returns The authenticated user or null if authentication fails
   */
  async authenticate(username: string, password: string): Promise<UsersDocument | null> {
    // Find the user by username
    const user = await this.usersRepository.findByUsernameSelectPassword(username);

    const isValidPassword = (await user?.checkPassword(password)) || false;

    // If no user is found or the password is incorrect, return null
    if (!user || !isValidPassword) {
      return null;
    }

    // Return the user without the password field
    // Use the id property which is guaranteed to be a string
    return user;
  }

  /**
   * Find a user by username
   * @param username The username
   * @returns The user or null if not found
   */
  async findByUsername(username: string): Promise<UsersDocument | null> {
    return this.usersRepository.findByUsername(username);
  }

  /**
   * Find a user by email
   * @param email The email
   * @returns The user or null if not found
   */
  async findByEmail(email: string): Promise<UsersDocument | null> {
    return this.usersRepository.findByEmail(email);
  }

  /**
   * Find a user by code
   * @param code The user code
   * @returns The user or null if not found
   */
  async findByCode(code: string): Promise<UsersDocument | null> {
    return this.usersRepository.findByCode(code);
  }

  /**
   * Find users by unit ID
   * @param unitId The unit ID
   * @returns An array of users
   */
  async findByUnitId(unitId: string): Promise<UsersDocument[]> {
    return this.usersRepository.findByUnitId(unitId);
  }

  /**
   * Find users by member role ID
   * @param memberRoleId The member role ID
   * @returns An array of users
   */
  async findByMemberRoleId(memberRoleId: string): Promise<UsersDocument[]> {
    return this.usersRepository.findByMemberRoleId(memberRoleId);
  }

  /**
   * Find users by face ID
   * @param faceId The face ID
   * @returns An array of users
   */
  async findByFaceId(faceId: string): Promise<UsersDocument[]> {
    return this.usersRepository.findByFaceId(faceId);
  }
}

export default UsersService;
