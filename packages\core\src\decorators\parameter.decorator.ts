import 'reflect-metadata';

export const PARAMS_METADATA = 'parameters';

/**
 * Get the Hono context object
 */
export function HttpContext() {
  return createParamDecorator('http_context');
}

/**
 * Get the request body as JSON
 * @param validator Optional validation function
 */
export function Body(validator?: (value: any) => any) {
  return createParamDecorator('body', undefined, validator);
}

/**
 * Get a path parameter
 * @param name Parameter name
 * @param validator Optional validation function
 */
export function Param(name?: string, validator?: (value: any) => any) {
  return createParamDecorator('param', name, validator);
}

/**
 * Get a query parameter
 * @param name Parameter name
 * @param validator Optional validation function
 */
export function Query(name?: string, validator?: (value: any) => any) {
  return createParamDecorator('query', name, validator);
}

/**
 * Get a header value
 * @param name Header name
 * @param validator Optional validation function
 */
export function Headers(name?: string, validator?: (value: any) => any) {
  return createParamDecorator('headers', name, validator);
}

/**
 * Get the next function
 */
export function Next() {
  return createParamDecorator('next');
}

export interface ParamMetadata {
  index: number;
  type:
    | 'http_context'
    | 'body'
    | 'param'
    | 'query'
    | 'headers'
    | 'next';
  name?: string;
  validator?: (value: any) => any;
}

/**
 * Parameter decorator factory
 */
function createParamDecorator(
  type: ParamMetadata['type'],
  name?: string,
  validator?: (value: any) => any
) {
  return function (
    target: any,
    propertyKey: string | symbol,
    parameterIndex: number
  ) {
    const parameters: ParamMetadata[] =
      Reflect.getMetadata(PARAMS_METADATA, target, propertyKey) || [];
    parameters.push({ index: parameterIndex, type, name, validator });
    Reflect.defineMetadata(PARAMS_METADATA, parameters, target, propertyKey);
  };
}
