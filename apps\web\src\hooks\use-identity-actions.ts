import { useCallback, useEffect, useRef, useState } from 'react'
import { toast } from 'sonner'
import { useNavigate } from '@tanstack/react-router'
import { useQueryClient } from '@tanstack/react-query'
import {
  useCurrentUserQuery,
  useLoginMutation,
  useLogoutAllMutation,
  useLogoutMutation,
  useRefreshTokenMutation,
  useVerifyTokenMutation,
} from './use-identity-query'
import type { LoginRequest } from '@/types/auth'
import {
  clearAccessToken,
  clearAuthData,
  getAccessToken,
  getDeviceInfo,
  getStoredUser,
  isTokenExpired,
  setAccessToken,
  storeUser,
} from '@/utils/auth'

// Import global refresh state
const getGlobalRefreshState = () => {
  try {
    // Try to import the global refresh state
    const { globalRefreshState } = require('@/providers/global-provider')
    return globalRefreshState
  } catch {
    // Fallback to local state if global provider not available
    return {
      isRefreshing: false,
      refreshPromise: null,
      isOnAuthPage: false,
    }
  }
}

// Local fallback flag to prevent multiple refresh attempts
let isRefreshingLocal = false

/**
 * Hook for handling identity actions and UI interactions
 */
export const useIdentityActions = () => {
  const navigate = useNavigate()
  const queryClient = useQueryClient()
  const refreshTimeoutRef = useRef<NodeJS.Timeout | undefined>(undefined)
  const scheduleTokenRefreshRef = useRef<((token: string) => void) | null>(null)

  // State to track initialization
  const [isInitializing, setIsInitializing] = useState(true)
  const [hasTriedRefresh, setHasTriedRefresh] = useState(false)

  // Get current access token
  const accessToken = getAccessToken()
  const isAuthenticated = !!accessToken && !isTokenExpired(accessToken)

  // Queries and mutations
  const currentUserQuery = useCurrentUserQuery({
    enabled: isAuthenticated,
    retry: false,
    refetchOnWindowFocus: false,
  })

  const loginMutation = useLoginMutation()
  const logoutMutation = useLogoutMutation()
  const logoutAllMutation = useLogoutAllMutation()
  const refreshTokenMutation = useRefreshTokenMutation()
  const verifyTokenMutation = useVerifyTokenMutation()

  // Refresh access token action
  const refreshAccessToken = useCallback(async () => {
    const globalState = getGlobalRefreshState()

    // Don't refresh on auth pages
    if (globalState.isOnAuthPage) {
      console.debug('Skipping token refresh on auth page')
      return null
    }

    // Check if global refresh is already in progress
    if (globalState.isRefreshing && globalState.refreshPromise) {
      console.debug('Global token refresh already in progress, waiting...')
      return globalState.refreshPromise
    }

    // Prevent multiple simultaneous local refresh attempts
    if (isRefreshingLocal) {
      console.debug('Local token refresh already in progress, skipping')
      return null
    }

    try {
      isRefreshingLocal = true
      console.debug('Starting token refresh...')

      const deviceInfo = getDeviceInfo()
      const response = await refreshTokenMutation.mutateAsync({ deviceInfo })

      // Debug: Log response structure
      console.debug('Refresh response:', response)

      // Validate response structure
      if (!response || !response.data?.access_token) {
        console.error('Invalid refresh response:', response)
        throw new Error('Invalid response from server - missing access token')
      }

      // Store new access token
      setAccessToken(response.data?.access_token)

      // Schedule next refresh (with validation)
      if (response.data?.access_token && scheduleTokenRefreshRef.current) {
        scheduleTokenRefreshRef.current(response.data?.access_token)
      }

      // Invalidate queries to refetch with new token
      queryClient.invalidateQueries({ queryKey: ['identity'] })

      console.debug('Token refreshed successfully')
      return response.data?.access_token
    } catch (error: any) {
      console.error('Token refresh failed:', error)

      // If refresh fails, logout user
      clearAuthData()
      queryClient.clear()

      // Clear refresh timeout
      if (refreshTimeoutRef.current) {
        clearTimeout(refreshTimeoutRef.current)
      }

      // Redirect to login only if not on auth page
      if (!globalState.isOnAuthPage) {
        navigate({ to: '/auth/login' })
        toast.error('Phiên đăng nhập đã hết hạn. Vui lòng đăng nhập lại.')
      }

      throw error
    } finally {
      isRefreshingLocal = false
    }
  }, [refreshTokenMutation, navigate, queryClient])

  // Auto refresh token before expiry
  const scheduleTokenRefresh = useCallback(
    (token: string) => {
      // Clear any existing timeout
      if (refreshTimeoutRef.current) {
        clearTimeout(refreshTimeoutRef.current)
      }

      // Validate token parameter
      if (!token || typeof token !== 'string') {
        console.error('Invalid token provided to scheduleTokenRefresh:', token)
        return
      }

      // Don't schedule if already refreshing
      const globalState = getGlobalRefreshState()
      if (isRefreshingLocal || globalState.isRefreshing) {
        return
      }

      try {
        // Parse token to get expiry time
        const tokenParts = token.split('.')
        if (tokenParts.length < 2 || !tokenParts[1]) {
          console.error(
            'Invalid token format - missing parts:',
            tokenParts.length,
          )
          return
        }

        const payload = JSON.parse(atob(tokenParts[1]))
        const expiryTime = payload.exp * 1000
        const currentTime = Date.now()

        // If token is already expired, don't schedule
        if (expiryTime <= currentTime) {
          console.warn('Token is already expired')
          return
        }

        // Calculate refresh time (2 minutes before expiry)
        const refreshTime = expiryTime - 2 * 60 * 1000
        const timeUntilRefresh = refreshTime - currentTime

        if (timeUntilRefresh <= 0) {
          // Token is expiring soon, but don't refresh immediately
          // Let axios interceptor handle it when needed
          console.debug(
            'Token expiring soon, will be refreshed by interceptor when needed',
          )
        } else {
          // Schedule refresh 2 minutes before expiry
          console.debug(
            `Scheduling token refresh in ${Math.round(timeUntilRefresh / 1000)} seconds`,
          )
          refreshTimeoutRef.current = setTimeout(() => {
            // Only refresh if not already refreshing and token is still valid
            const globalState = getGlobalRefreshState()
            if (
              !isRefreshingLocal &&
              !globalState.isRefreshing &&
              !isTokenExpired(getAccessToken() || '')
            ) {
              refreshAccessToken()
            }
          }, timeUntilRefresh)
        }
      } catch (error) {
        console.error('Error scheduling token refresh:', error)
      }
    },
    [refreshAccessToken],
  )

  // Update ref to current function
  useEffect(() => {
    scheduleTokenRefreshRef.current = scheduleTokenRefresh
  }, [scheduleTokenRefresh])

  // Try to refresh token on initialization if no access token but user was previously logged in
  const tryInitialRefresh = useCallback(async () => {
    const globalState = getGlobalRefreshState()
    if (hasTriedRefresh || isRefreshingLocal || globalState.isRefreshing || accessToken) {
      setIsInitializing(false)
      return
    }

    // Only try refresh if user was previously logged in (has stored user data)
    const storedUser = getStoredUser()
    if (!storedUser) {
      console.debug('No stored user data found, skipping initial token refresh')
      setIsInitializing(false)
      setHasTriedRefresh(true)
      return
    }

    try {
      setHasTriedRefresh(true)
      console.debug(
        'Attempting initial token refresh for stored user:',
        storedUser.username,
      )

      const deviceInfo = getDeviceInfo()
      const response = await refreshTokenMutation.mutateAsync({ deviceInfo })

      if (response.data?.access_token) {
        setAccessToken(response.data?.access_token)
        if (scheduleTokenRefreshRef.current) {
          scheduleTokenRefreshRef.current(response.data?.access_token)
        }
        queryClient.invalidateQueries({ queryKey: ['identity', 'me'] })
        console.debug('Initial token refresh successful')
      }
    } catch (error) {
      console.debug('Initial token refresh failed - user needs to login')
      // Don't show error toast for initial refresh failure
      clearAccessToken()
    } finally {
      setIsInitializing(false)
    }
  }, [hasTriedRefresh, accessToken, refreshTokenMutation, queryClient])

  // Login action
  const login = useCallback(
    async (credentials: LoginRequest) => {
      try {
        const deviceInfo = getDeviceInfo()
        const response = await loginMutation.mutateAsync({
          ...credentials,
          deviceInfo,
        })

        // Debug: Log response structure
        console.debug('Login response:', response)

        // Validate response structure
        if (!response || !response.data?.access_token) {
          console.error('Invalid login response:', response)
          throw new Error('Invalid response from server - missing access token')
        }

        // Store access token in memory/secure storage
        setAccessToken(response.data?.access_token)

        // Schedule token refresh (with validation)
        if (response.data?.access_token) {
          scheduleTokenRefresh(response.data?.access_token)
        }

        // Invalidate and refetch user data
        queryClient.invalidateQueries({ queryKey: ['identity', 'me'] })

        toast.success('Đăng nhập thành công!')

        // Navigate to dashboard
        navigate({ to: '/dashboard' })
      } catch (error: any) {
        const errorMessage =
          error?.response?.data?.error || 'Đăng nhập thất bại'
        toast.error(errorMessage)
        throw error
      }
    },
    [loginMutation, navigate, queryClient, scheduleTokenRefresh],
  )

  // Logout action
  const logout = useCallback(async () => {
    try {
      await logoutMutation.mutateAsync()
    } catch (error) {
      console.error('Logout error:', error)
    } finally {
      // Clear all auth data (access token + user data)
      clearAuthData()
      queryClient.clear()

      // Clear refresh timeout
      if (refreshTimeoutRef.current) {
        clearTimeout(refreshTimeoutRef.current)
      }

      // Navigate to login
      navigate({ to: '/auth/login' })
      toast.success('Đăng xuất thành công!')
    }
  }, [logoutMutation, navigate, queryClient])

  // Logout from all devices action
  const logoutAll = useCallback(async () => {
    try {
      const response = await logoutAllMutation.mutateAsync()
      toast.success(`Đã đăng xuất khỏi ${response.revoked_tokens} thiết bị`)
    } catch (error) {
      console.error('Logout all error:', error)
      toast.error('Có lỗi xảy ra khi đăng xuất khỏi tất cả thiết bị')
    } finally {
      // Clear all auth data (access token + user data)
      clearAuthData()
      queryClient.clear()

      // Clear refresh timeout
      if (refreshTimeoutRef.current) {
        clearTimeout(refreshTimeoutRef.current)
      }

      // Navigate to login
      navigate({ to: '/auth/login' })
    }
  }, [logoutAllMutation, navigate, queryClient])

  // Verify token action
  const verifyToken = useCallback(
    async (token?: string): Promise<boolean> => {
      try {
        const tokenToVerify = token || accessToken
        if (!tokenToVerify) return false

        const response = await verifyTokenMutation.mutateAsync({
          token: tokenToVerify,
        })

        return response.valid
      } catch (error) {
        console.error('Token verification error:', error)
        return false
      }
    },
    [verifyTokenMutation, accessToken],
  )

  // Initialize authentication on mount
  useEffect(() => {
    if (accessToken && !isTokenExpired(accessToken)) {
      // Token exists and valid, schedule refresh and mark as initialized
      scheduleTokenRefresh(accessToken)
      setIsInitializing(false)
    } else if (!hasTriedRefresh) {
      // No valid token, try to refresh from cookie
      tryInitialRefresh()
    } else {
      // Already tried refresh, mark as initialized
      setIsInitializing(false)
    }

    // Cleanup on unmount
    return () => {
      if (refreshTimeoutRef.current) {
        clearTimeout(refreshTimeoutRef.current)
      }
    }
  }, [accessToken, scheduleTokenRefresh, hasTriedRefresh, tryInitialRefresh])

  // Store user data when available
  useEffect(() => {
    if (currentUserQuery.data?.user) {
      storeUser(currentUserQuery.data.user)
    }
  }, [currentUserQuery.data?.user])

  // Handle user query errors - but don't auto-refresh here to avoid duplicate calls
  // The axios interceptor will handle 401 errors and refresh tokens automatically
  useEffect(() => {
    if (currentUserQuery.error) {
      const error = currentUserQuery.error as any
      if (error?.response?.status === 401) {
        console.debug(
          'User query failed with 401, axios interceptor will handle token refresh',
        )
        // Don't call refreshAccessToken here to avoid duplicate refresh attempts
        // The axios interceptor will handle this automatically
      }
    }
  }, [currentUserQuery.error])

  return {
    // State
    user: currentUserQuery.data?.user || null,
    isAuthenticated,
    isLoading:
      isInitializing ||
      currentUserQuery.isLoading ||
      loginMutation.isPending ||
      refreshTokenMutation.isPending,
    error: currentUserQuery.error || loginMutation.error,

    // Actions
    login,
    logout,
    logoutAll,
    refreshAccessToken,
    verifyToken,

    // Mutation states
    isLoggingIn: loginMutation.isPending,
    isLoggingOut: logoutMutation.isPending,
    isRefreshing: refreshTokenMutation.isPending,
  }
}

export default useIdentityActions
