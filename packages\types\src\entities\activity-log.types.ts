/**
 * Activity Log Attributes Interface
 * Defines the core data structure for activity logs
 */
export interface ActivityLogAttributes {
  id: string;
  user_id: string;
  action: string;
  resource_type: string;
  resource_id?: string;
  description: string;
  metadata?: Record<string, any>;
  ip_address?: string;
  user_agent?: string;
  created_by: string;
  created_at: Date;
}

/**
 * Activity Log Query Options Interface
 * Defines the options for querying activity logs
 */
export interface ActivityLogQueryOptions {
  limit?: number;
  skip?: number;
  sortBy?: string;
  sortDirection?: 'asc' | 'desc';
  action?: string;
  resourceType?: string;
  startDate?: Date;
  endDate?: Date;
}
