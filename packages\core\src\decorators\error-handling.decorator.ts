import 'reflect-metadata';

// Metadata keys
export const ERROR_HANDLER_METADATA = 'error:handlers';
export const ERROR_HANDLERS_METADATA = 'error:multiple_handlers';
export const CATCH_ALL_METADATA = 'error:catch_all';
export const HTTP_ERROR_HANDLER_METADATA = 'error:http_handlers';

// Interface for error handler metadata
export interface ErrorHandlerMetadata {
  errorType: Function;
  handlerName: string | symbol;
  options?: ErrorHandlerOptions;
}

// Interface for multiple error handlers metadata
export interface ErrorHandlersMetadata {
  errorTypes: Function[];
  handlerName: string | symbol;
  options?: ErrorHandlerOptions;
}

// Interface for catch-all error handler metadata
export interface CatchAllMetadata {
  handlerName: string | symbol;
  options?: CatchAllOptions;
}

// Interface for HTTP error handler metadata
export interface HttpErrorHandlerMetadata {
  statusCode?: number; // If undefined, handles all HTTP errors
  handlerName: string | symbol;
  options?: ErrorHandlerOptions;
}

// Options for error handlers
export interface ErrorHandlerOptions {
  priority?: number;
  logError?: boolean;
  rethrow?: boolean;
  environments?: string[];
  customMessage?: string;
}

// Options for catch-all handlers
export interface CatchAllOptions extends ErrorHandlerOptions {
  // Additional options specific to catch-all handlers
}

/**
 * Decorator to register a handler for a specific error type
 * @param errorType The error type to handle
 * @param options Optional configuration for the error handler
 */
export function ErrorHandler(errorType: Function, options?: ErrorHandlerOptions): MethodDecorator {
  return function (
    target: any,
    propertyKey: string | symbol,
    descriptor: PropertyDescriptor | void,
  ): PropertyDescriptor | void {
    // Get existing error handlers
    const errorHandlers: ErrorHandlerMetadata[] =
      Reflect.getMetadata(ERROR_HANDLER_METADATA, target.constructor) || [];

    // Add new handler
    errorHandlers.push({
      errorType,
      handlerName: propertyKey,
      options,
    });

    // Save metadata
    Reflect.defineMetadata(
      ERROR_HANDLER_METADATA,
      errorHandlers,
      target.constructor,
    );

    return descriptor as PropertyDescriptor;
  };
}

/**
 * Decorator to register a handler for multiple error types
 * @param errorTypes Array of error types to handle
 * @param options Optional configuration for the error handler
 */
export function ErrorHandlers(errorTypes: Function[], options?: ErrorHandlerOptions): MethodDecorator {
  return function (
    target: any,
    propertyKey: string | symbol,
    descriptor: PropertyDescriptor | void,
  ): PropertyDescriptor | void {
    // Get existing multiple error handlers
    const errorHandlers: ErrorHandlersMetadata[] =
      Reflect.getMetadata(ERROR_HANDLERS_METADATA, target.constructor) || [];

    // Add new handler
    errorHandlers.push({
      errorTypes,
      handlerName: propertyKey,
      options,
    });

    // Save metadata
    Reflect.defineMetadata(
      ERROR_HANDLERS_METADATA,
      errorHandlers,
      target.constructor,
    );

    return descriptor as PropertyDescriptor;
  };
}

/**
 * Decorator to register a catch-all error handler
 * This handler will catch any errors not handled by specific error handlers
 * @param options Optional configuration for the catch-all handler
 */
export function CatchAll(options?: CatchAllOptions): MethodDecorator {
  return function (
    target: any,
    propertyKey: string | symbol,
    descriptor: PropertyDescriptor | void,
  ): PropertyDescriptor | void {
    // Get existing catch-all handlers
    const catchAllHandlers: CatchAllMetadata[] =
      Reflect.getMetadata(CATCH_ALL_METADATA, target.constructor) || [];

    // Add new handler
    catchAllHandlers.push({
      handlerName: propertyKey,
      options,
    });

    // Save metadata
    Reflect.defineMetadata(
      CATCH_ALL_METADATA,
      catchAllHandlers,
      target.constructor,
    );

    return descriptor as PropertyDescriptor;
  };
}

/**
 * Decorator to register a handler for HTTP errors with specific status codes
 * @param statusCode Optional HTTP status code to handle (if not provided, handles all HTTP errors)
 * @param options Optional configuration for the HTTP error handler
 */
export function HttpErrorHandler(statusCode?: number, options?: ErrorHandlerOptions): MethodDecorator {
  return function (
    target: any,
    propertyKey: string | symbol,
    descriptor: PropertyDescriptor | void,
  ): PropertyDescriptor | void {
    // Get existing HTTP error handlers
    const httpErrorHandlers: HttpErrorHandlerMetadata[] =
      Reflect.getMetadata(HTTP_ERROR_HANDLER_METADATA, target.constructor) || [];

    // Add new handler
    httpErrorHandlers.push({
      statusCode,
      handlerName: propertyKey,
      options,
    });

    // Save metadata
    Reflect.defineMetadata(
      HTTP_ERROR_HANDLER_METADATA,
      httpErrorHandlers,
      target.constructor,
    );

    return descriptor as PropertyDescriptor;
  };
}
