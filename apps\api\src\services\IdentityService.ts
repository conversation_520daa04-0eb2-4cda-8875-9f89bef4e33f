import jwt from 'jsonwebtoken';
import crypto from 'crypto';
import { logger } from '@c-cam/logger';
import { Injectable, Inject } from '@c-cam/core';
import { DynamicPolicyLoader } from '@c-cam/core';
import { environment } from '@/configs/environment';
import { TokenDocument } from '@/database/entities/TokenModel';
import { UsersDocument } from '@/database/entities/UsersModel';
import TokenRepository from '@/repositories/TokenRepository';
import UsersService from './UsersService';
import MemberRoleService from './MemberRoleService';
import PolicyService from './PolicyService';
import { DeviceInfo } from '@c-cam/types';

/**
 * Interface for token payload
 */
interface TokenPayload {
  sub: string; // User ID (standard JWT claim)
  username: string;
  role: string;
  unit_id: string;
  member_role_id?: string;
  policies?: any[]; // Policy statements for PBAC
  permissions?: string[]; // User permissions
  roles?: string[]; // User roles
  tenant_id?: string; // Tenant ID for multi-tenancy support
  iat: number; // Issued at timestamp (standard JWT claim)
  exp: number; // Expiration timestamp (standard JWT claim)
}

/**
 * Service for handling identity-related operations
 */
@Injectable()
class IdentityService {
  constructor(
    @Inject(UsersService) private usersService: UsersService,
    @Inject(TokenRepository) private tokenRepository: TokenRepository,
    @Inject(MemberRoleService) private memberRoleService: MemberRoleService,
    @Inject(PolicyService) private policyService: PolicyService,
  ) {}

  /**
   * Authenticate a user with username and password
   * @param username The username
   * @param password The password
   * @returns The authenticated user or null if authentication fails
   */
  async authenticate(username: string, password: string): Promise<UsersDocument | null> {
    return this.usersService.authenticate(username, password);
  }

  /**
   * Generate a random token
   * @param length The length of the token
   * @returns A random token
   */
  private generateRandomToken(length = 40): string {
    return crypto.randomBytes(length).toString('hex');
  }

  /**
   * Generate a JWT token for a user
   * @param userId The user ID
   * @param username The username
   * @param role The user role
   * @param unitId The unit ID (tenant ID)
   * @param memberRoleId The member role ID
   * @param policies Optional policy statements to include in the token
   * @param permissions Optional permissions to include in the token
   * @param roles Optional roles to include in the token
   * @returns A JWT token
   */
  generateToken(
    userId: string,
    username: string,
    role: string,
    unitId: string,
    memberRoleId?: string,
    policies?: any[],
    permissions?: string[],
    roles?: string[],
  ): string {
    // Create token payload
    const payload: TokenPayload = {
      sub: userId, // Standard JWT claim for subject
      username,
      role,
      unit_id: unitId,
      tenant_id: unitId, // Add tenant_id for multi-tenancy support
      member_role_id: memberRoleId,
      iat: Math.floor(Date.now() / 1000),
      exp: Math.floor(Date.now() / 1000) + 3600 * 2, // 2 hours
    };

    // Add policies if provided
    if (policies && policies.length > 0) {
      payload.policies = policies;
    }

    // Add permissions if provided
    if (permissions && permissions.length > 0) {
      payload.permissions = permissions;
    }

    // Add roles if provided
    if (roles && roles.length > 0) {
      payload.roles = roles;
    }

    // Sign token with secret
    return jwt.sign(payload, environment.jwt.secret);
  }

  /**
   * Create access and refresh tokens for a user
   * @param userId The user ID
   * @param username The username
   * @param role The user role
   * @param unitId The unit ID (tenant ID)
   * @param memberRoleId The member role ID
   * @param deviceInfo Optional device information
   * @param ipAddress Optional IP address
   * @param userAgent Optional user agent
   * @param parentTokenId Optional parent token ID for rotation tracking
   * @returns The created token document
   */
  async createTokenPair(
    userId: string,
    username: string,
    role: string,
    unitId: string,
    memberRoleId?: string,
    deviceInfo?: DeviceInfo,
    ipAddress?: string,
    userAgent?: string,
    parentTokenId?: string,
  ): Promise<TokenDocument> {
    // Get user roles from the database
    const userRoles = await this.getUserRoles(userId);

    // Get user permissions
    const permissions = await this.getUserPermissions(userId);

    // Get policy statements for the user
    const policyStatements = await this.getUserPolicyStatements(userId, userRoles);

    // Generate access token with policies, permissions, and roles
    const accessToken = this.generateToken(
      userId,
      username,
      role,
      unitId,
      memberRoleId,
      policyStatements,
      permissions,
      userRoles,
    );

    // Generate refresh token
    const refreshToken = this.generateRandomToken();

    // Generate session ID if not rotating
    const sessionId = parentTokenId
      ? (await this.tokenRepository.findById(parentTokenId))?.session_id || crypto.randomUUID()
      : crypto.randomUUID();

    // Calculate expiration dates
    const accessExpiresAt = new Date();
    accessExpiresAt.setMinutes(accessExpiresAt.getMinutes() + 15); // 15 minutes for access token

    const refreshExpiresAt = new Date();
    refreshExpiresAt.setDate(refreshExpiresAt.getDate() + 7); // 7 days for refresh token

    // Get rotation count
    const rotationCount = parentTokenId
      ? ((await this.tokenRepository.findById(parentTokenId))?.rotation_count || 0) + 1
      : 0;

    // Create token document
    const tokenData = {
      user_id: userId,
      token: accessToken,
      refresh_token: refreshToken,
      token_type: 'Bearer',
      expires_at: accessExpiresAt,
      refresh_expires_at: refreshExpiresAt,
      revoked: false,
      created_at: new Date(),
      updated_at: new Date(),
      device_info: deviceInfo,
      ip_address: ipAddress,
      user_agent: userAgent,
      session_id: sessionId,
      rotation_count: rotationCount,
      parent_token_id: parentTokenId,
      is_compromised: false,
    };

    // Save token to database
    return this.tokenRepository.create(tokenData);
  }

  /**
   * Verify a JWT token
   * @param token The token to verify
   * @returns The decoded token payload or null if invalid
   */
  verifyToken(token: string): TokenPayload | null {
    try {
      const payload = jwt.verify(token, environment.jwt.secret) as TokenPayload;
      return payload;
    } catch (error) {
      logger.error('Token verification failed:', { error });
      return null;
    }
  }

  /**
   * Verify a token from the database
   * @param token The token to verify
   * @returns The token document or null if not found or revoked
   */
  async verifyTokenFromDb(token: string): Promise<TokenDocument | null> {
    // Find the token in the database
    const tokenDoc = await this.tokenRepository.findByToken(token);

    // Check if token exists and is not revoked
    if (!tokenDoc || tokenDoc.revoked) {
      return null;
    }

    // Check if token is expired
    if (tokenDoc.expires_at < new Date()) {
      // Revoke expired token
      await this.tokenRepository.revokeToken(token);
      return null;
    }

    return tokenDoc;
  }

  /**
   * Refresh a token using a refresh token with rotation
   * @param refreshToken The refresh token
   * @param deviceInfo Optional device information
   * @param ipAddress Optional IP address
   * @param userAgent Optional user agent
   * @returns The new token document or null if refresh token is invalid
   */
  async refreshToken(
    refreshToken: string,
    deviceInfo?: DeviceInfo,
    ipAddress?: string,
    userAgent?: string,
  ): Promise<TokenDocument | null> {
    // Check if refresh token is being reused (potential attack)
    const isReused = await this.tokenRepository.isRefreshTokenReused(refreshToken);
    if (isReused) {
      logger.warn('Refresh token reuse detected - potential attack', {
        refreshToken: refreshToken.substring(0, 10) + '...',
      });

      // Find the token and mark as compromised
      const compromisedToken = await this.tokenRepository.findOne({
        refresh_token: refreshToken,
      });
      if (compromisedToken) {
        await this.tokenRepository.markTokenAsCompromised(
          compromisedToken.id,
          compromisedToken.user_id,
        );
      }

      return null;
    }

    // Find the token by refresh token
    const tokenDoc = await this.tokenRepository.findByRefreshToken(refreshToken);

    // Check if token exists and is not revoked
    if (!tokenDoc || tokenDoc.revoked || tokenDoc.is_compromised) {
      return null;
    }

    // Check if refresh token is expired
    if (tokenDoc.refresh_expires_at < new Date()) {
      await this.tokenRepository.revokeRefreshToken(refreshToken);
      return null;
    }

    // Validate IP and User-Agent for security (optional but recommended)
    if (ipAddress && tokenDoc.ip_address && ipAddress !== tokenDoc.ip_address) {
      logger.warn('IP address mismatch during token refresh', {
        originalIp: tokenDoc.ip_address,
        newIp: ipAddress,
        userId: tokenDoc.user_id,
      });
      // You might want to require re-authentication here
    }

    // Get user ID from token
    const userId = tokenDoc.user_id;

    // Get user from database
    const user = await this.usersService.findById(userId);

    if (!user) {
      return null;
    }

    // Clear existing policies for the user in the policy registry
    const policyLoader = DynamicPolicyLoader.getInstance();
    policyLoader.clearPoliciesForUser(userId);

    // Revoke the old token (both access and refresh)
    await this.tokenRepository.revokeToken(tokenDoc.token);
    await this.tokenRepository.revokeRefreshToken(refreshToken);

    // Get user's role from MemberRoleService
    const userRoles = await this.getUserRoles(user.id);
    const primaryRole = userRoles.length > 0 && userRoles[0] ? userRoles[0] : 'user';

    // Create a new token pair with rotation tracking
    const newTokenDoc = await this.createTokenPair(
      user.id,
      user.username,
      primaryRole,
      user.unit_id,
      user.member_role_id,
      deviceInfo || tokenDoc.device_info,
      ipAddress || tokenDoc.ip_address,
      userAgent || tokenDoc.user_agent,
      tokenDoc.id, // Parent token ID for rotation tracking
    );

    logger.info('Token refreshed successfully', {
      userId: user.id,
      sessionId: tokenDoc.session_id,
      rotationCount: newTokenDoc.rotation_count,
    });

    return newTokenDoc;
  }

  /**
   * Revoke a token
   * @param token The token to revoke
   * @returns True if the token was revoked, false otherwise
   */
  async revokeToken(token: string): Promise<boolean> {
    return this.tokenRepository.revokeToken(token);
  }

  /**
   * Revoke a refresh token
   * @param refreshToken The refresh token to revoke
   * @returns True if the refresh token was revoked, false otherwise
   */
  async revokeRefreshToken(refreshToken: string): Promise<boolean> {
    return this.tokenRepository.revokeRefreshToken(refreshToken);
  }

  /**
   * Revoke all tokens for a user
   * @param userId The user ID
   * @returns The number of tokens revoked
   */
  async revokeAllUserTokens(userId: string): Promise<number> {
    return this.tokenRepository.revokeAllUserTokens(userId);
  }

  /**
   * Clean up expired tokens
   * @returns The number of tokens deleted
   */
  async cleanupExpiredTokens(): Promise<number> {
    return this.tokenRepository.cleanupExpiredTokens();
  }

  /**
   * Get a user by ID
   * @param userId The user ID
   * @returns The user or null if not found
   */
  async getUserById(userId: string): Promise<UsersDocument | null> {
    return this.usersService.findById(userId);
  }

  /**
   * Update a user's profile
   * @param userId The user ID
   * @param profileData The profile data to update
   * @returns True if the update was successful, false otherwise
   */
  async updateUserProfile(
    userId: string,
    profileData: Partial<{
      name: string;
      email: string;
      phone: string;
      dob: Date;
      gender: string;
    }>,
  ): Promise<boolean> {
    return this.usersService.updateUser(userId, profileData);
  }

  /**
   * Get user permissions from the database
   * @param userId The user ID
   * @returns An array of permission strings
   */
  async getUserPermissions(userId: string): Promise<string[]> {
    try {
      // Get user roles
      const roles = await this.getUserRoles(userId);
      if (!roles || roles.length === 0) {
        return [];
      }

      // Get permissions for each role from the PermissionModel
      // This would typically be done via a PermissionService
      const permissions: string[] = [];

      // For demonstration - you would replace this with actual queries to your PermissionModel
      for (const roleId of roles) {
        // Example: query permissions from the database based on role ID
        // const rolePermissions = await permissionService.findByRoleId(roleId);
        // permissions.push(...rolePermissions.map(p => `${p.module}:${p.action}:${p.feature}`));

        // For now, return some placeholder permissions based on role
        if (roleId === 'admin') {
          permissions.push('admin:access', 'users:manage', 'settings:manage');
        } else if (roleId === 'user') {
          permissions.push('users:read', 'profile:update');
        }
      }

      // Remove duplicates
      return [...new Set(permissions)];
    } catch (error) {
      logger.error('Error getting user permissions', { error, userId });
      return [];
    }
  }

  /**
   * Get user roles from the database
   * @param userId The user ID
   * @returns Array of role IDs
   */
  async getUserRoles(userId: string): Promise<string[]> {
    try {
      // Get member roles for the user
      const memberRoles = await this.memberRoleService.findByUserId(userId);

      if (!memberRoles || memberRoles.length === 0) {
        return ['user']; // Default role if no roles found
      }

      // Extract role IDs from member roles
      return memberRoles.map((mr) => mr.role_id);
    } catch (error) {
      logger.error('Error getting user roles', { error, userId });
      return ['user']; // Default role on error
    }
  }

  /**
   * Get policy statements for a user to include in JWT token
   * @param userId User ID
   * @param roles User roles
   * @returns Array of policy statements
   */
  async getUserPolicyStatements(userId: string, roles: string[]): Promise<any[]> {
    try {
      // Get user attributes (for policy conditions)
      const user = await this.usersService.findById(userId);
      if (!user) {
        return [];
      }

      // Create user attributes object for policy evaluation
      const attributes = {
        tenantId: user.unit_id,
        email: user.email,
        // Add other attributes that might be relevant for policy conditions
      };

      // Get policy statements from the policy service
      return await this.policyService.getPolicyStatementsForToken(userId, roles, attributes);
    } catch (error) {
      logger.error('Error getting policy statements', { error, userId });
      return [];
    }
  }

  /**
   * Get user with roles
   * @param userId The user ID
   * @returns The user with roles or null if not found
   */
  async getUserWithRoles(userId: string): Promise<{ id: string; roles: string[] } | null> {
    const user = await this.usersService.findById(userId);

    if (!user) {
      return null;
    }

    // Get roles from database
    const roles = await this.getUserRoles(userId);

    return {
      id: user.id,
      roles,
    };
  }
}

export default IdentityService;
