import { BaseModel, Inject, Injectable } from '@c-cam/core';
import { AuditTrailLogsDocument } from '@/database/entities/AuditTrailLogsModel';
import AuditTrailLogsRepository from '@/repositories/AuditTrailLogsRepository';

/**
 * Service for managing audit trail logs
 * Extends the BaseModel with AuditTrailLogsDocument type
 */
@Injectable()
class AuditTrailLogsService extends BaseModel<AuditTrailLogsDocument> {
  constructor(
    @Inject(AuditTrailLogsRepository)
    auditTrailLogsRepository: AuditTrailLogsRepository,
  ) {
    super(auditTrailLogsRepository);
  }

  /**
   * Log an audit trail event
   * @param userId The user ID
   * @param module The module name
   * @param action The action name
   * @param description The event description
   * @param createdBy The ID of the user creating the log
   * @returns The newly created audit log
   */
  async logEvent(
    userId: string,
    module: string,
    action: string,
    description: string,
    createdBy: string,
  ): Promise<AuditTrailLogsDocument> {
    return this.create({
      user_id: userId,
      module,
      action,
      description,
      created_by: createdBy,
    });
  }

  /**
   * Find audit logs by user ID
   * @param userId The user ID
   * @returns An array of audit logs
   */
  async findByUserId(userId: string): Promise<AuditTrailLogsDocument[]> {
    return (this.repository as AuditTrailLogsRepository).findByUserId(userId);
  }

  /**
   * Find audit logs by module
   * @param module The module name
   * @returns An array of audit logs
   */
  async findByModule(module: string): Promise<AuditTrailLogsDocument[]> {
    return (this.repository as AuditTrailLogsRepository).findByModule(module);
  }

  /**
   * Find audit logs by action
   * @param action The action name
   * @returns An array of audit logs
   */
  async findByAction(action: string): Promise<AuditTrailLogsDocument[]> {
    return (this.repository as AuditTrailLogsRepository).findByAction(action);
  }

  /**
   * Find audit logs by date range
   * @param startDate The start date of the range
   * @param endDate The end date of the range
   * @returns An array of audit logs
   */
  async findByDateRange(
    startDate: Date,
    endDate: Date,
  ): Promise<AuditTrailLogsDocument[]> {
    return (this.repository as AuditTrailLogsRepository).findByDateRange(
      startDate,
      endDate,
    );
  }

  /**
   * Find audit logs by user ID and date range
   * @param userId The user ID
   * @param startDate The start date of the range
   * @param endDate The end date of the range
   * @returns An array of audit logs
   */
  async findByUserIdAndDateRange(
    userId: string,
    startDate: Date,
    endDate: Date,
  ): Promise<AuditTrailLogsDocument[]> {
    return (
      this.repository as AuditTrailLogsRepository
    ).findByUserIdAndDateRange(userId, startDate, endDate);
  }

  /**
   * Find audit logs by module, action, and date range
   * @param module The module name
   * @param action The action name
   * @param startDate The start date of the range
   * @param endDate The end date of the range
   * @returns An array of audit logs
   */
  async findByModuleActionAndDateRange(
    module: string,
    action: string,
    startDate: Date,
    endDate: Date,
  ): Promise<AuditTrailLogsDocument[]> {
    return (
      this.repository as AuditTrailLogsRepository
    ).findByModuleActionAndDateRange(module, action, startDate, endDate);
  }

  /**
   * Generate an activity report for a user
   * @param userId The user ID
   * @param startDate The start date of the report
   * @param endDate The end date of the report
   * @returns A summary of user activity
   */
  async generateUserActivityReport(
    userId: string,
    startDate: Date,
    endDate: Date,
  ): Promise<{ module: string; actionCount: number }[]> {
    const logs = await (
      this.repository as AuditTrailLogsRepository
    ).findByUserIdAndDateRange(userId, startDate, endDate);

    // Group logs by module and count actions
    const moduleMap = new Map<string, number>();

    logs.forEach((log) => {
      const count = moduleMap.get(log.module) || 0;
      moduleMap.set(log.module, count + 1);
    });

    // Convert map to array of objects
    return Array.from(moduleMap.entries()).map(([module, actionCount]) => ({
      module,
      actionCount,
    }));
  }
}

export default AuditTrailLogsService;
