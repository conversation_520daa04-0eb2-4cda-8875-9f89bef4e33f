import { Document } from 'mongoose';
import { createSchema, createModel } from '@c-cam/core';
import { ShiftAttributes } from '@c-cam/types';

/**
 * Shift Document Interface
 * Extends the ShiftAttributes (excluding id) and Document
 */
export interface ShiftDocument extends Omit<ShiftAttributes, 'id'>, Document {}

/**
 * Shift Schema
 * Defines the MongoDB schema for shifts
 */
const ShiftSchema = createSchema({
  name: { type: String, required: true },
  shift_type: { type: String, required: true },
  work_coefficient: { type: Number, required: true, default: 1 },
});

// Add indexes
ShiftSchema.index({ name: 1 }, { unique: true });

// Create and export the model
const ShiftModel = createModel<ShiftDocument>('shift', ShiftSchema);

export default ShiftModel;
