export function Notification() {
  return (
    <div className="flex flex-col justify-start items-start flex-grow-0 flex-shrink-0 pr-[3.5px]">
      <div className="flex flex-col justify-start items-start flex-grow-0 flex-shrink-0">
        <div className="flex flex-col justify-center items-start self-stretch flex-grow-0 flex-shrink-0 relative px-[7px] pt-[1.3200000524520874px]">
          <svg
            width={19}
            height={19}
            viewBox="0 0 19 19"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            className="flex-grow-0 flex-shrink-0 w-[18px] h-[18px] relative"
            preserveAspectRatio="none"
          >
            <path
              d="M2.39744 11.7369C2.23795 12.7824 2.951 13.5081 3.82404 13.8698C7.17111 15.2563 11.8289 15.2563 15.176 13.8698C16.049 13.5081 16.7621 12.7824 16.6026 11.7369C16.5045 11.0943 16.0199 10.5593 15.6608 10.0369C15.1905 9.34412 15.1437 8.58854 15.1437 7.78467C15.1437 4.67807 12.6169 2.15967 9.5 2.15967C6.3831 2.15967 3.85634 4.67807 3.85634 7.78467C3.85628 8.58854 3.80954 9.34412 3.33921 10.0369C2.98013 10.5593 2.49546 11.0943 2.39744 11.7369Z"
              stroke="#1F2329"
              strokeWidth="1.5"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
            <path
              d="M6.5 14.9097C6.84387 16.2036 8.05665 17.1597 9.5 17.1597C10.9434 17.1597 12.1561 16.2036 12.5 14.9097"
              stroke="#1F2329"
              strokeWidth="1.5"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
          <div className="flex justify-center items-center flex-grow-0 flex-shrink-0 w-4 h-4 absolute left-[15.5px] top-[-5.34px] pl-[6.190000057220459px] pr-[6.199999809265137px] pt-[5.59499979019165px] pb-[5.755000114440918px] rounded-[700px] bg-[#ea5455]">
            <p className="flex-grow-0 flex-shrink-0 text-[10px] font-semibold text-center text-white">
              4
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}
