import { Hono } from 'hono';
import { serve } from '@hono/node-server';
import { Application, IApplication } from './application';
import { AppEnv } from './interfaces';
import { IServiceCollection, IServiceProvider } from '../di/type';
import { ServiceCollection } from '../di/container';
import { logger } from '@c-cam/logger';

/**
 * Application host interface
 */
export interface IApplicationHost {
  ConfigureServices(
    configureServices: (services: IServiceCollection) => void,
  ): IApplicationHost;
  Configure(configure: (app: IApplication) => void): IApplicationHost;
  Build(): Hono<AppEnv>;
  Run(port?: number): Promise<void>;
}

/**
 * Application host implementation
 */
export class ApplicationHost implements IApplicationHost {
  private readonly _services: IServiceCollection;
  private _serviceProvider: IServiceProvider | null = null;
  private _configureApp: ((app: IApplication) => void) | null = null;

  constructor() {
    this._services = new ServiceCollection();
  }

  /**
   * Configure services
   */
  public ConfigureServices(
    configureServices: (services: IServiceCollection) => void,
  ): IApplicationHost {
    configureServices(this._services);
    return this;
  }

  /**
   * Configure application
   */
  public Configure(configure: (app: IApplication) => void): IApplicationHost {
    this._configureApp = configure;
    return this;
  }

  /**
   * Build the application
   */
  public Build(): Hono<AppEnv> {
    if (!this._configureApp) {
      throw new Error('Application configuration is required');
    }

    // Build service provider
    this._serviceProvider = this._services.BuildServiceProvider();

    // Create application builder
    const appBuilder = new Application(this._serviceProvider);

    // Configure application
    this._configureApp(appBuilder);

    // Build application
    return appBuilder.build();
  }

  /**
   * Run the application
   */
  public async Run(port = 3000): Promise<void> {
    const app = this.Build();

    return new Promise<void>((resolve) => {
      const server = serve({
        fetch: app.fetch,
        port,
      });

      server.on('listening', () => {
        logger.info(`Server is running on port ${port}`);
        resolve();
      });
    });
  }
}
