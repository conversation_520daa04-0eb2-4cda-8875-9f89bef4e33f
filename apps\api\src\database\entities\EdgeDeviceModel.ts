import { Document } from 'mongoose';
import { createSchema, createModel } from '@c-cam/core';
import { EdgeDeviceAttributes } from '@c-cam/types';

/**
 * Edge Device Document Interface
 * Extends the EdgeDeviceAttributes (excluding id) and Document
 */
export interface EdgeDeviceDocument
  extends Omit<EdgeDeviceAttributes, 'id'>,
    Document {}

/**
 * Edge Device Schema
 * Defines the MongoDB schema for edge devices
 */
const EdgeDeviceSchema = createSchema({
  camera_id: {
    type: String,
    ref: 'camera',
    required: true,
  },
  name: { type: String, required: true },
  type: { type: String, required: true },
  ip_address: { type: String, required: true },
  mac_address: { type: String, required: true },
  firmware_version: { type: String, required: true },
  is_attendance_device: { type: Boolean, default: false },
  attendance_mode: { type: String, required: false },
  preferred_stream_mode: { type: String, required: false },
  status: { type: String, default: 'active' },
  created_by: { type: String, required: true },
});

// Add indexes
EdgeDeviceSchema.index({ ip_address: 1 }, { unique: true });
EdgeDeviceSchema.index({ mac_address: 1 }, { unique: true });

// Create and export the model
const EdgeDeviceModel = createModel<EdgeDeviceDocument>(
  'edge_device',
  EdgeDeviceSchema,
);

export default EdgeDeviceModel;
