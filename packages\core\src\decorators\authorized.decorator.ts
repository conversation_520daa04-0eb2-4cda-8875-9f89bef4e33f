import 'reflect-metadata';
import { logger } from '@c-cam/logger';
import { Context, NextFunction } from '../http/types.js';
import { createClaimsPrincipalFromJwt } from '../security/claims-principal.js';
import { UnauthorizedError } from '../http/http-error.js';
import { instances } from './injectable.decorator.js';
import { Container } from '../di/container.js';
import { PolicyRegistry } from '../security/policy.js';

// Metadata keys for authorized decorator
export const AUTHORIZED_METADATA = 'authorized';
export const POLICY_METADATA = 'policy';

// Interface for token verification service
export interface ITokenVerifier {
  verifyToken(token: string): any;
}

// Note: User and ClaimsPrincipal are stored in Hono context vars
// but don't need a specific type definition as they're accessed directly

/**
 * Authorization options interface
 */
export interface AuthorizationOptions {
  roles?: string[];
  policies?: string[];
  resource?: string;
  action?: string;
  required?: boolean;
}

/**
 * Decorator that requires authorization for controller methods or entire controllers
 * Extracts user information from Bear<PERSON> token and makes it available in the request
 *
 * @param options Optional configuration for the authorization
 * @returns A decorator function that can be applied to classes or methods
 */
export function Authorized(options: AuthorizationOptions = {}) {
  return function (
    target: any,
    propertyKey?: string | symbol,
    descriptor?: PropertyDescriptor,
  ) {
    // Store metadata about authorization requirement
    if (propertyKey && descriptor) {
      // Method decorator
      Reflect.defineMetadata(
        AUTHORIZED_METADATA,
        { ...options, required: true },
        target,
        propertyKey,
      );
    } else {
      // Class decorator
      Reflect.defineMetadata(
        AUTHORIZED_METADATA,
        { ...options, required: true },
        target,
      );
    }

    return descriptor || target;
  };
}

/**
 * Decorator that applies a policy for controller methods or entire controllers
 *
 * @param policyNames Names of policies to apply
 * @param resource Optional resource identifier
 * @param action Optional action being performed
 * @returns A decorator function that can be applied to classes or methods
 */
export function Policy(
  policyNames: string[],
  resource?: string,
  action?: string,
) {
  return function (
    target: any,
    propertyKey?: string | symbol,
    descriptor?: PropertyDescriptor,
  ) {
    const metadata = {
      policies: policyNames,
      resource,
      action,
      required: true,
    };

    // Store metadata about policy requirement
    if (propertyKey && descriptor) {
      // Method decorator
      Reflect.defineMetadata(POLICY_METADATA, metadata, target, propertyKey);
    } else {
      // Class decorator
      Reflect.defineMetadata(POLICY_METADATA, metadata, target);
    }

    return descriptor || target;
  };
}

/**
 * Middleware function that handles authorization
 * This is used internally by the framework to process the @Authorized decorator
 *
 * @param c Hono context object
 * @param next Hono next function
 * @param options Authorization options from decorator metadata
 */
export async function handleAuthorization(
  c: Context,
  next: NextFunction,
  options: AuthorizationOptions,
): Promise<void> {
  try {
    // Get authorization header
    const authHeader = c.req.header('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      if (options.required) {
        throw new UnauthorizedError('Missing or invalid authorization header');
      } else {
        return next(); // Skip if authorization is not required
      }
    }

    // Extract token
    const token = authHeader.split(' ')[1];
    if (!token) {
      if (options.required) {
        throw new UnauthorizedError('Missing token');
      } else {
        return next(); // Skip if authorization is not required
      }
    }

    // Find a service that can verify tokens
    // This assumes there's a service registered with a verifyToken method
    // The actual service name may vary based on your application
    const tokenVerifier = findTokenVerifier();

    if (!tokenVerifier) {
      throw new UnauthorizedError('Authentication service unavailable');
    }

    // Verify token
    let payload;
    try {
      payload = tokenVerifier.verifyToken(token);
    } catch (error) {
      // If token verification fails, throw an UnauthorizedError
      throw new UnauthorizedError('Invalid token');
    }

    // Create ClaimsPrincipal from token payload
    const claimsPrincipal = createClaimsPrincipalFromJwt(payload);

    // Attach ClaimsPrincipal to context vars
    c.set('claimsPrincipal', claimsPrincipal);

    // For backward compatibility, also attach user info to context vars
    c.set('user', {
      id: payload.sub,
      email: payload.email,
      // Add any other properties from the token payload
      ...payload,
    });

    // Variable to track overall authorization result
    let isAuthorized = true;
    let failureReason = '';

    // Check roles if specified
    if (options.roles && options.roles.length > 0) {
      // First check if the roles are in the token claims
      const hasRoleInClaims = options.roles.some((role) =>
        claimsPrincipal.isInRole(role),
      );

      if (hasRoleInClaims) {
        // User has required role in claims
        isAuthorized = true;
      } else {
        // If roles not in claims, check database as fallback
        const hasRequiredRole = await checkUserRoles(
          payload.sub,
          options.roles,
        );

        if (!hasRequiredRole) {
          isAuthorized = false;
          failureReason = `User does not have any of the required roles: ${options.roles.join(', ')}`;
        }
      }
    }

    // Check policies if specified
    if (isAuthorized && options.policies && options.policies.length > 0) {
      const policyRegistry = PolicyRegistry.getInstance();

      for (const policyName of options.policies) {
        const policy = policyRegistry.getPolicy(policyName);

        if (!policy) {
          logger.warn(`Policy '${policyName}' not found in registry`);
          continue;
        }

        const result = await policy.evaluate(
          c,
          options.resource,
          options.action,
        );

        if (!result.allowed) {
          isAuthorized = false;
          failureReason =
            result.reason || `Policy '${policyName}' denied access`;
          break;
        }
      }
    }

    if (!isAuthorized) {
      throw new UnauthorizedError(failureReason || 'Insufficient permissions');
    }

    await next();
  } catch (error) {
    throw error;
  }
}

/**
 * Helper function to find a service that can verify tokens
 * This is a simple implementation that looks for a service with a verifyToken method
 */
function findTokenVerifier(): ITokenVerifier | null {
  // Try to find a service with a verifyToken method
  // This is a simplified approach and may need to be adjusted based on your DI setup
  const services: any[] = [];

  // Collect all instantiated services
  for (const [_, metadata] of instances.entries()) {
    if (metadata.instance) {
      services.push(metadata.instance);
    }
  }

  // Find a service with verifyToken method
  for (const service of services) {
    if (typeof service.verifyToken === 'function') {
      return service as ITokenVerifier;
    }
  }

  // If no service is found, try to find one by a common name
  try {
    return Container.get('IdentityService') as ITokenVerifier;
  } catch (error) {
    // Silently handle the error if the service is not found
    // This is expected in some cases
  }

  return null;
}

/**
 * Helper function to check if a user has any of the required roles
 * This is a placeholder implementation that should be replaced with your actual role checking logic
 */
export async function checkUserRoles(
  userId: string,
  requiredRoles: string[],
): Promise<boolean> {
  try {
    // Try to find a service that can check user roles
    // This is a simplified approach and may need to be adjusted based on your application
    const identityService = Container.get('IdentityService') as any;

    if (
      !identityService ||
      typeof identityService.getUserWithRoles !== 'function'
    ) {
      // Log the error but don't expose it to the client
      logger.error(
        'Identity service not found or missing getUserWithRoles method',
      );
      return false;
    }

    const user = await identityService.getUserWithRoles(userId);

    if (!user || !user.roles) {
      return false;
    }

    // Check if user has any of the required roles
    return requiredRoles.some((role) => {
      if (!Array.isArray(user.roles)) {
        return false;
      }
      return user.roles.some((userRole: any) => {
        if (typeof userRole === 'string') {
          return userRole === role;
        } else if (userRole && typeof userRole === 'object') {
          return userRole.name === role;
        }
        return false;
      });
    });
  } catch (error) {
    // Log the error but don't expose it to the client
    logger.error('Error checking user roles:', { error });
    return false;
  }
}
