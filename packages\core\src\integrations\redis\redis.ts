import { Redis, RedisOptions, Cluster, ClusterOptions } from 'ioredis';
import { logger } from '@c-cam/logger';

/**
 * Redis connection health status
 */
export interface RedisHealthStatus {
  isConnected: boolean;
  status:
    | 'ready'
    | 'connecting'
    | 'reconnecting'
    | 'disconnecting'
    | 'disconnected'
    | 'error';
  mode: 'single' | 'cluster';
  uptime?: number;
  lastError?: string;
  connectionInfo?: {
    host?: string;
    port?: number;
    db?: number;
    nodes?: Array<{ host: string; port: number }>;
  };
}

/**
 * Redis connection options
 */
export interface RedisConnectionOptions {
  /**
   * Connection mode: 'single' for standalone Redis, 'cluster' for Redis Cluster
   */
  mode: 'single' | 'cluster';

  /**
   * Redis connection options for standalone mode
   */
  singleOptions?: RedisOptions & {
    /**
     * Redis connection URI (e.g., redis://user:password@localhost:6379/0)
     */
    uri?: string;
  };

  /**
   * Redis cluster connection options
   */
  clusterOptions?: ClusterOptions & {
    /**
     * List of cluster nodes
     */
    nodes: { host: string; port: number }[];
  };

  /**
   * Connection health check options
   */
  healthCheck?: {
    /**
     * Enable periodic health checks
     */
    enabled?: boolean;
    /**
     * Health check interval in milliseconds
     */
    interval?: number;
    /**
     * Timeout for health check operations in milliseconds
     */
    timeout?: number;
  };
}

/**
 * Redis connection manager
 */
export class RedisConnection {
  private static _instance: RedisConnection;
  private _defaultConnection: Redis | Cluster | null = null;
  private _options: RedisConnectionOptions | null = null;
  private _connectionStartTime: number | null = null;
  private _lastError: string | null = null;
  private _healthCheckInterval: NodeJS.Timeout | null = null;

  /**
   * Get the singleton instance
   */
  public static getInstance(): RedisConnection {
    if (!RedisConnection._instance) {
      RedisConnection._instance = new RedisConnection();
    }
    return RedisConnection._instance;
  }

  /**
   * Get the current connection options
   * @returns The connection options or null if not initialized
   */
  public getOptions(): RedisConnectionOptions | null {
    return this._options;
  }

  /**
   * Check if Redis is connected and ready
   * @returns True if connected and ready, false otherwise
   */
  public isConnected(): boolean {
    if (!this._defaultConnection) {
      return false;
    }

    if (this._defaultConnection instanceof Redis) {
      return this._defaultConnection.status === 'ready';
    } else {
      // For cluster, check if any node is ready
      return this._defaultConnection.status === 'ready';
    }
  }

  /**
   * Get detailed health status of the Redis connection
   * @returns Health status information
   */
  public getHealthStatus(): RedisHealthStatus {
    if (!this._defaultConnection || !this._options) {
      return {
        isConnected: false,
        status: 'disconnected',
        mode: 'single',
      };
    }

    const uptime = this._connectionStartTime
      ? Date.now() - this._connectionStartTime
      : undefined;

    const baseStatus: RedisHealthStatus = {
      isConnected: this.isConnected(),
      status: this._defaultConnection.status as any,
      mode: this._options.mode,
      uptime,
      lastError: this._lastError || undefined,
    };

    // Add connection info based on mode
    if (this._options.mode === 'single' && this._options.singleOptions) {
      baseStatus.connectionInfo = {
        host: this._options.singleOptions.host,
        port: this._options.singleOptions.port,
        db: this._options.singleOptions.db,
      };
    } else if (
      this._options.mode === 'cluster' &&
      this._options.clusterOptions
    ) {
      baseStatus.connectionInfo = {
        nodes: this._options.clusterOptions.nodes,
      };
    }

    return baseStatus;
  }

  /**
   * Initialize the Redis connection
   * @param options Connection options
   * @returns Promise resolving to the established connection
   * @throws Error if connection fails or if already initialized
   */
  public async initialize(
    options: RedisConnectionOptions,
  ): Promise<Redis | Cluster> {
    if (this._defaultConnection && this.isConnected()) {
      logger.warn('Redis connection already initialized and connected');
      return this._defaultConnection;
    }

    // Clean up any existing connection
    if (this._defaultConnection) {
      await this.close();
    }

    this._options = options;
    this._lastError = null;

    try {
      // Set default connection options for better performance and reliability
      const defaultSingleOptions: Partial<RedisOptions> = {
        maxRetriesPerRequest: 3,
        lazyConnect: false,
        keepAlive: 30000,
        connectTimeout: 10000,
        commandTimeout: 5000,
      };

      const defaultClusterOptions: Partial<ClusterOptions> = {
        retryDelayOnFailover: 100,
        lazyConnect: false,
        enableOfflineQueue: false,
        redisOptions: {
          maxRetriesPerRequest: 3,
          connectTimeout: 10000,
          commandTimeout: 5000,
        },
      };

      // Create the default connection
      if (options.mode === 'single') {
        if (!options.singleOptions) {
          throw new Error(
            'Single Redis options are required when mode is "single"',
          );
        }

        const mergedOptions = {
          ...defaultSingleOptions,
          ...options.singleOptions,
        };

        // Create a standalone Redis connection
        if (options.singleOptions.uri) {
          this._defaultConnection = new Redis(
            options.singleOptions.uri,
            mergedOptions,
          );
        } else {
          this._defaultConnection = new Redis(mergedOptions);
        }

        // Set up event listeners
        this._setupConnectionEventListeners(this._defaultConnection, 'default');
      } else if (options.mode === 'cluster') {
        if (!options.clusterOptions || !options.clusterOptions.nodes) {
          throw new Error('Cluster nodes are required when mode is "cluster"');
        }

        const mergedOptions = {
          ...defaultClusterOptions,
          ...options.clusterOptions,
        };

        // Create a Redis Cluster connection
        this._defaultConnection = new Cluster(
          options.clusterOptions.nodes,
          mergedOptions,
        );

        // Set up event listeners
        this._setupConnectionEventListeners(
          this._defaultConnection,
          'default-cluster',
        );
      } else {
        throw new Error(`Unsupported Redis mode: ${options.mode}`);
      }

      // Wait for connection to be ready
      await this._waitForConnection();

      this._connectionStartTime = Date.now();

      // Start health check if enabled
      this._startHealthCheck();

      logger.info('Redis successfully connected', {
        mode: options.mode,
        status: this._defaultConnection.status,
        connectionInfo: this._getConnectionInfo(),
      });

      return this._defaultConnection;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      this._lastError = errorMessage;

      logger.error('Failed to connect to Redis', {
        error: errorMessage,
        mode: options.mode,
        connectionInfo: this._getConnectionInfo(true), // Hide credentials
      });

      // Reset connection state on failure
      this._defaultConnection = null;
      this._options = null;
      this._connectionStartTime = null;

      throw error;
    }
  }

  /**
   * Wait for connection to be ready
   * @private
   */
  private async _waitForConnection(): Promise<void> {
    if (!this._defaultConnection) {
      throw new Error('No connection to wait for');
    }

    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error('Connection timeout'));
      }, 10000);

      const checkConnection = () => {
        if (this._defaultConnection?.status === 'ready') {
          clearTimeout(timeout);
          resolve();
        } else if (
          this._defaultConnection?.status === 'end' ||
          this._defaultConnection?.status === 'close'
        ) {
          clearTimeout(timeout);
          reject(new Error('Connection failed'));
        } else {
          setTimeout(checkConnection, 100);
        }
      };

      checkConnection();
    });
  }

  /**
   * Start health check monitoring
   * @private
   */
  private _startHealthCheck(): void {
    if (!this._options?.healthCheck?.enabled) {
      return;
    }

    const interval = this._options.healthCheck.interval || 30000; // 30 seconds default

    this._healthCheckInterval = setInterval(async () => {
      try {
        if (this._defaultConnection) {
          await this._defaultConnection.ping();
        }
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : String(error);
        this._lastError = errorMessage;
        logger.warn('Redis health check failed', { error: errorMessage });
      }
    }, interval);
  }

  /**
   * Get connection information for logging
   * @param hideCredentials Whether to hide sensitive information
   * @private
   */
  private _getConnectionInfo(
    hideCredentials: boolean = false,
  ): Record<string, any> {
    if (!this._options) {
      return {};
    }

    if (this._options.mode === 'single' && this._options.singleOptions) {
      const info: Record<string, any> = {
        host: this._options.singleOptions.host,
        port: this._options.singleOptions.port,
        db: this._options.singleOptions.db,
      };

      if (this._options.singleOptions.uri && !hideCredentials) {
        info.uri = this._options.singleOptions.uri;
      } else if (this._options.singleOptions.uri && hideCredentials) {
        // Mask credentials in URI
        info.uri = this._options.singleOptions.uri.replace(
          /\/\/([^:]+):([^@]+)@/,
          '//***:***@',
        );
      }

      return info;
    } else if (
      this._options.mode === 'cluster' &&
      this._options.clusterOptions
    ) {
      return {
        nodes: this._options.clusterOptions.nodes,
      };
    }

    return {};
  }

  /**
   * Set up event listeners for a connection
   * @param connection The connection to set up listeners for
   * @param connectionName The connection name
   */
  private _setupConnectionEventListeners(
    connection: Redis | Cluster,
    connectionName: string,
  ): void {
    // Remove any existing listeners to prevent duplicates
    connection.removeAllListeners('connect');
    connection.removeAllListeners('ready');
    connection.removeAllListeners('error');
    connection.removeAllListeners('close');
    connection.removeAllListeners('reconnecting');
    connection.removeAllListeners('end');

    connection.on('connect', () => {
      logger.info(`Redis connecting: ${connectionName}`, {
        status: connection.status,
        mode: this._options?.mode,
      });
    });

    connection.on('ready', () => {
      logger.info(`Redis ready: ${connectionName}`, {
        status: connection.status,
        mode: this._options?.mode,
        connectionInfo: this._getConnectionInfo(),
      });
    });

    connection.on('error', (err) => {
      const errorMessage = err instanceof Error ? err.message : String(err);
      this._lastError = errorMessage;

      logger.error(`Redis connection error for ${connectionName}`, {
        error: errorMessage,
        status: connection.status,
        mode: this._options?.mode,
      });
    });

    connection.on('close', () => {
      logger.warn(`Redis connection closed: ${connectionName}`, {
        status: connection.status,
        mode: this._options?.mode,
      });
    });

    connection.on('reconnecting', (delay: number) => {
      logger.info(`Redis reconnecting: ${connectionName}`, {
        delay,
        status: connection.status,
        mode: this._options?.mode,
      });
    });

    connection.on('end', () => {
      logger.warn(`Redis connection ended: ${connectionName}`, {
        status: connection.status,
        mode: this._options?.mode,
      });
    });

    // Cluster-specific events
    if (connection instanceof Cluster) {
      connection.on('node error', (err, node) => {
        logger.error(`Redis cluster node error: ${connectionName}`, {
          error: err instanceof Error ? err.message : String(err),
          node: `${node.options.host}:${node.options.port}`,
        });
      });

      connection.on('+node', (node) => {
        logger.info(`Redis cluster node added: ${connectionName}`, {
          node: `${node.options.host}:${node.options.port}`,
        });
      });

      connection.on('-node', (node) => {
        logger.info(`Redis cluster node removed: ${connectionName}`, {
          node: `${node.options.host}:${node.options.port}`,
        });
      });
    }
  }

  /**
   * Get the default Redis connection
   * @returns The active Redis connection
   * @throws Error if connection is not initialized or not ready
   */
  public getConnection(): Redis | Cluster {
    if (!this._defaultConnection) {
      throw new Error(
        'Redis connection not initialized. Call initialize() first.',
      );
    }

    if (!this.isConnected()) {
      throw new Error(
        `Redis connection is not ready. Current status: ${this._defaultConnection.status}`,
      );
    }

    return this._defaultConnection;
  }

  /**
   * Perform a health check on the Redis connection
   * @returns Promise resolving to true if healthy, false otherwise
   */
  public async healthCheck(): Promise<boolean> {
    try {
      if (!this._defaultConnection || !this.isConnected()) {
        return false;
      }

      await this._defaultConnection.ping();
      return true;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      this._lastError = errorMessage;
      logger.warn('Redis health check failed', { error: errorMessage });
      return false;
    }
  }

  /**
   * Close all Redis connections gracefully
   * @param force Whether to force close the connection
   */
  public async close(force: boolean = false): Promise<void> {
    try {
      // Stop health check monitoring
      if (this._healthCheckInterval) {
        clearInterval(this._healthCheckInterval);
        this._healthCheckInterval = null;
      }

      // Close the default connection
      if (this._defaultConnection) {
        const connectionInfo = this._getConnectionInfo();

        logger.info('Closing Redis connection', {
          force,
          status: this._defaultConnection.status,
          connectionInfo,
        });

        if (force) {
          this._defaultConnection.disconnect();
        } else {
          await this._defaultConnection.quit();
        }

        this._defaultConnection = null;
        this._options = null;
        this._connectionStartTime = null;
        this._lastError = null;

        logger.info('Successfully closed Redis connection');
      } else {
        logger.info('No active Redis connection to close');
      }
    } catch (error) {
      logger.error('Error while closing Redis connection', {
        error: error instanceof Error ? error.message : String(error),
      });
      throw error;
    }
  }
}
