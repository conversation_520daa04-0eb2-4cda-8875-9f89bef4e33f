import UnitModel, { UnitDocument } from '@/database/entities/UnitModel';
import { Repository, Injectable } from '@c-cam/core';

/**
 * Repository for managing organizational units
 * Extends the BaseRepository with UnitDocument type
 */
@Injectable()
class UnitRepository extends Repository<UnitDocument> {
  constructor() {
    super(UnitModel);
  }

  /**
   * Find units by organization ID
   * @param organizationId The organization ID to search for
   * @returns A promise that resolves to an array of units
   */
  async findByOrganizationId(organizationId: string): Promise<UnitDocument[]> {
    return this.find({ organization_id: organizationId });
  }

  /**
   * Find units by user ID
   * @param userId The user ID to search for
   * @returns A promise that resolves to an array of units
   */
  async findByUserId(userId: string): Promise<UnitDocument[]> {
    return this.find({ user_id: userId });
  }

  /**
   * Find units by parent unit ID
   * @param parentUnitId The parent unit ID to search for
   * @returns A promise that resolves to an array of units
   */
  async findByParentUnitId(parentUnitId: string): Promise<UnitDocument[]> {
    return this.find({ parent_unit_id: parentUnitId });
  }

  /**
   * Find a unit by name and organization ID
   * @param name The unit name to search for
   * @param organizationId The organization ID to search for
   * @returns A promise that resolves to a unit or null if not found
   */
  async findByNameAndOrganizationId(
    name: string,
    organizationId: string,
  ): Promise<UnitDocument | null> {
    return this.findOne({ name, organization_id: organizationId });
  }

  /**
   * Get all root units (units without a parent)
   * @returns A promise that resolves to an array of root units
   */
  async findRootUnits(): Promise<UnitDocument[]> {
    return this.find({ parent_unit_id: { $exists: false } });
  }
}

export default UnitRepository;
