import { BaseModel, Inject, Injectable } from '@c-cam/core';
import { RoleDocument } from '@/database/entities/RoleModel';
import RoleRepository from '@/repositories/RoleRepository';

/**
 * Service for managing roles
 * Extends the BaseModel with RoleDocument type
 */
@Injectable()
class RoleService extends BaseModel<RoleDocument> {
  /**
   * Create a new RoleService
   * @param tenantId Optional tenant ID for multi-tenant operations
   */
  constructor(
    @Inject(RoleRepository)
    repository: RoleRepository,
  ) {
    super(repository);
  }

  /**
   * Create a new role
   * @param name The role name
   * @param createdBy The ID of the user creating the role
   * @param memberRoleId Optional member role ID
   * @param permissionId Optional permission ID
   * @returns The newly created role
   */
  async createRole(
    name: string,
    createdBy: string,
    memberRoleId?: string,
    permissionId?: string,
  ): Promise<RoleDocument> {
    // Check if a role with the same name already exists
    const existingRole = await (this.repository as RoleRepository).findByName(
      name,
    );

    if (existingRole) {
      throw new Error(`Role with name '${name}' already exists`);
    }

    // Create the new role
    return this.create({
      name,
      created_by: createdBy,
      member_role_id: memberRoleId,
      permission_id: permissionId,
    });
  }

  /**
   * Update a role
   * @param id The role ID
   * @param data The data to update
   * @returns True if the role was updated, false otherwise
   */
  async updateRole(
    id: string,
    data: {
      name?: string;
      memberRoleId?: string;
      permissionId?: string;
    },
  ): Promise<boolean> {
    // Check if the role exists
    const role = await this.findById(id);

    if (!role) {
      throw new Error(`Role with ID '${id}' not found`);
    }

    // If name is being updated, check for duplicates
    if (data.name && data.name !== role.name) {
      const existingRole = await (this.repository as RoleRepository).findByName(
        data.name,
      );

      if (existingRole && existingRole.id !== id) {
        throw new Error(`Role with name '${data.name}' already exists`);
      }
    }

    // Update the role
    return this.update(id, {
      name: data.name,
      member_role_id: data.memberRoleId,
      permission_id: data.permissionId,
    });
  }

  /**
   * Find a role by name
   * @param name The role name
   * @returns The role or null if not found
   */
  async findByName(name: string): Promise<RoleDocument | null> {
    return (this.repository as RoleRepository).findByName(name);
  }

  /**
   * Find roles by member role ID
   * @param memberRoleId The member role ID
   * @returns An array of roles
   */
  async findByMemberRoleId(memberRoleId: string): Promise<RoleDocument[]> {
    return (this.repository as RoleRepository).findByMemberRoleId(memberRoleId);
  }

  /**
   * Find roles by permission ID
   * @param permissionId The permission ID
   * @returns An array of roles
   */
  async findByPermissionId(permissionId: string): Promise<RoleDocument[]> {
    return (this.repository as RoleRepository).findByPermissionId(permissionId);
  }

  /**
   * Find roles by creator
   * @param createdBy The creator ID
   * @returns An array of roles
   */
  async findByCreatedBy(createdBy: string): Promise<RoleDocument[]> {
    return (this.repository as RoleRepository).findByCreatedBy(createdBy);
  }
}

export default RoleService;
