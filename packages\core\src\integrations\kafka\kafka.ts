import {
  Kafka,
  KafkaConfig,
  Consumer,
  ConsumerConfig,
  Producer,
  ProducerConfig,
  Admin,
  AdminConfig,
  logLevel,
} from 'kafkajs';
import { logger } from '@c-cam/logger';

/**
 * Kafka connection health status
 */
export interface KafkaHealthStatus {
  isConnected: boolean;
  status: 'connected' | 'connecting' | 'disconnected' | 'error';
  uptime?: number;
  lastError?: string;
  components: {
    producer: {
      connected: boolean;
      error?: string;
    };
    consumer?: {
      connected: boolean;
      error?: string;
      subscribedTopics: string[];
    };
    admin: {
      connected: boolean;
      error?: string;
    };
  };
  multiTenant?: {
    enabled: boolean;
    tenants: Array<{
      id: string;
      producer: { connected: boolean; error?: string };
      consumer?: {
        connected: boolean;
        error?: string;
        subscribedTopics: string[];
      };
    }>;
  };
}

/**
 * Kafka connection options
 */
export interface KafkaConnectionOptions {
  /**
   * KafkaJS client configuration
   */
  client: KafkaConfig;

  /**
   * Consumer configuration
   */
  consumer?: ConsumerConfig;

  /**
   * Producer configuration
   */
  producer?: ProducerConfig;

  /**
   * Admin configuration
   */
  admin?: AdminConfig;

  /**
   * Health check configuration
   */
  healthCheck?: {
    /**
     * Enable periodic health checks
     */
    enabled?: boolean;
    /**
     * Health check interval in milliseconds
     */
    interval?: number;
    /**
     * Timeout for health check operations in milliseconds
     */
    timeout?: number;
  };

  /**
   * Error handling configuration
   */
  errorHandling?: {
    /**
     * Whether to throw errors or log and continue
     */
    throwOnError?: boolean;
    /**
     * Maximum retry attempts for operations
     */
    maxRetries?: number;
    /**
     * Retry delay in milliseconds
     */
    retryDelay?: number;
  };
}

/**
 * Kafka message handler function
 */
export type KafkaMessageHandler = (
  topic: string,
  partition: number,
  message: {
    key: string | null;
    value: Buffer | null;
    headers: Record<string, string>;
    timestamp: string;
    offset: string;
  },
) => Promise<void>;

/**
 * Kafka connection manager
 */
export class KafkaConnection {
  private static _instance: KafkaConnection;
  private _client: Kafka | null = null;
  private _producer: Producer | null = null;
  private _consumer: Consumer | null = null;
  private _admin: Admin | null = null;
  private _options: KafkaConnectionOptions | null = null;
  private _messageHandlers: Map<string, Set<KafkaMessageHandler>> = new Map();
  private _subscribedTopics: Set<string> = new Set();
  private _connectionStartTime: number | null = null;
  private _lastError: string | null = null;
  private _healthCheckInterval: NodeJS.Timeout | null = null;
  private _componentErrors: Map<string, string> = new Map();

  private constructor() {}

  /**
   * Get the singleton instance
   */
  public static getInstance(): KafkaConnection {
    if (!KafkaConnection._instance) {
      KafkaConnection._instance = new KafkaConnection();
    }
    return KafkaConnection._instance;
  }

  /**
   * Get the current connection options
   * @returns The connection options or null if not initialized
   */
  public getOptions(): KafkaConnectionOptions | null {
    return this._options;
  }

  /**
   * Check if Kafka is connected and ready
   * @returns True if connected and ready, false otherwise
   */
  public isConnected(): boolean {
    return !!(this._client && this._producer && this._admin);
  }

  /**
   * Get detailed health status of the Kafka connection
   * @returns Health status information
   */
  public getHealthStatus(): KafkaHealthStatus {
    if (!this._client || !this._options) {
      return {
        isConnected: false,
        status: 'disconnected',
        components: {
          producer: { connected: false },
          admin: { connected: false },
        },
      };
    }

    const uptime = this._connectionStartTime
      ? Date.now() - this._connectionStartTime
      : undefined;

    const baseStatus: KafkaHealthStatus = {
      isConnected: this.isConnected(),
      status: this.isConnected() ? 'connected' : 'error',
      uptime,
      lastError: this._lastError || undefined,
      components: {
        producer: {
          connected: !!this._producer,
          error: this._componentErrors.get('producer'),
        },
        admin: {
          connected: !!this._admin,
          error: this._componentErrors.get('admin'),
        },
      },
    };

    // Add consumer info if available
    if (this._consumer) {
      baseStatus.components.consumer = {
        connected: !!this._consumer,
        error: this._componentErrors.get('consumer'),
        subscribedTopics: Array.from(this._subscribedTopics),
      };
    }

    return baseStatus;
  }

  /**
   * Perform a health check on the Kafka connection
   * @returns Promise resolving to true if healthy, false otherwise
   */
  public async healthCheck(): Promise<boolean> {
    try {
      if (!this._admin) {
        return false;
      }

      // Try to list topics as a health check
      await this._admin.listTopics();
      return true;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      this._lastError = errorMessage;
      this._componentErrors.set('admin', errorMessage);
      logger.warn('Kafka health check failed', { error: errorMessage });
      return false;
    }
  }

  /**
   * Initialize the Kafka connection(s)
   * @param options Connection options
   * @returns Promise resolving when initialization is complete
   * @throws Error if initialization fails
   */
  public async initialize(options: KafkaConnectionOptions): Promise<void> {
    if (this._client && this.isConnected()) {
      logger.warn('Kafka connection already initialized and connected');
      return;
    }

    // Clean up any existing connection
    if (this._client) {
      await this.close();
    }

    this._options = {
      errorHandling: {
        throwOnError: false,
        maxRetries: 3,
        retryDelay: 1000,
      },
      healthCheck: {
        enabled: false,
        interval: 30000,
        timeout: 5000,
      },
      ...options,
    };

    this._lastError = null;
    this._componentErrors.clear();

    try {
      // Configure KafkaJS logger to use our logger
      const kafkaLogCreator = () => {
        return ({ namespace, level, label, log }: any) => {
          const logMethod = this._getLogMethod(level);
          logMethod(`[${namespace}] ${label}`, log);
        };
      };

      // Create the Kafka client with enhanced configuration
      this._client = new Kafka({
        retry: {
          initialRetryTime: 100,
          retries: this._options.errorHandling?.maxRetries || 3,
        },
        ...options.client,
        logCreator: kafkaLogCreator,
      });

      // Create the producer with error handling
      try {
        this._producer = this._client.producer(options.producer);
        await this._producer.connect();
        logger.info('Kafka producer connected');
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : String(error);
        this._componentErrors.set('producer', errorMessage);
        throw new Error(`Failed to connect Kafka producer: ${errorMessage}`);
      }

      // Create the consumer if consumer config is provided
      if (options.consumer) {
        try {
          this._consumer = this._client.consumer(options.consumer);
          await this._consumer.connect();
          logger.info(
            `Kafka consumer connected with group ID: ${options.consumer.groupId}`,
          );

          // Set up message handler for the consumer
          await this._setupConsumerMessageHandler(this._consumer);
        } catch (error) {
          const errorMessage =
            error instanceof Error ? error.message : String(error);
          this._componentErrors.set('consumer', errorMessage);
          throw new Error(`Failed to connect Kafka consumer: ${errorMessage}`);
        }
      }

      // Create the admin client with error handling
      try {
        this._admin = this._client.admin(options.admin);
        await this._admin.connect();
        logger.info('Kafka admin client connected');
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : String(error);
        this._componentErrors.set('admin', errorMessage);
        throw new Error(
          `Failed to connect Kafka admin client: ${errorMessage}`,
        );
      }

      logger.info('Kafka connected');

      // Set connection start time
      this._connectionStartTime = Date.now();

      // Start health check monitoring if enabled
      this._startHealthCheck();

      logger.info('Kafka successfully initialized', {
        hasConsumer: !!this._consumer,
        healthCheckEnabled: this._options.healthCheck?.enabled || false,
      });
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      this._lastError = errorMessage;

      logger.error('Failed to connect to Kafka', {
        error: errorMessage,
        brokers: this._maskBrokers(options.client.brokers),
      });

      // Reset connection state on failure
      this._client = null;
      this._producer = null;
      this._consumer = null;
      this._admin = null;
      this._options = null;
      this._connectionStartTime = null;

      throw error;
    }
  }

  /**
   * Start health check monitoring
   * @private
   */
  private _startHealthCheck(): void {
    if (!this._options?.healthCheck?.enabled) {
      return;
    }

    const interval = this._options.healthCheck.interval || 30000; // 30 seconds default

    this._healthCheckInterval = setInterval(async () => {
      try {
        await this.healthCheck();
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : String(error);
        this._lastError = errorMessage;
        logger.warn('Kafka health check failed', { error: errorMessage });
      }
    }, interval);
  }

  /**
   * Mask sensitive information in broker URLs
   * @param brokers Broker configuration
   * @private
   */
  private _maskBrokers(brokers: any): string[] {
    try {
      let brokerList: string[] = [];

      if (Array.isArray(brokers)) {
        brokerList = brokers;
      } else if (typeof brokers === 'function') {
        const result = brokers();
        if (Array.isArray(result)) {
          brokerList = result;
        } else if (result && typeof result.then === 'function') {
          // It's a promise, return placeholder
          return ['***:***'];
        }
      }

      return brokerList.map((broker) => {
        // Mask any credentials in broker URLs
        return broker.replace(/\/\/([^:]+):([^@]+)@/, '//***:***@');
      });
    } catch (error) {
      return ['***:***'];
    }
  }

  /**
   * Get the appropriate log method based on KafkaJS log level
   * @param level KafkaJS log level
   * @returns The corresponding log method
   */
  private _getLogMethod(level: number): (message: string, meta?: any) => void {
    switch (level) {
      case logLevel.ERROR:
      case logLevel.NOTHING:
        return logger.error.bind(logger);
      case logLevel.WARN:
        return logger.warn.bind(logger);
      case logLevel.INFO:
        return logger.info.bind(logger);
      case logLevel.DEBUG:
      default:
        return logger.debug.bind(logger);
    }
  }

  /**
   * Set up message handler for the consumer
   * @param consumer The consumer to set up the handler for
   */
  private async _setupConsumerMessageHandler(
    consumer: Consumer,
  ): Promise<void> {
    await consumer.run({
      eachMessage: async ({ topic, partition, message }) => {
        try {
          // Convert message to a more usable format
          const formattedMessage = {
            key: message.key ? message.key.toString() : null,
            value: message.value,
            headers: this._parseMessageHeaders(message.headers),
            timestamp: message.timestamp,
            offset: message.offset,
          };

          // Get handlers for this topic
          const handlers = this._messageHandlers.get(topic);
          if (handlers) {
            for (const handler of handlers) {
              try {
                await handler(topic, partition, formattedMessage);
              } catch (error) {
                logger.error('Error in Kafka message handler', {
                  topic,
                  partition,
                  error,
                });
              }
            }
          }

          // Also check for wildcard handlers
          for (const [
            handlerTopic,
            handlers,
          ] of this._messageHandlers.entries()) {
            if (
              handlerTopic !== topic &&
              this._topicMatchesWildcard(topic, handlerTopic)
            ) {
              for (const handler of handlers) {
                try {
                  await handler(topic, partition, formattedMessage);
                } catch (error) {
                  logger.error('Error in Kafka wildcard message handler', {
                    topic,
                    handlerTopic,
                    partition,
                    error,
                  });
                }
              }
            }
          }
        } catch (error) {
          logger.error('Error processing Kafka message', {
            topic,
            partition,
            error,
          });
        }
      },
    });
  }

  /**
   * Parse message headers from KafkaJS format
   * @param headers KafkaJS message headers
   * @returns Parsed headers as a record
   */
  private _parseMessageHeaders(headers: any): Record<string, string> {
    if (!headers) {
      return {};
    }

    const result: Record<string, string> = {};
    for (const [key, value] of Object.entries(headers)) {
      if (value instanceof Buffer) {
        result[key] = value.toString();
      } else if (typeof value === 'string') {
        result[key] = value;
      } else if (Array.isArray(value)) {
        // Handle array of values (take the first one if available)
        const firstValue = value[0];
        if (firstValue instanceof Buffer) {
          result[key] = firstValue.toString();
        } else if (typeof firstValue === 'string') {
          result[key] = firstValue;
        }
      }
    }
    return result;
  }

  /**
   * Check if a topic matches a wildcard pattern
   * @param topic The topic to check
   * @param wildcardPattern The wildcard pattern
   * @returns True if the topic matches the wildcard pattern
   */
  private _topicMatchesWildcard(
    topic: string,
    wildcardPattern: string,
  ): boolean {
    // Convert Kafka wildcards to regex
    // In Kafka, we typically use * for wildcards
    const regex = new RegExp(
      '^' +
        wildcardPattern.replace(/\*/g, '.*') + // * matches any sequence of characters
        '$',
    );
    return regex.test(topic);
  }

  /**
   * Get the Kafka client
   */
  public getClient(): Kafka {
    if (!this._client) {
      throw new Error('Kafka client not initialized. Call initialize() first.');
    }
    return this._client;
  }

  /**
   * Get the Kafka producer
   */
  public getProducer(): Producer {
    if (!this._producer) {
      throw new Error(
        'Kafka producer not initialized. Call initialize() first.',
      );
    }
    return this._producer;
  }

  /**
   * Get the Kafka consumer
   */
  public getConsumer(): Consumer {
    if (!this._consumer) {
      throw new Error(
        'Kafka consumer not initialized. Make sure consumer config was provided.',
      );
    }
    return this._consumer;
  }

  /**
   * Get the Kafka admin client
   */
  public getAdmin(): Admin {
    if (!this._admin) {
      throw new Error(
        'Kafka admin client not initialized. Call initialize() first.',
      );
    }
    return this._admin;
  }

  /**
   * Subscribe to a topic
   * @param topic The topic to subscribe to
   * @param fromBeginning Whether to read from the beginning of the topic
   */
  public async subscribe(topic: string, fromBeginning = false): Promise<void> {
    const consumer = this.getConsumer();

    // Check if already subscribed
    if (this._subscribedTopics.has(topic)) {
      logger.debug(`Already subscribed to Kafka topic: ${topic}`);
      return;
    }

    try {
      await consumer.subscribe({ topic, fromBeginning });
      this._subscribedTopics.add(topic);
      logger.info(`Subscribed to Kafka topic: ${topic}`);
    } catch (error) {
      logger.error('Error subscribing to Kafka topic', { topic, error });
      throw error;
    }
  }

  /**
   * Register a message handler for a topic
   * @param topic The topic to handle messages for
   * @param handler The message handler function
   */
  public onMessage(topic: string, handler: KafkaMessageHandler): void {
    let handlers = this._messageHandlers.get(topic);
    if (!handlers) {
      handlers = new Set();
      this._messageHandlers.set(topic, handlers);
    }
    handlers.add(handler);
    logger.debug(`Registered Kafka message handler for topic: ${topic}`);
  }

  /**
   * Remove a message handler for a topic
   * @param topic The topic to remove the handler for
   * @param handler The message handler function to remove
   */
  public offMessage(topic: string, handler: KafkaMessageHandler): void {
    const handlers = this._messageHandlers.get(topic);
    if (handlers) {
      handlers.delete(handler);
      if (handlers.size === 0) {
        this._messageHandlers.delete(topic);
      }
      logger.debug(`Removed Kafka message handler for topic: ${topic}`);
    }
  }

  /**
   * Produce a message to a topic
   * @param topic The topic to produce to
   * @param message The message to produce
   * @param key Optional message key
   * @param headers Optional message headers
   */
  public async produce(
    topic: string,
    message: string | Buffer | object,
    key?: string,
    headers?: Record<string, string>,
  ): Promise<void> {
    const producer = this.getProducer();

    // Convert message to Buffer if it's an object
    const messageValue =
      typeof message === 'object' && !(message instanceof Buffer)
        ? Buffer.from(JSON.stringify(message))
        : typeof message === 'string'
          ? Buffer.from(message)
          : message;

    try {
      await producer.send({
        topic,
        messages: [
          {
            key: key ? Buffer.from(key) : null,
            value: messageValue,
            headers: headers
              ? this._convertHeadersToBuffers(headers)
              : undefined,
          },
        ],
      });
      logger.debug(`Produced Kafka message to topic: ${topic}`);
    } catch (error) {
      logger.error('Error producing Kafka message', { topic, error });
      throw error;
    }
  }

  /**
   * Convert headers from string values to Buffer values
   * @param headers Headers with string values
   * @returns Headers with Buffer values
   */
  private _convertHeadersToBuffers(
    headers: Record<string, string>,
  ): Record<string, Buffer> {
    const result: Record<string, Buffer> = {};
    for (const [key, value] of Object.entries(headers)) {
      result[key] = Buffer.from(value);
    }
    return result;
  }

  /**
   * Create a topic
   * @param topic The topic to create
   * @param numPartitions The number of partitions
   * @param replicationFactor The replication factor
   */
  public async createTopic(
    topic: string,
    numPartitions = 1,
    replicationFactor = 1,
  ): Promise<void> {
    const admin = this.getAdmin();

    try {
      await admin.createTopics({
        topics: [
          {
            topic,
            numPartitions,
            replicationFactor,
          },
        ],
      });
      logger.info(`Created Kafka topic: ${topic}`);
    } catch (error) {
      logger.error('Error creating Kafka topic', { topic, error });
      throw error;
    }
  }

  /**
   * List all topics
   * @returns List of topics
   */
  public async listTopics(): Promise<string[]> {
    const admin = this.getAdmin();

    try {
      const topics = await admin.listTopics();
      logger.debug('Listed Kafka topics', { count: topics.length });
      return topics;
    } catch (error) {
      logger.error('Error listing Kafka topics', { error });
      throw error;
    }
  }

  /**
   * Close all Kafka connections gracefully
   * @param force Whether to force close connections
   */
  public async close(force: boolean = false): Promise<void> {
    try {
      // Stop health check monitoring
      if (this._healthCheckInterval) {
        clearInterval(this._healthCheckInterval);
        this._healthCheckInterval = null;
      }

      const closePromises: Promise<void>[] = [];

      // Close the default consumer
      if (this._consumer) {
        closePromises.push(
          (async () => {
            try {
              if (force) {
                this._consumer!.disconnect();
              } else {
                await this._consumer!.disconnect();
              }
              logger.info('Closed default Kafka consumer');
            } catch (error) {
              const errorMessage =
                error instanceof Error ? error.message : String(error);
              logger.error('Error closing default Kafka consumer', {
                error: errorMessage,
              });
            }
          })(),
        );
      }

      // Close the default producer
      if (this._producer) {
        closePromises.push(
          (async () => {
            try {
              if (force) {
                this._producer!.disconnect();
              } else {
                await this._producer!.disconnect();
              }
              logger.info('Closed default Kafka producer');
            } catch (error) {
              const errorMessage =
                error instanceof Error ? error.message : String(error);
              logger.error('Error closing default Kafka producer', {
                error: errorMessage,
              });
            }
          })(),
        );
      }

      // Close the admin client
      if (this._admin) {
        closePromises.push(
          (async () => {
            try {
              if (force) {
                this._admin!.disconnect();
              } else {
                await this._admin!.disconnect();
              }
              logger.info('Closed Kafka admin client');
            } catch (error) {
              const errorMessage =
                error instanceof Error ? error.message : String(error);
              logger.error('Error closing Kafka admin client', {
                error: errorMessage,
              });
            }
          })(),
        );
      }

      // Wait for all close operations to complete (or timeout)
      if (!force) {
        await Promise.allSettled(closePromises);
      }

      // Clear all state
      this._messageHandlers.clear();
      this._subscribedTopics.clear();
      this._componentErrors.clear();

      this._consumer = null;
      this._producer = null;
      this._admin = null;
      this._client = null;
      this._options = null;
      this._connectionStartTime = null;
      this._lastError = null;

      logger.info('All Kafka connections closed successfully');
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      logger.error('Error while closing Kafka connections', {
        error: errorMessage,
      });
      throw error;
    }
  }
}
