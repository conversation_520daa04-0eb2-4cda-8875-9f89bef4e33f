import { Context } from 'hono';
import {
  Controller,
  ControllerBase,
  HttpContext,
  HttpGet,
  HttpPost,
  HttpPut,
  HttpDelete,
  Inject,
  ValidationError,
  NotFoundError,
  UnauthorizedError,
  Param,
  Body,
  Query,
} from '@c-cam/core';
import UsersService from '../services/UsersService';

@Controller('/api/users')
export class UsersController extends ControllerBase {
  constructor(
    @Inject(UsersService) private usersService: UsersService,
  ) {
    super();
  }

  /**
   * Helper method to get authenticated user ID
   */
  private getAuthenticatedUserId(c: Context): string {
    const userId = c.get('userId');
    if (!userId) {
      throw new UnauthorizedError('You must be authenticated to access this resource');
    }
    return userId;
  }

  /**
   * Get all users
   */
  @HttpGet('/')
  async getUsers(
    @HttpContext() c: Context,
    @Query('limit') limit?: string,
    @Query('skip') skip?: string,
    @Query('sortBy') sortBy?: string,
    @Query('sortDirection') sortDirection?: string,
  ): Promise<Response> {
    this.getAuthenticatedUserId(c);

    const users = await this.usersService.find({
      limit: limit ? parseInt(limit) : undefined,
      skip: skip ? parseInt(skip) : undefined,
      sortBy,
      sortDirection,
    });

    return this.success(c, { users });
  }

  /**
   * Find users by unit ID
   */
  @HttpGet('/unit/:unitId')
  async getUsersByUnitId(
    @HttpContext() c: Context,
    @Param('unitId') unitId: string,
  ): Promise<Response> {
    this.getAuthenticatedUserId(c);
    this.validateIf(!unitId, 'Unit ID is required');

    const users = await this.usersService.findByUnitId(unitId);
    return this.success(c, { users });
  }

  /**
   * Find users by member role ID
   */
  @HttpGet('/role/:memberRoleId')
  async getUsersByMemberRoleId(
    @HttpContext() c: Context,
    @Param('memberRoleId') memberRoleId: string,
  ): Promise<Response> {
    this.getAuthenticatedUserId(c);
    this.validateIf(!memberRoleId, 'Member role ID is required');

    const users = await this.usersService.findByMemberRoleId(memberRoleId);
    return this.success(c, { users });
  }

  /**
   * Authenticate a user
   */
  @HttpPost('/authenticate')
  async authenticate(
    @HttpContext() c: Context,
    @Body() credentials: { username: string; password: string },
  ): Promise<Response> {
    // Validate required fields
    this.validateRequiredFields(credentials, ['username', 'password']);

    const user = await this.usersService.authenticate(
      credentials.username,
      credentials.password,
    );

    this.unauthorizedIf(!user, 'Invalid credentials');

    return this.success(c, { user });
  }

  /**
   * Get a user by ID
   */
  @HttpGet('/:id')
  async getUserById(
    @HttpContext() c: Context,
    @Param('id') id: string,
  ): Promise<Response> {
    this.getAuthenticatedUserId(c);
    this.validateIf(!id, 'User ID is required');

    const user = await this.usersService.findById(id);
    this.notFoundIf(!user, 'User not found');

    return this.success(c, { user });
  }

  /**
   * Create a new user
   */
  @HttpPost('/')
  async createUser(
    @HttpContext() c: Context,
    @Body() userData: any,
  ): Promise<Response> {
    const userId = this.getAuthenticatedUserId(c);

    // Validate required fields
    this.validateRequiredFields(userData, ['username', 'password', 'name']);

    // Sanitize the data
    const sanitizedData = this.sanitizeData(userData);

    // Add the creator ID
    sanitizedData.created_by = userId;

    const user = await this.usersService.createUser(sanitizedData);
    return this.created(c, { user }, 'User created successfully');
  }

  /**
   * Update a user
   */
  @HttpPut('/:id')
  async updateUser(
    @HttpContext() c: Context,
    @Param('id') id: string,
    @Body() userData: any,
  ): Promise<Response> {
    this.getAuthenticatedUserId(c);
    this.validateIf(!id, 'User ID is required');

    // Sanitize the data
    const sanitizedData = this.sanitizeData(userData);

    const success = await this.usersService.updateUser(id, sanitizedData);
    this.validateIf(!success, 'Failed to update user');

    return this.success(c, { success: true }, 'User updated successfully');
  }

  /**
   * Delete a user
   */
  @HttpDelete('/:id')
  async deleteUser(
    @HttpContext() c: Context,
    @Param('id') id: string,
  ): Promise<Response> {
    this.getAuthenticatedUserId(c);
    this.validateIf(!id, 'User ID is required');

    const success = await this.usersService.delete(id);
    this.validateIf(!success, 'Failed to delete user');

    return this.success(c, { success: true }, 'User deleted successfully');
  }
}
