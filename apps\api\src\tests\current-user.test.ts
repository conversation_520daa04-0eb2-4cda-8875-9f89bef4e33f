/**
 * Test file to verify getCurrentUser functionality
 * This test demonstrates the enhanced getCurrentUser method that parses tokens from cookies
 */

import { Hono } from 'hono';
import { 
  getSignedCookie, 
  setSignedCookie 
} from 'hono/cookie';
import { ControllerBase } from '@c-cam/core';

// Test secret
const TEST_SECRET = 'test-secret-for-current-user-123';

// Mock user data
const MOCK_USER = {
  id: 'user123',
  username: 'testuser',
  name: 'Test User',
  email: '<EMAIL>',
  role: 'user',
  unit_id: 'unit123',
  member_role_id: 'role123',
};

// Mock JWT payload
const MOCK_JWT_PAYLOAD = {
  sub: MOCK_USER.id,
  username: MOCK_USER.username,
  role: MOCK_USER.role,
  unit_id: MOCK_USER.unit_id,
  member_role_id: MOCK_USER.member_role_id,
  roles: ['user'],
  permissions: ['read:profile'],
  iat: Math.floor(Date.now() / 1000),
  exp: Math.floor(Date.now() / 1000) + 3600, // 1 hour
};

// Test controller that extends ControllerBase
class TestController extends ControllerBase {
  protected getJwtSecret(): string | null {
    return TEST_SECRET;
  }

  protected async verifyAndDecodeToken(token: string): Promise<any | null> {
    // Mock JWT verification - in real app this would use jsonwebtoken
    if (token === 'valid-token') {
      return MOCK_JWT_PAYLOAD;
    }
    return null;
  }

  protected async validateUserFromToken(payload: any): Promise<any | null> {
    // Mock user validation - in real app this would query database
    if (payload.sub === MOCK_USER.id) {
      return MOCK_USER;
    }
    return null;
  }

  // Public method to test getCurrentUser
  async testGetCurrentUser(req: any) {
    return await this.getCurrentUser(req);
  }
}

// Create test Hono app
const testApp = new Hono();
const testController = new TestController();

// Test route to set access token in signed cookie
testApp.post('/test/set-access-token', async (c) => {
  const { token } = await c.req.json();
  
  await setSignedCookie(c, 'access_token', token, TEST_SECRET, {
    httpOnly: true,
    secure: true,
    sameSite: 'Strict',
    path: '/',
    maxAge: 3600, // 1 hour
  });
  
  return c.json({ success: true, message: 'Access token set in signed cookie' });
});

// Test route to get current user from Authorization header
testApp.get('/test/current-user-header', async (c) => {
  try {
    const currentUser = await testController.testGetCurrentUser(c.req);
    
    if (!currentUser) {
      return c.json({ 
        success: false, 
        message: 'No current user found' 
      }, 401);
    }
    
    return c.json({ 
      success: true, 
      user: currentUser,
      source: 'authorization_header'
    });
  } catch (error) {
    return c.json({ 
      success: false, 
      error: error.message 
    }, 500);
  }
});

// Test route to get current user from signed cookie
testApp.get('/test/current-user-cookie', async (c) => {
  try {
    const currentUser = await testController.testGetCurrentUser(c);
    
    if (!currentUser) {
      return c.json({ 
        success: false, 
        message: 'No current user found' 
      }, 401);
    }
    
    return c.json({ 
      success: true, 
      user: currentUser,
      source: 'signed_cookie'
    });
  } catch (error) {
    return c.json({ 
      success: false, 
      error: error.message 
    }, 500);
  }
});

// Test route to demonstrate user validation
testApp.get('/test/user-validation', async (c) => {
  const authHeader = c.req.header('Authorization');
  
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return c.json({ 
      success: false, 
      message: 'Authorization header required' 
    }, 401);
  }
  
  const token = authHeader.substring(7);
  
  // Test token verification
  const payload = await testController.verifyAndDecodeToken(token);
  if (!payload) {
    return c.json({ 
      success: false, 
      message: 'Invalid token' 
    }, 401);
  }
  
  // Test user validation
  const user = await testController.validateUserFromToken(payload);
  if (!user) {
    return c.json({ 
      success: false, 
      message: 'User not found or invalid' 
    }, 404);
  }
  
  return c.json({
    success: true,
    message: 'User validation successful',
    payload,
    user
  });
});

export { testApp, testController, MOCK_USER, MOCK_JWT_PAYLOAD, TEST_SECRET };

/**
 * Usage examples:
 * 
 * 1. Set access token in signed cookie:
 *    POST /test/set-access-token
 *    Body: { "token": "valid-token" }
 * 
 * 2. Get current user from Authorization header:
 *    GET /test/current-user-header
 *    Headers: { "Authorization": "Bearer valid-token" }
 * 
 * 3. Get current user from signed cookie:
 *    GET /test/current-user-cookie
 *    (Requires access_token cookie to be set first)
 * 
 * 4. Test user validation:
 *    GET /test/user-validation
 *    Headers: { "Authorization": "Bearer valid-token" }
 */
