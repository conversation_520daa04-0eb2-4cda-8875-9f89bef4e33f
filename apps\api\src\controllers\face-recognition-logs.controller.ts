import { Context } from 'hono';
import {
  Controller,
  ControllerBase,
  HttpContext,
  HttpGet,
  HttpPost,
  HttpDelete,
  Inject,
} from '@c-cam/core';
import FaceRecognitionLogsService from '../services/FaceRecognitionLogsService';

@Controller('/api/face-recognition-logs')
export class FaceRecognitionLogsController extends ControllerBase {
  constructor(
    @Inject(FaceRecognitionLogsService) private faceRecognitionLogsService: FaceRecognitionLogsService,
  ) {
    super();
  }

  /**
   * Get all face recognition logs
   */
  @HttpGet('/')
  async getFaceRecognitionLogs(@HttpContext() c: Context): Promise<Response> {
    try {
      const userId = c.get('userId');
      if (!userId) {
        return c.json({ error: 'Unauthorized' }, 401);
      }

      const { limit, skip, sortBy, sortDirection } = c.req.query();

      const logs = await this.faceRecognitionLogsService.find({
        limit: limit ? parseInt(limit) : undefined,
        skip: skip ? parseInt(skip) : undefined,
        sortBy,
        sortDirection,
      });

      return c.json({ logs });
    } catch (error: any) {
      return c.json({ error: error.message }, 500);
    }
  }

  /**
   * Get a face recognition log by ID
   */
  @HttpGet('/:id')
  async getFaceRecognitionLogById(@HttpContext() c: Context): Promise<Response> {
    try {
      const userId = c.get('userId');
      if (!userId) {
        return c.json({ error: 'Unauthorized' }, 401);
      }

      const { id } = c.req.param();
      if (!id) {
        return c.json({ error: 'Log ID is required' }, 400);
      }
      const log = await this.faceRecognitionLogsService.findById(id);

      if (!log) {
        return c.json({ error: 'Face recognition log not found' }, 404);
      }

      return c.json({ log });
    } catch (error: any) {
      return c.json({ error: error.message }, 500);
    }
  }

  /**
   * Log a face recognition event
   */
  @HttpPost('/')
  async logRecognitionEvent(@HttpContext() c: Context): Promise<Response> {
    try {
      const userId = c.get('userId');
      if (!userId) {
        return c.json({ error: 'Unauthorized' }, 401);
      }

      const logData = await c.req.json();

      // Add the creator ID
      logData.created_by = userId;

      // Validate required fields
      const requiredFields = ['camera_id', 'edge_device_id', 'device_id', 'status'];
      for (const field of requiredFields) {
        if (!logData[field]) {
          return c.json({ error: `${field} is required` }, 400);
        }
      }

      const log = await this.faceRecognitionLogsService.logRecognitionEvent(logData);
      return c.json({ log }, 201);
    } catch (error: any) {
      return c.json({ error: error.message }, 500);
    }
  }

  /**
   * Delete a face recognition log
   */
  @HttpDelete('/:id')
  async deleteFaceRecognitionLog(@HttpContext() c: Context): Promise<Response> {
    try {
      const userId = c.get('userId');
      if (!userId) {
        return c.json({ error: 'Unauthorized' }, 401);
      }

      const { id } = c.req.param();
      if (!id) {
        return c.json({ error: 'Log ID is required' }, 400);
      }
      const success = await this.faceRecognitionLogsService.delete(id);

      if (!success) {
        return c.json({ error: 'Failed to delete face recognition log' }, 400);
      }

      return c.json({ success: true });
    } catch (error: any) {
      return c.json({ error: error.message }, 500);
    }
  }

  /**
   * Find face recognition logs by user ID
   */
  @HttpGet('/user/:userId')
  async getLogsByUserId(@HttpContext() c: Context): Promise<Response> {
    try {
      const authUserId = c.get('userId');
      if (!authUserId) {
        return c.json({ error: 'Unauthorized' }, 401);
      }

      const { userId } = c.req.param();
      if (!userId) {
        return c.json({ error: 'User ID is required' }, 400);
      }
      const logs = await this.faceRecognitionLogsService.findByUserId(userId);

      return c.json({ logs });
    } catch (error: any) {
      return c.json({ error: error.message }, 500);
    }
  }

  /**
   * Find face recognition logs by camera ID
   */
  @HttpGet('/camera/:cameraId')
  async getLogsByCameraId(@HttpContext() c: Context): Promise<Response> {
    try {
      const userId = c.get('userId');
      if (!userId) {
        return c.json({ error: 'Unauthorized' }, 401);
      }

      const { cameraId } = c.req.param();
      if (!cameraId) {
        return c.json({ error: 'Camera ID is required' }, 400);
      }
      const logs = await this.faceRecognitionLogsService.findByCameraId(cameraId);

      return c.json({ logs });
    } catch (error: any) {
      return c.json({ error: error.message }, 500);
    }
  }

  /**
   * Find face recognition logs by edge device ID
   */
  @HttpGet('/edge-device/:edgeDeviceId')
  async getLogsByEdgeDeviceId(@HttpContext() c: Context): Promise<Response> {
    try {
      const userId = c.get('userId');
      if (!userId) {
        return c.json({ error: 'Unauthorized' }, 401);
      }

      const { edgeDeviceId } = c.req.param();
      if (!edgeDeviceId) {
        return c.json({ error: 'Edge device ID is required' }, 400);
      }
      const logs = await this.faceRecognitionLogsService.findByEdgeDeviceId(edgeDeviceId);

      return c.json({ logs });
    } catch (error: any) {
      return c.json({ error: error.message }, 500);
    }
  }

  /**
   * Find face recognition logs by status
   */
  @HttpGet('/status/:status')
  async getLogsByStatus(@HttpContext() c: Context): Promise<Response> {
    try {
      const userId = c.get('userId');
      if (!userId) {
        return c.json({ error: 'Unauthorized' }, 401);
      }

      const { status } = c.req.param();
      if (!status) {
        return c.json({ error: 'Status is required' }, 400);
      }
      const logs = await this.faceRecognitionLogsService.findByStatus(status);

      return c.json({ logs });
    } catch (error: any) {
      return c.json({ error: error.message }, 500);
    }
  }

  /**
   * Find face recognition logs by similarity percent range
   */
  @HttpGet('/similarity/:minSimilarity/:maxSimilarity')
  async getLogsBySimilarityRange(@HttpContext() c: Context): Promise<Response> {
    try {
      const userId = c.get('userId');
      if (!userId) {
        return c.json({ error: 'Unauthorized' }, 401);
      }

      const { minSimilarity, maxSimilarity } = c.req.param();
      if (!minSimilarity || !maxSimilarity) {
        return c.json({ error: 'Min and max similarity values are required' }, 400);
      }
      const minValue = parseFloat(minSimilarity);
      const maxValue = parseFloat(maxSimilarity);

      if (isNaN(minValue) || isNaN(maxValue)) {
        return c.json({ error: 'Invalid similarity values' }, 400);
      }

      const logs = await this.faceRecognitionLogsService.findBySimilarityRange(minValue, maxValue);

      return c.json({ logs });
    } catch (error: any) {
      return c.json({ error: error.message }, 500);
    }
  }

  /**
   * Find face recognition logs by date range
   */
  @HttpGet('/date-range/:startDate/:endDate')
  async getLogsByDateRange(@HttpContext() c: Context): Promise<Response> {
    try {
      const userId = c.get('userId');
      if (!userId) {
        return c.json({ error: 'Unauthorized' }, 401);
      }

      const { startDate, endDate } = c.req.param();
      if (!startDate || !endDate) {
        return c.json({ error: 'Start date and end date are required' }, 400);
      }
      const start = new Date(startDate);
      const end = new Date(endDate);

      if (isNaN(start.getTime()) || isNaN(end.getTime())) {
        return c.json({ error: 'Invalid date format' }, 400);
      }

      const logs = await this.faceRecognitionLogsService.findByDateRange(start, end);

      return c.json({ logs });
    } catch (error: any) {
      return c.json({ error: error.message }, 500);
    }
  }

  /**
   * Find face recognition logs by user ID and date range
   */
  @HttpGet('/user/:userId/date-range/:startDate/:endDate')
  async getLogsByUserIdAndDateRange(@HttpContext() c: Context): Promise<Response> {
    try {
      const authUserId = c.get('userId');
      if (!authUserId) {
        return c.json({ error: 'Unauthorized' }, 401);
      }

      const { userId, startDate, endDate } = c.req.param();
      if (!userId || !startDate || !endDate) {
        return c.json({ error: 'User ID, start date, and end date are required' }, 400);
      }
      const start = new Date(startDate);
      const end = new Date(endDate);

      if (isNaN(start.getTime()) || isNaN(end.getTime())) {
        return c.json({ error: 'Invalid date format' }, 400);
      }

      const logs = await this.faceRecognitionLogsService.findByUserIdAndDateRange(userId, start, end);

      return c.json({ logs });
    } catch (error: any) {
      return c.json({ error: error.message }, 500);
    }
  }

  /**
   * Generate a recognition summary for a user
   */
  @HttpGet('/user/:userId/summary/:startDate/:endDate')
  async generateUserRecognitionSummary(@HttpContext() c: Context): Promise<Response> {
    try {
      const authUserId = c.get('userId');
      if (!authUserId) {
        return c.json({ error: 'Unauthorized' }, 401);
      }

      const { userId, startDate, endDate } = c.req.param();
      if (!userId || !startDate || !endDate) {
        return c.json({ error: 'User ID, start date, and end date are required' }, 400);
      }
      const start = new Date(startDate);
      const end = new Date(endDate);

      if (isNaN(start.getTime()) || isNaN(end.getTime())) {
        return c.json({ error: 'Invalid date format' }, 400);
      }

      const summary = await this.faceRecognitionLogsService.generateUserRecognitionSummary(userId, start, end);

      return c.json({ summary });
    } catch (error: any) {
      return c.json({ error: error.message }, 500);
    }
  }
}
