import { Document } from 'mongoose';
import { createSchema, createModel } from '@c-cam/core';
import { TenantAttributes } from '@c-cam/types';

/**
 * Tenant Document Interface
 * Extends the TenantAttributes (excluding id) and Document
 */
export interface TenantDocument
  extends Omit<TenantAttributes, 'id'>,
    Document {}

/**
 * Tenant Schema
 * Defines the MongoDB schema for tenants
 */
const TenantSchema = createSchema({
  name: { type: String, required: true },
  address: { type: String, required: false },
  unit_id: {
    type: String,
    ref: 'unit',
    required: false,
  },
  created_by: { type: String, required: true },
});

// Add indexes
TenantSchema.index({ name: 1 }, { unique: true });

// Create and export the model
const TenantModel = createModel<TenantDocument>('tenant', TenantSchema);

export default TenantModel;
