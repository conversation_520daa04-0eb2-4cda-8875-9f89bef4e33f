{"$schema": "https://json.schemastore.org/tsconfig", "compilerOptions": {"declaration": true, "declarationMap": true, "esModuleInterop": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "incremental": false, "isolatedModules": false, "lib": ["es2022", "DOM", "DOM.Iterable"], "module": "ES2022", "moduleDetection": "force", "moduleResolution": "bundler", "noUncheckedIndexedAccess": true, "resolveJsonModule": true, "skipLibCheck": true, "strict": true, "target": "ES2022"}}