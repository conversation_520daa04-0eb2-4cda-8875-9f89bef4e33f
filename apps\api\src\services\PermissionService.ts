import { BaseModel, Inject, Injectable } from '@c-cam/core';
import { PermissionDocument } from '@/database/entities/PermissionModel';
import PermissionRepository from '@/repositories/PermissionRepository';

/**
 * Service for managing permissions
 * Extends the BaseModel with PermissionDocument type
 */
@Injectable()
class PermissionService extends BaseModel<PermissionDocument> {
  /**
   * Create a new PermissionService
   * @param tenantId Optional tenant ID for multi-tenant operations
   */
  constructor(
    @Inject(PermissionRepository)
    repository: PermissionRepository,
  ) {
    super(repository);
  }

  /**
   * Create a new permission
   * @param roleId The role ID
   * @param module The module name
   * @param feature The feature name
   * @param action The action name
   * @param createdBy The ID of the user creating the permission
   * @returns The newly created permission
   */
  async createPermission(
    roleId: string,
    module: string,
    feature: string,
    action: string,
    createdBy: string,
  ): Promise<PermissionDocument> {
    // Check if the permission already exists
    const existingPermissions = await (
      this.repository as PermissionRepository
    ).findByModuleFeatureAction(module, feature, action);

    const duplicatePermission = existingPermissions.find(
      (p) => p.role_id === roleId,
    );
    if (duplicatePermission) {
      throw new Error(
        `Permission for role '${roleId}' on ${module}.${feature}.${action} already exists`,
      );
    }

    // Create the new permission
    return this.create({
      role_id: roleId,
      module,
      feature,
      action,
      created_by: createdBy,
    });
  }

  /**
   * Check if a role has a specific permission
   * @param roleId The role ID
   * @param module The module name
   * @param feature The feature name
   * @param action The action name
   * @returns True if the role has the permission, false otherwise
   */
  async hasPermission(
    roleId: string,
    module: string,
    feature: string,
    action: string,
  ): Promise<boolean> {
    const permissions = await (
      this.repository as PermissionRepository
    ).findByModuleFeatureAction(module, feature, action);

    return permissions.some((p) => p.role_id === roleId);
  }

  /**
   * Get all permissions for a role
   * @param roleId The role ID
   * @returns An array of permissions
   */
  async getPermissionsForRole(roleId: string): Promise<PermissionDocument[]> {
    return (this.repository as PermissionRepository).findByRoleId(roleId);
  }

  /**
   * Get all permissions for a module
   * @param module The module name
   * @returns An array of permissions
   */
  async getPermissionsForModule(module: string): Promise<PermissionDocument[]> {
    return (this.repository as PermissionRepository).findByModule(module);
  }

  /**
   * Get all permissions for a feature
   * @param feature The feature name
   * @returns An array of permissions
   */
  async getPermissionsForFeature(
    feature: string,
  ): Promise<PermissionDocument[]> {
    return (this.repository as PermissionRepository).findByFeature(feature);
  }

  /**
   * Get all permissions for an action
   * @param action The action name
   * @returns An array of permissions
   */
  async getPermissionsForAction(action: string): Promise<PermissionDocument[]> {
    return (this.repository as PermissionRepository).findByAction(action);
  }

  /**
   * Remove all permissions for a role
   * @param roleId The role ID
   * @returns True if any permissions were removed, false otherwise
   */
  async removeAllPermissionsForRole(roleId: string): Promise<boolean> {
    return (this.repository as PermissionRepository).deleteByRoleId(roleId);
  }
}

export default PermissionService;
