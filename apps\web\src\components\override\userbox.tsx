import { LogOut, Set<PERSON><PERSON>, User } from 'lucide-react'
import { useIdentityActions } from '@/hooks/use-identity-actions'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { Button } from '@/components/ui/button'

export function UserBox() {
  const { user, logout, logoutAll } = useIdentityActions()

  const handleLogout = async () => {
    try {
      await logout()
    } catch (error) {
      console.error('Logout failed:', error)
    }
  }

  const handleLogoutAll = async () => {
    try {
      await logoutAll()
    } catch (error) {
      console.error('Logout all failed:', error)
    }
  }

  // Get user initials for avatar
  const getUserInitials = (name: string) => {
    return name
      .split(' ')
      .map((word) => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2)
  }

  // Get primary role for display
  const getPrimaryRole = (roles: string[]) => {
    if (roles.includes('admin')) return 'Quản trị viên'
    if (roles.includes('manager')) return 'Quản lý'
    if (roles.includes('user')) return 'Người dùng'
    return roles[0] || 'Người dùng'
  }

  if (!user) {
    return null
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" className="h-auto p-0 hover:bg-transparent">
          <div className="flex flex-col justify-start items-start flex-grow-0 flex-shrink-0">
            <div className="flex justify-start items-center self-stretch flex-grow-0 flex-shrink-0 gap-3">
              <div className="flex flex-col justify-start items-start flex-grow-0 flex-shrink-0">
                <div className="flex flex-col justify-start items-end flex-grow-0 flex-shrink-0 relative gap-1">
                  <p className="flex-grow-0 flex-shrink-0 text-[13px] font-semibold text-left text-[#1f2329]">
                    {user.name || user.username}
                  </p>
                  <p className="flex-grow-0 flex-shrink-0 text-xs font-medium text-left text-[#1f2329]/50">
                    {getPrimaryRole(user.roles)}
                  </p>
                </div>
              </div>
              <div
                className="flex flex-col justify-center items-center flex-grow-0 flex-shrink-0 h-8 w-8 relative overflow-hidden gap-2.5 rounded-[28px]"
                style={{
                  background:
                    'linear-gradient(135.5deg, #e53c3f 26.91%, #f182a9 91.43%)',
                }}
              >
                <p className="flex-grow-0 flex-shrink-0 text-xs font-semibold text-left text-white">
                  {getUserInitials(user.name || user.username)}
                </p>
              </div>
            </div>
          </div>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="w-56" align="end" forceMount>
        <DropdownMenuLabel className="font-normal">
          <div className="flex flex-col space-y-1">
            <p className="text-sm font-medium leading-none">
              {user.name || user.username}
            </p>
            <p className="text-xs leading-none text-muted-foreground">
              {user.email || user.username}
            </p>
          </div>
        </DropdownMenuLabel>
        <DropdownMenuSeparator />
        <DropdownMenuItem>
          <User className="mr-2 h-4 w-4" />
          <span>Hồ sơ</span>
        </DropdownMenuItem>
        <DropdownMenuItem>
          <Settings className="mr-2 h-4 w-4" />
          <span>Cài đặt</span>
        </DropdownMenuItem>
        <DropdownMenuSeparator />
        <DropdownMenuItem onClick={handleLogout}>
          <LogOut className="mr-2 h-4 w-4" />
          <span>Đăng xuất</span>
        </DropdownMenuItem>
        <DropdownMenuItem onClick={handleLogoutAll}>
          <LogOut className="mr-2 h-4 w-4" />
          <span>Đăng xuất tất cả thiết bị</span>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
