{
  "extends": "@c-cam/tsconfig/base.json",
  "compilerOptions": {
    /* JSX & Hono */
    "jsx": "react-jsx",
    "jsxImportSource": "hono/jsx",
    /* Decorators */
    "experimentalDecorators": true,
    "emitDecoratorMetadata": true,
    /* Output */
    "outDir": "./dist",
    "declaration": true,
    "noEmit": true,
    /* Path Aliases */
    "baseUrl": ".",
    "paths": {
      "@/*": [
        "./src/*"
      ]
    }
  },
  "ts-node": {
    "require": [
      "tsconfig-paths/register"
    ]
  },
  "exclude": [
    "node_modules"
  ]
}
