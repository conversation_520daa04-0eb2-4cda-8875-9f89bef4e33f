import RoleModel, { RoleDocument } from '@/database/entities/RoleModel';
import { Repository } from '@c-cam/core';

/**
 * Repository for managing roles
 * Extends the BaseRepository with RoleDocument type
 */
class RoleRepository extends Repository<RoleDocument> {
  constructor() {
    super(RoleModel);
  }

  /**
   * Find a role by name
   * @param name The role name to search for
   * @returns A promise that resolves to a role or null if not found
   */
  async findByName(name: string): Promise<RoleDocument | null> {
    return this.findOne({ name });
  }

  /**
   * Find roles by member role ID
   * @param memberRoleId The member role ID to search for
   * @returns A promise that resolves to an array of roles
   */
  async findByMemberRoleId(memberRoleId: string): Promise<RoleDocument[]> {
    return this.find({ member_role_id: memberRoleId });
  }

  /**
   * Find roles by permission ID
   * @param permissionId The permission ID to search for
   * @returns A promise that resolves to an array of roles
   */
  async findByPermissionId(permissionId: string): Promise<RoleDocument[]> {
    return this.find({ permission_id: permissionId });
  }

  /**
   * Find roles by created by
   * @param createdBy The creator ID to search for
   * @returns A promise that resolves to an array of roles
   */
  async findByCreatedBy(createdBy: string): Promise<RoleDocument[]> {
    return this.find({ created_by: createdBy });
  }
}

export default RoleRepository;
