import { createFileRoute } from '@tanstack/react-router'
import ProtectedLayout from '@/components/layout/protected.layout'
import { useCamerasQuery } from '@/hooks/use-camera-query'

export const Route = createFileRoute('/cameras/')({
  component: RouteComponent,
})

function RouteComponent() {
  const { data: camerasData, isLoading, error } = useCamerasQuery({
    limit: 10,
    sortBy: 'name',
    sortDirection: 'asc'
  })

  if (isLoading) {
    return (
      <ProtectedLayout>
        <div>Loading cameras...</div>
      </ProtectedLayout>
    )
  }

  if (error) {
    return (
      <ProtectedLayout>
        <div>Error loading cameras: {error.message}</div>
      </ProtectedLayout>
    )
  }

  return (
    <ProtectedLayout>
      <div>
        <h1>Cameras</h1>
        {camerasData?.cameras && camerasData.cameras.length > 0 ? (
          <ul>
            {camerasData.cameras.map((camera) => (
              <li key={camera.id}>
                <strong>{camera.name}</strong>
                {camera.location && <span> - {camera.location}</span>}
                {camera.status && <span> ({camera.status})</span>}
              </li>
            ))}
          </ul>
        ) : (
          <p>No cameras found.</p>
        )}
      </div>
    </ProtectedLayout>
  )
}
