import { BaseModel, Inject, Injectable } from '@c-cam/core';
import { EdgeDeviceInfoDocument } from '@/database/entities/EdgeDeviceInfoModel';
import EdgeDeviceInfoRepository from '@/repositories/EdgeDeviceInfoRepository';

/**
 * Service for managing edge device information
 * Extends the BaseModel with EdgeDeviceInfoDocument type
 */
@Injectable()
class EdgeDeviceInfoService extends BaseModel<EdgeDeviceInfoDocument> {
  /**
   * Create a new EdgeDeviceInfoService
   * @param tenantId Optional tenant ID for multi-tenant operations
   */
  constructor(
    @Inject(EdgeDeviceInfoRepository)
    repository: EdgeDeviceInfoRepository,
  ) {
    super(repository);
  }

  /**
   * Find edge device info by edge device ID
   * @param edgeDeviceId The edge device ID to search for
   * @returns A promise that resolves to an array of edge device info
   */
  async findByEdgeDeviceId(
    edgeDeviceId: string,
  ): Promise<EdgeDeviceInfoDocument[]> {
    return (this.repository as EdgeDeviceInfoRepository).findByEdgeDeviceId(
      edgeDeviceId,
    );
  }

  /**
   * Find edge device info by CPU model
   * @param cpuModel The CPU model to search for
   * @returns A promise that resolves to an array of edge device info
   */
  async findByCpuModel(cpuModel: string): Promise<EdgeDeviceInfoDocument[]> {
    return (this.repository as EdgeDeviceInfoRepository).findByCpuModel(
      cpuModel,
    );
  }

  /**
   * Find edge device info by operating system
   * @param os The operating system to search for
   * @returns A promise that resolves to an array of edge device info
   */
  async findByOperatingSystem(os: string): Promise<EdgeDeviceInfoDocument[]> {
    return (this.repository as EdgeDeviceInfoRepository).findByOperatingSystem(
      os,
    );
  }

  /**
   * Find edge device info by date range
   * @param startDate The start date of the range
   * @param endDate The end date of the range
   * @returns A promise that resolves to an array of edge device info
   */
  async findByDateRange(
    startDate: Date,
    endDate: Date,
  ): Promise<EdgeDeviceInfoDocument[]> {
    return (this.repository as EdgeDeviceInfoRepository).findByDateRange(
      startDate,
      endDate,
    );
  }

  /**
   * Delete edge device info by edge device ID
   * @param edgeDeviceId The edge device ID to delete info for
   * @returns A promise that resolves to true if any info was deleted, false otherwise
   */
  async deleteByEdgeDeviceId(edgeDeviceId: string): Promise<boolean> {
    return (this.repository as EdgeDeviceInfoRepository).deleteByEdgeDeviceId(
      edgeDeviceId,
    );
  }

  /**
   * Create new edge device info
   * @param infoData The edge device info data
   * @returns The newly created edge device info
   */
  async createEdgeDeviceInfo(infoData: {
    edge_device_id: string;
    cpu_model: string;
    cpu_cores: number;
    cpu_frequency: number;
    total_memory: number;
    available_memory: number;
    operating_system: string;
    os_version: string;
    gpu_model?: string;
    gpu_memory?: number;
    created_by: string;
  }): Promise<EdgeDeviceInfoDocument> {
    // Validate required fields
    if (!infoData.edge_device_id) {
      throw new Error('Edge device ID is required');
    }

    if (!infoData.cpu_model) {
      throw new Error('CPU model is required');
    }

    if (!infoData.operating_system) {
      throw new Error('Operating system is required');
    }

    // Create the new edge device info
    return this.create(infoData);
  }

  /**
   * Update edge device info
   * @param id The edge device info ID
   * @param infoData The data to update
   * @returns True if the edge device info was updated, false otherwise
   */
  async updateEdgeDeviceInfo(
    id: string,
    infoData: Partial<{
      edge_device_id: string;
      cpu_model: string;
      cpu_cores: number;
      cpu_frequency: number;
      total_memory: number;
      available_memory: number;
      operating_system: string;
      os_version: string;
      gpu_model: string;
      gpu_memory: number;
    }>,
  ): Promise<boolean> {
    // Check if the edge device info exists
    const info = await this.findById(id);
    if (!info) {
      throw new Error(`Edge device info with ID '${id}' not found`);
    }

    // Update the edge device info
    return this.update(id, infoData);
  }
}

export default EdgeDeviceInfoService;
