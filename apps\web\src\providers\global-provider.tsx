import React, { createContext, useContext, useEffect, useState } from 'react'
import { useLocation } from '@tanstack/react-router'

// Global refresh token state - shared between axios and hooks
export const globalRefreshState = {
  isRefreshing: false,
  refreshPromise: null as Promise<string | null> | null,
  isOnAuthPage: false,
}

// Global context for app-wide state (NOT auth state)
const GlobalContext = createContext<{
  isInitialized: boolean
  currentRoute: string
  isRefreshingToken: boolean
  refreshTokenPromise: Promise<string | null> | null
  synchronizedRefreshToken: () => Promise<string | null>
} | undefined>(undefined)

// Hook to use global context
export const useGlobalContext = () => {
  const context = useContext(GlobalContext)
  if (context === undefined) {
    throw new Error('useGlobalContext must be used within a GlobalProvider')
  }
  return context
}

interface GlobalProviderProps {
  children: React.ReactNode
}

/**
 * Global provider that manages app-wide state and synchronizes refresh token logic
 * This provider does NOT handle auth state - that's handled by AuthProvider
 */
export const GlobalProvider: React.FC<GlobalProviderProps> = ({ children }) => {
  const location = useLocation()
  const [isInitialized, setIsInitialized] = useState(false)
  const [isRefreshingToken, setIsRefreshingToken] = useState(false)
  const [refreshTokenPromise, setRefreshTokenPromise] = useState<Promise<string | null> | null>(null)

  // Track current route and update global state
  useEffect(() => {
    const isAuthPage = location.pathname.startsWith('/auth/')
    globalRefreshState.isOnAuthPage = isAuthPage

    // Don't attempt refresh token on auth pages
    if (isAuthPage) {
      globalRefreshState.isRefreshing = false
      globalRefreshState.refreshPromise = null
    }
  }, [location.pathname])

  // Initialize app state
  useEffect(() => {
    // Simple initialization - not dependent on auth
    setIsInitialized(true)
  }, [])

  // Synchronized refresh token function that can be called from anywhere
  const synchronizedRefreshToken = async (): Promise<string | null> => {
    // Don't refresh on auth pages
    if (globalRefreshState.isOnAuthPage) {
      console.debug('Skipping token refresh on auth page')
      return null
    }

    // If already refreshing, return the existing promise
    if (globalRefreshState.isRefreshing && globalRefreshState.refreshPromise) {
      console.debug('Token refresh already in progress, waiting for existing promise')
      return globalRefreshState.refreshPromise
    }

    // Start new refresh
    globalRefreshState.isRefreshing = true
    setIsRefreshingToken(true)

    // Create a promise that will be resolved by the auth provider
    const refreshPromise = new Promise<string | null>((resolve) => {
      // This will be called by the auth provider's refresh function
      const refreshFn = (window as any).__authRefreshToken
      if (refreshFn && typeof refreshFn === 'function') {
        refreshFn()
          .then((token: string | null) => {
            console.debug('Global token refresh successful')
            resolve(token)
          })
          .catch((error: any) => {
            console.error('Global token refresh failed:', error)
            resolve(null)
          })
      } else {
        console.warn('Auth refresh function not available')
        resolve(null)
      }
    }).finally(() => {
      globalRefreshState.isRefreshing = false
      globalRefreshState.refreshPromise = null
      setIsRefreshingToken(false)
      setRefreshTokenPromise(null)
    })

    globalRefreshState.refreshPromise = refreshPromise
    setRefreshTokenPromise(refreshPromise)

    return refreshPromise
  }

  // Expose synchronized refresh function globally
  useEffect(() => {
    // Make the synchronized refresh function available globally
    ;(window as any).__globalRefreshToken = synchronizedRefreshToken
  }, [])

  const globalContextValue = {
    isInitialized,
    currentRoute: location.pathname,
    isRefreshingToken,
    refreshTokenPromise,
    synchronizedRefreshToken,
  }

  return (
    <GlobalContext.Provider value={globalContextValue}>
      {children}
    </GlobalContext.Provider>
  )
}

export default GlobalProvider
