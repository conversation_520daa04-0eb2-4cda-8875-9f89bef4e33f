import React, { createContext, useContext, useEffect, useState } from 'react'
import { useLocation } from '@tanstack/react-router'
import type { AuthContextType, GlobalState } from '@/types/auth'
import { useIdentityActions } from '@/hooks/use-identity-actions'

// Global context
const GlobalContext = createContext<GlobalState | undefined>(undefined)

// Global refresh token state - shared between axios and hooks
export const globalRefreshState = {
  isRefreshing: false,
  refreshPromise: null as Promise<string | null> | null,
  isOnAuthPage: false,
}

// Hook to use global context
export const useGlobalContext = () => {
  const context = useContext(GlobalContext)
  if (context === undefined) {
    throw new Error('useGlobalContext must be used within a GlobalProvider')
  }
  return context
}

// Hook to use auth context (backward compatibility)
export const useAuth = () => {
  const { auth } = useGlobalContext()
  return auth
}

interface GlobalProviderProps {
  children: React.ReactNode
}

/**
 * Global provider that manages all application state including auth
 * This provider synchronizes refresh token logic across the entire app
 */
export const GlobalProvider: React.FC<GlobalProviderProps> = ({ children }) => {
  const location = useLocation()
  const [isInitialized, setIsInitialized] = useState(false)
  const [isRefreshingToken, setIsRefreshingToken] = useState(false)
  const [refreshTokenPromise, setRefreshTokenPromise] = useState<Promise<string | null> | null>(null)

  // Use the identity actions hook which contains all the auth logic
  const authActions = useIdentityActions()

  // Track current route and update global state
  useEffect(() => {
    const isAuthPage = location.pathname.startsWith('/auth/')
    globalRefreshState.isOnAuthPage = isAuthPage

    // Don't attempt refresh token on auth pages
    if (isAuthPage) {
      globalRefreshState.isRefreshing = false
      globalRefreshState.refreshPromise = null
    }
  }, [location.pathname])

  // Initialize app state
  useEffect(() => {
    const initializeApp = async () => {
      try {
        // Wait for auth initialization - check if not loading
        if (!authActions.isLoading) {
          setIsInitialized(true)
        }
      } catch (error) {
        console.error('Failed to initialize app:', error)
        setIsInitialized(true) // Still mark as initialized to prevent infinite loading
      }
    }

    initializeApp()
  }, [authActions.isLoading])

  // Synchronized refresh token function
  const synchronizedRefreshToken = async (): Promise<string | null> => {
    // Don't refresh on auth pages
    if (globalRefreshState.isOnAuthPage) {
      console.debug('Skipping token refresh on auth page')
      return null
    }

    // If already refreshing, return the existing promise
    if (globalRefreshState.isRefreshing && globalRefreshState.refreshPromise) {
      console.debug('Token refresh already in progress, waiting for existing promise')
      return globalRefreshState.refreshPromise
    }

    // Start new refresh
    globalRefreshState.isRefreshing = true
    setIsRefreshingToken(true)

    const refreshPromise = authActions.refreshAccessToken()
      .then((token) => {
        console.debug('Global token refresh successful')
        return token || null
      })
      .catch((error) => {
        console.error('Global token refresh failed:', error)
        return null
      })
      .finally(() => {
        globalRefreshState.isRefreshing = false
        globalRefreshState.refreshPromise = null
        setIsRefreshingToken(false)
        setRefreshTokenPromise(null)
      })

    globalRefreshState.refreshPromise = refreshPromise
    setRefreshTokenPromise(refreshPromise)

    return refreshPromise
  }

  // Expose synchronized refresh function globally
  useEffect(() => {
    // Make the synchronized refresh function available globally
    ;(window as any).__globalRefreshToken = synchronizedRefreshToken
  }, [])

  const authContextValue: AuthContextType = {
    // State from useIdentityActions
    user: authActions.user,
    accessToken: null, // We don't expose access token directly for security
    refreshToken: null, // Refresh token is in HttpOnly cookie
    isAuthenticated: authActions.isAuthenticated,
    isLoading: authActions.isLoading,
    error: authActions.error?.message || null,

    // Actions from useIdentityActions
    login: authActions.login,
    logout: authActions.logout,
    logoutAll: authActions.logoutAll,
    refreshAccessToken: async () => {
      await synchronizedRefreshToken()
    },
    verifyToken: authActions.verifyToken,
    clearError: () => {}, // Error clearing is handled in the hook
  }

  const globalState: GlobalState = {
    auth: authContextValue,
    isInitialized,
    currentRoute: location.pathname,
    isRefreshingToken,
    refreshTokenPromise,
  }

  return (
    <GlobalContext.Provider value={globalState}>
      {children}
    </GlobalContext.Provider>
  )
}

export default GlobalProvider
