# Core Package

This package contains the core framework for the c-cam application.

## Directory Structure

- **di/**: Dependency Injection system
  - `container.ts`: DI container implementation
  - `injectable.ts`: Dependency injection utilities
  - `type.ts`: Type definitions for the DI system

- **http/**: HTTP-related functionality
  - `controller.ts`: Base controller class
  - `http-error.ts`: HTTP error definitions
  - `middleware.ts`: Middleware handling
  - `router-factory.ts`: Router creation utilities

- **hosting/**: Application hosting
  - `application.ts`: Application configuration
  - `host.ts`: Application host and startup
  - `interfaces.ts`: Hosting interfaces

- **security/**: Security and authentication
  - `authorization.ts`: Authorization functionality
  - `claims-principal.ts`: Claims-based identity

- **decorators/**: TypeScript decorators
  - Various decorators for controllers, middleware, error handling, etc.

- **middlewares/**: Express middleware
  - `cors.middleware.ts`: CORS handling
  - `request-logger.middleware.ts`: Request logging

- **utilities/**: Helper functions
  - `async-handler.ts`: Async function utilities
  - `response.ts`: Response handling
  - `validation.ts`: Input validation
  - `helpers.ts`: General helper functions
  - `logger.ts`: Logging utilities

## Usage

All modules are exported from the main `index.ts` file, so you can import everything from the package root:

```typescript
import { Controller, Service, ApplicationHost } from '@c-cam/core';
```
