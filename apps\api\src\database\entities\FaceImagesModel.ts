import { Document } from 'mongoose';
import { createSchema, createModel } from '@c-cam/core';
import { FaceImagesAttributes } from '@c-cam/types';

/**
 * Face Images Document Interface
 * Extends the FaceImagesAttributes (excluding id) and Document
 */
export interface FaceImagesDocument
  extends Omit<FaceImagesAttributes, 'id'>,
    Document {}

/**
 * Face Images Schema
 * Defines the MongoDB schema for face images
 */
const FaceImagesSchema = createSchema({
  user_id: {
    type: String,
    ref: 'users',
    required: true,
  },
  image_url: { type: String, required: true },
  image_angle: { type: String, required: false },
  created_by: { type: String, required: true },
});

// Add indexes
FaceImagesSchema.index({ user_id: 1 });

// Create and export the model
const FaceImagesModel = createModel<FaceImagesDocument>(
  'face_images',
  FaceImagesSchema,
);

export default FaceImagesModel;
