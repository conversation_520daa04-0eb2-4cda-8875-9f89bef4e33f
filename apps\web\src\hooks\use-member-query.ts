import { useApiQuery } from '@/shared/hooks/use-api-query'

// Types for member/user data
export interface Member {
  id: string
  username: string
  email?: string
  first_name?: string
  last_name?: string
  full_name?: string
  phone?: string
  status?: string
  unit_id?: string
  created_at?: string
  updated_at?: string
  created_by?: string
}

export interface MemberQueryParams {
  limit?: number
  skip?: number
  sortBy?: string
  sortDirection?: 'asc' | 'desc'
}

export interface MemberListResponse {
  users: Array<Member>
}

export interface MemberResponse {
  user: Member
}

/**
 * Hook to fetch all members/users with optional pagination and sorting
 */
export const useMembersQuery = (params?: MemberQueryParams) => {
  return useApiQuery<MemberListResponse>(
    ['members', ...(params ? [JSON.stringify(params)] : [])],
    '/api/users',
    params
  )
}

/**
 * Hook to fetch a single member by ID
 */
export const useMemberQuery = (id: string, enabled = true) => {
  return useApiQuery<MemberResponse>(
    ['members', id],
    `/api/users/${id}`,
    undefined,
    { enabled: enabled && !!id }
  )
}

/**
 * Hook to fetch members by unit ID
 */
export const useMembersByUnitQuery = (unitId: string, enabled = true) => {
  return useApiQuery<MemberListResponse>(
    ['members', 'unit', unitId],
    `/api/users/unit/${unitId}`,
    undefined,
    { enabled: enabled && !!unitId }
  )
}

/**
 * Hook to fetch member by username
 */
export const useMemberByUsernameQuery = (username: string, enabled = true) => {
  return useApiQuery<MemberResponse>(
    ['members', 'username', username],
    `/api/users/username/${username}`,
    undefined,
    { enabled: enabled && !!username }
  )
}

/**
 * Hook to fetch member by email
 */
export const useMemberByEmailQuery = (email: string, enabled = true) => {
  return useApiQuery<MemberResponse>(
    ['members', 'email', email],
    `/api/users/email/${email}`,
    undefined,
    { enabled: enabled && !!email }
  )
}

/**
 * Hook to fetch members created by a specific user
 */
export const useMembersByCreatorQuery = (createdBy: string, enabled = true) => {
  return useApiQuery<MemberListResponse>(
    ['members', 'created-by', createdBy],
    `/api/users/created-by/${createdBy}`,
    undefined,
    { enabled: enabled && !!createdBy }
  )
}
