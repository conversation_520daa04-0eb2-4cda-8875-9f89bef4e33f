import { Document } from 'mongoose';
import { createSchema, createModel } from '@c-cam/core';
import { AuditTrailLogsAttributes } from '@c-cam/types';

/**
 * Audit Trail Logs Document Interface
 * Extends the AuditTrailLogsAttributes (excluding id) and Document
 */
export interface AuditTrailLogsDocument
  extends Omit<AuditTrailLogsAttributes, 'id'>,
    Document {}

/**
 * Audit Trail Logs Schema
 * Defines the MongoDB schema for audit trail logs
 */
const AuditTrailLogsSchema = createSchema({
  user_id: {
    type: String,
    ref: 'users',
    required: true,
  },
  module: { type: String, required: true },
  action: { type: String, required: true },
  description: { type: String, required: true },
  created_by: { type: String, required: true },
});

// Add indexes
AuditTrailLogsSchema.index({ user_id: 1, created_at: -1 });

// Create and export the model
const AuditTrailLogsModel = createModel<AuditTrailLogsDocument>(
  'audit_trail_logs',
  AuditTrailLogsSchema,
);

export default AuditTrailLogsModel;
