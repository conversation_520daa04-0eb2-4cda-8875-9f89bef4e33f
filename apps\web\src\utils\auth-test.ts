/**
 * Authentication System Test Utilities
 *
 * This file contains utilities to test the secure authentication implementation
 */

import { clearAccessToken, getAccessToken, setAccessToken } from './auth'
import { axiosClient } from '@/configs/axios'

export interface AuthTestResult {
  success: boolean
  message: string
  data?: any
  error?: any
}

/**
 * Test login functionality
 */
export const testLogin = async (
  username: string,
  password: string,
): Promise<AuthTestResult> => {
  try {
    const response = await axiosClient.post('/api/identity/login', {
      username,
      password,
      deviceInfo: {
        deviceName: 'Test Device',
        deviceType: 'desktop',
        userAgent: navigator.userAgent,
      },
    })

    if (response.data.access_token) {
      setAccessToken(response.data.access_token)
      return {
        success: true,
        message: 'Login successful',
        data: response.data,
      }
    } else {
      return {
        success: false,
        message: 'No access token received',
        data: response.data,
      }
    }
  } catch (error: any) {
    return {
      success: false,
      message: 'Login failed',
      error: error.response?.data || error.message,
    }
  }
}

/**
 * Test token refresh functionality
 */
export const testTokenRefresh = async (): Promise<AuthTestResult> => {
  try {
    const response = await axiosClient.post('/api/identity/refresh-token', {
      deviceInfo: {
        deviceName: 'Test Device',
        deviceType: 'desktop',
        userAgent: navigator.userAgent,
      },
    })

    if (response.data.access_token) {
      setAccessToken(response.data.access_token)
      return {
        success: true,
        message: 'Token refresh successful',
        data: response.data,
      }
    } else {
      return {
        success: false,
        message: 'No access token received from refresh',
        data: response.data,
      }
    }
  } catch (error: any) {
    return {
      success: false,
      message: 'Token refresh failed',
      error: error.response?.data || error.message,
    }
  }
}

/**
 * Test protected endpoint access
 */
export const testProtectedEndpoint = async (): Promise<AuthTestResult> => {
  try {
    const response = await axiosClient.get('/api/identity/me')

    return {
      success: true,
      message: 'Protected endpoint access successful',
      data: response.data,
    }
  } catch (error: any) {
    return {
      success: false,
      message: 'Protected endpoint access failed',
      error: error.response?.data || error.message,
    }
  }
}

/**
 * Test logout functionality
 */
export const testLogout = async (): Promise<AuthTestResult> => {
  try {
    const response = await axiosClient.post('/api/identity/logout')

    clearAccessToken()

    return {
      success: true,
      message: 'Logout successful',
      data: response.data,
    }
  } catch (error: any) {
    return {
      success: false,
      message: 'Logout failed',
      error: error.response?.data || error.message,
    }
  }
}

/**
 * Test token reuse detection (security test)
 */
export const testTokenReuseDetection = async (): Promise<AuthTestResult> => {
  try {
    // First refresh - should work
    const firstRefresh = await testTokenRefresh()
    if (!firstRefresh.success) {
      return {
        success: false,
        message: 'First refresh failed',
        error: firstRefresh.error,
      }
    }

    // Try to use the same refresh token again - should fail
    const secondRefresh = await testTokenRefresh()

    if (secondRefresh.success) {
      return {
        success: false,
        message:
          'Token reuse detection failed - second refresh should have failed',
        data: secondRefresh.data,
      }
    } else {
      return {
        success: true,
        message: 'Token reuse detection working correctly',
        data: secondRefresh.error,
      }
    }
  } catch (error: any) {
    return {
      success: false,
      message: 'Token reuse test failed',
      error: error.message,
    }
  }
}

/**
 * Test XSS protection (verify tokens not in localStorage)
 */
export const testXSSProtection = (): AuthTestResult => {
  try {
    // Check if any auth tokens are in localStorage
    const accessTokenInStorage =
      localStorage.getItem('auth_token') ||
      localStorage.getItem('access_token') ||
      localStorage.getItem('token')

    const refreshTokenInStorage = localStorage.getItem('refresh_token')

    if (accessTokenInStorage || refreshTokenInStorage) {
      return {
        success: false,
        message: 'XSS protection failed - tokens found in localStorage',
        data: {
          accessTokenInStorage: !!accessTokenInStorage,
          refreshTokenInStorage: !!refreshTokenInStorage,
        },
      }
    }

    // Check if access token is in memory
    const accessTokenInMemory = getAccessToken()

    return {
      success: true,
      message: 'XSS protection working correctly',
      data: {
        accessTokenInMemory: !!accessTokenInMemory,
        accessTokenInStorage: false,
        refreshTokenInStorage: false,
      },
    }
  } catch (error: any) {
    return {
      success: false,
      message: 'XSS protection test failed',
      error: error.message,
    }
  }
}

/**
 * Run comprehensive auth system tests
 */
export const runAuthTests = async (
  username: string,
  password: string,
): Promise<{
  results: Record<string, AuthTestResult>
  summary: {
    total: number
    passed: number
    failed: number
    passRate: number
  }
}> => {
  const results: Record<string, AuthTestResult> = {}

  console.log('🔐 Starting Authentication System Tests...')

  // Test 1: Login
  console.log('1. Testing login...')
  results.login = await testLogin(username, password)

  // Test 2: Protected endpoint access
  console.log('2. Testing protected endpoint access...')
  results.protectedEndpoint = await testProtectedEndpoint()

  // Test 3: Token refresh
  console.log('3. Testing token refresh...')
  results.tokenRefresh = await testTokenRefresh()

  // Test 4: XSS protection
  console.log('4. Testing XSS protection...')
  results.xssProtection = testXSSProtection()

  // Test 5: Token reuse detection (commented out as it will invalidate session)
  // console.log('5. Testing token reuse detection...')
  // results.tokenReuseDetection = await testTokenReuseDetection()

  // Test 6: Logout
  console.log('6. Testing logout...')
  results.logout = await testLogout()

  // Calculate summary
  const total = Object.keys(results).length
  const passed = Object.values(results).filter((r) => r.success).length
  const failed = total - passed
  const passRate = (passed / total) * 100

  const summary = {
    total,
    passed,
    failed,
    passRate,
  }

  console.log('🔐 Authentication Tests Complete!')
  console.log(`✅ Passed: ${passed}/${total} (${passRate.toFixed(1)}%)`)

  if (failed > 0) {
    console.log(`❌ Failed: ${failed}/${total}`)
    Object.entries(results).forEach(([test, result]) => {
      if (!result.success) {
        console.log(`   - ${test}: ${result.message}`)
      }
    })
  }

  return { results, summary }
}

/**
 * Quick auth status check
 */
export const checkAuthStatus = (): {
  isAuthenticated: boolean
  hasAccessToken: boolean
  accessTokenInMemory: boolean
  tokensInLocalStorage: boolean
} => {
  const accessToken = getAccessToken()
  const tokensInStorage = !!(
    localStorage.getItem('auth_token') ||
    localStorage.getItem('access_token') ||
    localStorage.getItem('refresh_token') ||
    localStorage.getItem('token')
  )

  return {
    isAuthenticated: !!accessToken,
    hasAccessToken: !!accessToken,
    accessTokenInMemory: !!accessToken,
    tokensInLocalStorage: tokensInStorage,
  }
}
