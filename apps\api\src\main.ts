/**
 * CCamAI API Application
 * Entry point for the API server with database seeding
 */

import { Hono } from 'hono';
import { cors } from 'hono/cors';
import { logger } from 'hono/logger';
import { serve } from '@hono/node-server';

import {
  RouterFactory,
  Container,
  GlobalAuthorizedMiddleware,
  createProductionErrorHandler,
  createDevelopmentErrorHandler,
  MongooseConnection,
} from '@c-cam/core';
import { logger as smartLogger } from '@c-cam/logger';

import { DatabaseSeeder, runSeeding } from './database/seed';
import { environment } from './configs/environment';

// Controllers
import { IdentityController } from './controllers/identity.controller';
import { CameraController } from './controllers/camera.controller';
import { DailyAttendanceSummariesController } from './controllers/daily-attendance-summaries.controller';
import { EdgeDeviceController } from './controllers/edge-device.controller';
import { EdgeDeviceInfoController } from './controllers/edge-device-info.controller';
import { EdgeDeviceLogsController } from './controllers/edge-device-logs.controller';
import { FaceImagesController } from './controllers/face-images.controller';
import { FaceRecognitionLogsController } from './controllers/face-recognition-logs.controller';
import { MemberRoleController } from './controllers/member-role.controller';
import { PermissionController } from './controllers/permission.controller';
import { RoleController } from './controllers/role.controller';
import { ShiftController } from './controllers/shift.controller';
import { ShiftDetailController } from './controllers/shift-detail.controller';
import { TenantController } from './controllers/tenant.controller';
import { UnitController } from './controllers/unit.controller';
import { UsersController } from './controllers/users.controller';

// Initialize Hono app
const app = new Hono();

// Middleware
app.use('*', cors({
  origin: environment.cors.origin.split(',').map(origin => origin.trim()),
  allowHeaders: ['Authorization', 'Content-Type'],
  allowMethods: environment.cors.methods,
  credentials: true,
  maxAge: 86400,
}));

app.use('*', logger());
app.use('*', environment.name === 'PRODUCTION'
  ? createProductionErrorHandler()
  : createDevelopmentErrorHandler()
);
app.use('*', GlobalAuthorizedMiddleware);

// Register controllers
const controllers = RouterFactory.createRoutes(() => {
  Container.register(DatabaseSeeder);
  Container.register(IdentityController);
  Container.register(CameraController);
  Container.register(DailyAttendanceSummariesController);
  Container.register(EdgeDeviceController);
  Container.register(EdgeDeviceInfoController);
  Container.register(EdgeDeviceLogsController);
  Container.register(FaceImagesController);
  Container.register(FaceRecognitionLogsController);
  Container.register(MemberRoleController);
  Container.register(PermissionController);
  Container.register(RoleController);
  Container.register(ShiftController);
  Container.register(ShiftDetailController);
  Container.register(TenantController);
  Container.register(UnitController);
  Container.register(UsersController);
});

// Routes
app.route('/', controllers);
app.get('/health', (c) => c.json({ message: 'OK' }));

// Database initialization
async function initializeDatabase(): Promise<void> {
  smartLogger.info('Connecting to MongoDB...');
  const mongoConnection = MongooseConnection.getInstance();
  await mongoConnection.initialize({
    uri: environment.mongo.uri,
    options: environment.mongo.options,
  });
  smartLogger.info('MongoDB connected successfully');

  smartLogger.info('Running database seeding...');
  await runSeeding({
    force: false,
    adminUser: {
      username: environment.admin.email.split('@')[0] || 'admin',
      email: environment.admin.email,
      password: environment.admin.password,
      name: 'System Administrator',
    },
  });
  smartLogger.info('Database seeding completed');
}

// Server startup
let server: any;

async function startServer(): Promise<void> {
  try {
    smartLogger.info('Initializing CCamAI API...');
    await initializeDatabase();

    smartLogger.info(`Starting server on port ${environment.server.port}...`);
    server = serve({
      fetch: app.fetch,
      port: environment.server.port,
    });
    smartLogger.info(`Server started successfully on port ${environment.server.port}`);
  } catch (error) {
    smartLogger.error('Failed to start server:', error);
    process.exit(1);
  }
}

startServer();

// Graceful shutdown
const shutdown = async () => {
  smartLogger.info('Shutting down server...');
  try {
    if (server) {
      await new Promise<void>((resolve) => server.close(() => resolve()));
      smartLogger.info('Server closed');
    }

    const mongoConnection = MongooseConnection.getInstance();
    if (mongoConnection.isConnected()) {
      await mongoConnection.close();
      smartLogger.info('Database closed');
    }

    smartLogger.info('Shutdown completed');
    process.exit(0);
  } catch (error) {
    smartLogger.error('Shutdown error:', error);
    process.exit(1);
  }
};

process.on('SIGTERM', shutdown);
process.on('SIGINT', shutdown);

export default app;
