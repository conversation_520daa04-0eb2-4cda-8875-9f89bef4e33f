import { BaseModel, Inject, Injectable } from '@c-cam/core';
import { UnitDocument } from '@/database/entities/UnitModel';
import UnitRepository from '@/repositories/UnitRepository';

/**
 * Service for managing organizational units
 * Extends the BaseModel with UnitDocument type
 */
@Injectable()
class UnitService extends BaseModel<UnitDocument> {
  /**
   * Create a new UnitService
   * @param tenantId Optional tenant ID for multi-tenant operations
   */
  constructor(
    @Inject(UnitRepository)
    repository: UnitRepository,
  ) {
    super(repository);
  }

  /**
   * Create a new organizational unit
   * @param unitData The unit data
   * @returns The newly created unit
   */
  async createUnit(unitData: {
    organization_id: string;
    name: string;
    created_by: string;
    user_id?: string;
    parent_unit_id?: string;
  }): Promise<UnitDocument> {
    // Check if a unit with the same name already exists in the organization
    const existingUnit = await (
      this.repository as UnitRepository
    ).findByNameAndOrganizationId(unitData.name, unitData.organization_id);

    if (existingUnit) {
      throw new Error(
        `Unit with name '${unitData.name}' already exists in this organization`,
      );
    }

    // Create the new unit
    return this.create(unitData);
  }

  /**
   * Update an organizational unit
   * @param id The unit ID
   * @param unitData The data to update
   * @returns True if the unit was updated, false otherwise
   */
  async updateUnit(
    id: string,
    unitData: Partial<{
      organization_id: string;
      user_id: string;
      parent_unit_id: string;
      name: string;
    }>,
  ): Promise<boolean> {
    // Check if the unit exists
    const unit = await this.findById(id);
    if (!unit) {
      throw new Error(`Unit with ID '${id}' not found`);
    }

    // If name or organization_id is being updated, check for duplicates
    if (
      (unitData.name && unitData.name !== unit.name) ||
      (unitData.organization_id &&
        unitData.organization_id !== unit.organization_id)
    ) {
      const orgId = unitData.organization_id || unit.organization_id;
      const name = unitData.name || unit.name;

      const existingUnit = await (
        this.repository as UnitRepository
      ).findByNameAndOrganizationId(name, orgId);

      if (existingUnit && existingUnit.id !== id) {
        throw new Error(
          `Unit with name '${name}' already exists in this organization`,
        );
      }
    }

    // Update the unit
    return this.update(id, unitData);
  }

  /**
   * Get the organizational hierarchy
   * @param organizationId The organization ID
   * @returns A hierarchical structure of units
   */
  async getOrganizationalHierarchy(organizationId: string): Promise<
    Array<{
      id: string;
      name: string;
      user_id?: string;
      children: Array<unknown>;
    }>
  > {
    // Get all units for the organization
    const units = await (
      this.repository as UnitRepository
    ).findByOrganizationId(organizationId);

    // Build the hierarchy recursively
    const buildHierarchy = (
      parentId: string | null,
    ): Array<{
      id: string;
      name: string;
      user_id?: string;
      children: Array<unknown>;
    }> => {
      const children = units.filter((unit) =>
        parentId === null
          ? !unit.parent_unit_id
          : unit.parent_unit_id === parentId,
      );

      return children.map((unit) => ({
        id: unit.id,
        name: unit.name,
        user_id: unit.user_id,
        children: buildHierarchy(unit.id),
      }));
    };

    return buildHierarchy(null);
  }

  /**
   * Find units by organization ID
   * @param organizationId The organization ID
   * @returns An array of units
   */
  async findByOrganizationId(organizationId: string): Promise<UnitDocument[]> {
    return (this.repository as UnitRepository).findByOrganizationId(
      organizationId,
    );
  }

  /**
   * Find units by user ID
   * @param userId The user ID
   * @returns An array of units
   */
  async findByUserId(userId: string): Promise<UnitDocument[]> {
    return (this.repository as UnitRepository).findByUserId(userId);
  }

  /**
   * Find units by parent unit ID
   * @param parentUnitId The parent unit ID
   * @returns An array of units
   */
  async findByParentUnitId(parentUnitId: string): Promise<UnitDocument[]> {
    return (this.repository as UnitRepository).findByParentUnitId(parentUnitId);
  }

  /**
   * Find a unit by name and organization ID
   * @param name The unit name
   * @param organizationId The organization ID
   * @returns The unit or null if not found
   */
  async findByNameAndOrganizationId(
    name: string,
    organizationId: string,
  ): Promise<UnitDocument | null> {
    return (this.repository as UnitRepository).findByNameAndOrganizationId(
      name,
      organizationId,
    );
  }

  /**
   * Get all root units (units without a parent)
   * @returns An array of root units
   */
  async findRootUnits(): Promise<UnitDocument[]> {
    return (this.repository as UnitRepository).findRootUnits();
  }
}

export default UnitService;
