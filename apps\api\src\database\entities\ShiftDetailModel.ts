import { Document } from 'mongoose';
import { createSchema, createModel } from '@c-cam/core';
import { ShiftDetailAttributes } from '@c-cam/types';

/**
 * Shift Detail Document Interface
 * Extends the ShiftDetailAttributes (excluding id) and Document
 */
export interface ShiftDetailDocument
  extends Omit<ShiftDetailAttributes, 'id'>,
    Document {}

/**
 * Shift Detail Schema
 * Defines the MongoDB schema for shift details
 */
const ShiftDetailSchema = createSchema({
  shift_id: {
    type: String,
    ref: 'shift',
    required: true,
  },
  is_overnight: { type: <PERSON>olean, default: false },
  check_in_start_time: { type: String, required: true },
  late_threshold_time: { type: String, required: true },
  half_day_missed_start_time: { type: String, required: true },
  break_time: { type: String, required: false },
  check_out_start_time: { type: String, required: true },
  early_leave_threshold_time: { type: String, required: true },
  half_day_missed_end_time: { type: String, required: true },
  total_working_hours: { type: Number, required: true },
  check_in_required: { type: Boolean, default: true },
  flex_late_threshold_time: { type: String, required: false },
  flex_half_day_missed_time: { type: String, required: false },
});

// Add indexes
ShiftDetailSchema.index({ shift_id: 1 }, { unique: true });

// Create and export the model
const ShiftDetailModel = createModel<ShiftDetailDocument>(
  'shift_detail',
  ShiftDetailSchema,
);

export default ShiftDetailModel;
