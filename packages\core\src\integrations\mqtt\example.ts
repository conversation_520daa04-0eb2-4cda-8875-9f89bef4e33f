import {
  MqttConnection,
  MqttConnectionOptions,
  MqttClient,
  IMqttClient,
} from './index.js';

/**
 * Example of how to use the MQTT integration
 */
async function mqttExample() {
  // 1. Configure and initialize the MQTT connection
  const mqttOptions: MqttConnectionOptions = {
    url: 'mqtt://localhost:1883',
    clientId: 'example-client',
    // Authentication if needed
    // username: 'user',
    // password: 'pass',
    clean: true,
    keepalive: 60,
    reconnectPeriod: 1000,
    connectTimeout: 30000,
    qos: 0,
    retain: false,
  };

  // Get the MQTT connection manager
  const mqttConnection = MqttConnection.getInstance();

  // Initialize the connection
  await mqttConnection.initialize(mqttOptions);

  // 2. Create a client instance
  const client: IMqttClient = new MqttClient({
    topicPrefix: 'app',
    defaultQos: 0,
    defaultRetain: false,
  });

  // 3. Basic MQTT operations

  // Subscribe to a topic
  await client.subscribe('sensors/temperature');

  // Register a message handler
  client.onMessage('sensors/temperature', (topic, message) => {
    console.log(`Received message on topic ${topic}: ${message.toString()}`);
  });

  // Publish a message
  await client.publish('sensors/temperature', '25.5');

  // Publish an object (will be converted to JSON)
  await client.publish('sensors/data', {
    temperature: 25.5,
    humidity: 60,
    timestamp: new Date().toISOString(),
  });

  // Unsubscribe from a topic
  await client.unsubscribe('sensors/temperature');

  // 4. Advanced usage

  // Subscribe to a wildcard topic
  await client.subscribe('sensors/#');

  // Register a handler for the wildcard topic
  client.onMessage('sensors/#', (topic, message) => {
    console.log(
      `Received message on wildcard topic ${topic}: ${message.toString()}`,
    );
  });

  // Publish to different topics that match the wildcard
  await client.publish('sensors/humidity', '60');
  await client.publish('sensors/pressure', '1013');

  // Publish with specific QoS and retain options
  await client.publish('sensors/status', 'online', { qos: 1, retain: true });

  // 6. Close the connection when done
  await mqttConnection.close();
}

// This is just an example and won't be executed
// mqttExample().catch(console.error);

// Export the example function for documentation purposes
export { mqttExample };
