import { Context } from 'hono';
import { logger } from '@c-cam/logger';
import {
  PolicyRegistry,
  PolicyBase,
  PolicyEvaluationResult,
} from './policy.js';
import { Container } from '../di/container.js';

/**
 * Interface for dynamic policy conditions
 */
export interface DynamicPolicyCondition {
  effect: 'allow' | 'deny';
  resources: string[];
  actions: string[];
  conditions: Record<string, any>;
}

/**
 * Dynamic Policy class that evaluates policies based on data from the database
 */
export class DynamicPolicy extends PolicyBase {
  private policyConditions: DynamicPolicyCondition;

  constructor(name: string, policyConditions: DynamicPolicyCondition) {
    super(name);
    this.policyConditions = policyConditions;
  }

  async evaluate(
    context: Context,
    resource?: string | Record<string, any>,
    action?: string,
  ): Promise<PolicyEvaluationResult> {
    // Extract user information from context
    const user = context.get('user');
    if (!user) {
      return {
        allowed: false,
        reason: 'User not found in context',
      };
    }

    // Check if the resource matches
    const resourceStr = typeof resource === 'string' ? resource : undefined;
    const resourceMatches = this.matchResource(resourceStr);
    if (!resourceMatches) {
      return {
        allowed: false,
        reason: `Resource '${resourceStr}' does not match policy resources`,
      };
    }

    // Check if the action matches
    const actionMatches = this.matchAction(action);
    if (!actionMatches) {
      return {
        allowed: false,
        reason: `Action '${action}' does not match policy actions`,
      };
    }

    // Check conditions
    const conditionsMet = await this.evaluateConditions(context);
    if (!conditionsMet) {
      return {
        allowed: false,
        reason: 'Policy conditions not met',
      };
    }

    // Determine if access is allowed based on the effect
    const allowed = this.policyConditions.effect === 'allow';
    return {
      allowed,
      reason: allowed ? undefined : 'Policy denies access',
    };
  }

  private matchResource(resource?: string): boolean {
    if (!resource) return true;

    const resources = this.policyConditions.resources;
    // Allow all resources if wildcard is present
    if (resources.includes('*')) return true;

    return resources.some((r) => {
      // Exact match
      if (r === resource) return true;

      // Wildcard pattern match (e.g., "users/*" matches "users/123")
      if (r.endsWith('/*')) {
        const prefix = r.slice(0, -2);
        return resource.startsWith(prefix + '/');
      }

      return false;
    });
  }

  private matchAction(action?: string): boolean {
    if (!action) return true;

    const actions = this.policyConditions.actions;
    // Allow all actions if wildcard is present
    if (actions.includes('*')) return true;

    return actions.includes(action);
  }

  private async evaluateConditions(context: Context): Promise<boolean> {
    const conditions = this.policyConditions.conditions;
    if (!conditions || Object.keys(conditions).length === 0) {
      return true; // No conditions means all conditions are met
    }

    const user = context.get('user');

    // Time-based conditions
    if (conditions.timeRestrictions) {
      if (!this.evaluateTimeConditions(conditions.timeRestrictions)) {
        return false;
      }
    }

    // IP-based conditions
    if (conditions.ipRestrictions && conditions.ipRestrictions.length > 0) {
      const clientIp =
        context.req.header('x-forwarded-for') ||
        context.req.header('x-real-ip') ||
        'unknown';
      if (!conditions.ipRestrictions.includes(clientIp)) {
        return false;
      }
    }

    // User attribute conditions
    for (const [key, value] of Object.entries(conditions)) {
      if (key === 'timeRestrictions' || key === 'ipRestrictions') {
        continue; // Already handled
      }

      // Skip if the condition is not related to user attributes
      if (key.startsWith('_')) continue;

      // Check if user has the attribute and it matches the condition
      if (user[key] !== value) {
        return false;
      }
    }

    return true;
  }

  private evaluateTimeConditions(timeRestrictions: any): boolean {
    if (!timeRestrictions) return true;

    const now = new Date();
    const currentHour = now.getHours();
    const currentDay = now.getDay(); // 0 = Sunday, 1 = Monday, etc.

    // Check allowed hours
    if (timeRestrictions.allowedHours) {
      const { start, end } = timeRestrictions.allowedHours;
      if (currentHour < start || currentHour >= end) {
        return false;
      }
    }

    // Check allowed days
    if (
      timeRestrictions.allowedDays &&
      Array.isArray(timeRestrictions.allowedDays)
    ) {
      if (!timeRestrictions.allowedDays.includes(currentDay)) {
        return false;
      }
    }

    return true;
  }
}

/**
 * Service interface for loading dynamic policies
 */
export interface IDynamicPolicyService {
  loadPoliciesForUser(userId: string): Promise<DynamicPolicyCondition[]>;
}

/**
 * Dynamic Policy Loader
 * Handles loading policies from the database and registering them with the policy registry
 */
export class DynamicPolicyLoader {
  private static instance: DynamicPolicyLoader;
  private policyRegistry: PolicyRegistry;
  private loadedPolicies: Map<string, string[]> = new Map(); // userId -> policy names

  private constructor() {
    this.policyRegistry = PolicyRegistry.getInstance();
  }

  public static getInstance(): DynamicPolicyLoader {
    if (!DynamicPolicyLoader.instance) {
      DynamicPolicyLoader.instance = new DynamicPolicyLoader();
    }
    return DynamicPolicyLoader.instance;
  }

  /**
   * Load and register dynamic policies for a user
   * @param userId User ID
   * @returns Array of policy names that were loaded
   */
  async loadPoliciesForUser(userId: string): Promise<string[]> {
    try {
      // Try to find a policy service
      const policyService = this.findDynamicPolicyService();

      if (!policyService) {
        logger.warn('No dynamic policy service found');
        return [];
      }

      // Load policies from the service
      const policyConditions = await policyService.loadPoliciesForUser(userId);

      // Register each policy
      const policyNames: string[] = [];

      for (let i = 0; i < policyConditions.length; i++) {
        const condition = policyConditions[i];
        const policyName = `dynamic:user:${userId}:${i}`;

        // Skip undefined conditions
        if (!condition) continue;
        
        // Create and register the policy
        const policy = new DynamicPolicy(policyName, condition);
        this.policyRegistry.registerPolicy(policy);

        policyNames.push(policyName);
      }

      // Store the loaded policies for this user
      this.loadedPolicies.set(userId, policyNames);

      return policyNames;
    } catch (error) {
      logger.error('Error loading dynamic policies', { error, userId });
      return [];
    }
  }

  /**
   * Clear policies for a user
   * @param userId User ID
   */
  clearPoliciesForUser(userId: string): void {
    const policyNames = this.loadedPolicies.get(userId);

    if (policyNames) {
      // Remove each policy from the registry
      for (const name of policyNames) {
        this.policyRegistry.removePolicy(name);
      }

      // Remove from the loaded policies map
      this.loadedPolicies.delete(userId);
    }
  }

  /**
   * Find a service that can load dynamic policies
   */
  private findDynamicPolicyService(): IDynamicPolicyService | null {
    try {
      // Try to get a service with a specific name
      return Container.get('PolicyService') as IDynamicPolicyService;
    } catch (error) {
      // Try to find a service with the loadPoliciesForUser method
      const services: any[] = [];

      // This part would need to access the services registered in the container
      // The actual implementation would depend on how services are stored and accessed

      for (const service of services) {
        if (typeof service.loadPoliciesForUser === 'function') {
          return service as IDynamicPolicyService;
        }
      }

      return null;
    }
  }
}
