import TokenModel, { TokenDocument } from '@/database/entities/TokenModel';
import { Repository, Injectable } from '@c-cam/core';

/**
 * Repository for managing tokens
 * Extends the BaseRepository with TokenDocument type
 */
@Injectable()
class TokenRepository extends Repository<TokenDocument> {
  constructor() {
    super(TokenModel);
  }

  /**
   * Find a token by its value
   * @param token The token value
   * @returns A promise that resolves to a token document or null if not found
   */
  async findByToken(token: string): Promise<TokenDocument | null> {
    return this.findOne({ token, revoked: false });
  }

  /**
   * Find a token by its refresh token value
   * @param refreshToken The refresh token value
   * @returns A promise that resolves to a token document or null if not found
   */
  async findByRefreshToken(
    refreshToken: string,
  ): Promise<TokenDocument | null> {
    return this.findOne({ refresh_token: refreshToken, revoked: false });
  }

  /**
   * Revoke a token
   * @param token The token value
   * @returns A promise that resolves to true if the token was revoked, false otherwise
   */
  async revokeToken(token: string): Promise<boolean> {
    const result = await this.getModel().updateOne(
      { token },
      { revoked: true, revoked_at: new Date(), updated_at: new Date() },
    );
    return result.modifiedCount > 0;
  }

  /**
   * Revoke a refresh token
   * @param refreshToken The refresh token value
   * @returns A promise that resolves to true if the token was revoked, false otherwise
   */
  async revokeRefreshToken(refreshToken: string): Promise<boolean> {
    const result = await this.getModel().updateOne(
      { refresh_token: refreshToken },
      { revoked: true, revoked_at: new Date(), updated_at: new Date() },
    );
    return result.modifiedCount > 0;
  }

  /**
   * Mark token as compromised and revoke all related tokens
   * @param tokenId The token ID
   * @param userId The user ID
   * @returns A promise that resolves to the number of revoked tokens
   */
  async markTokenAsCompromised(
    tokenId: string,
    userId: string,
  ): Promise<number> {
    // Mark the specific token as compromised
    await this.getModel().updateOne(
      { _id: tokenId },
      {
        is_compromised: true,
        compromised_at: new Date(),
        revoked: true,
        revoked_at: new Date(),
        updated_at: new Date(),
      },
    );

    // Revoke all tokens in the same rotation chain
    const result = await this.getModel().updateMany(
      {
        $or: [{ parent_token_id: tokenId }, { _id: tokenId }],
        user_id: userId,
        revoked: false,
      },
      {
        revoked: true,
        revoked_at: new Date(),
        updated_at: new Date(),
      },
    );

    return result.modifiedCount;
  }

  /**
   * Find tokens by session ID
   * @param sessionId The session ID
   * @returns A promise that resolves to an array of token documents
   */
  async findBySessionId(sessionId: string): Promise<TokenDocument[]> {
    return this.find({ session_id: sessionId, revoked: false });
  }

  /**
   * Update token last used timestamp
   * @param token The token value
   * @returns A promise that resolves to true if updated, false otherwise
   */
  async updateLastUsed(token: string): Promise<boolean> {
    const result = await this.getModel().updateOne(
      { token, revoked: false },
      { last_used_at: new Date(), updated_at: new Date() },
    );
    return result.modifiedCount > 0;
  }

  /**
   * Check if refresh token is being reused (potential attack)
   * @param refreshToken The refresh token value
   * @returns A promise that resolves to true if token is being reused
   */
  async isRefreshTokenReused(refreshToken: string): Promise<boolean> {
    const token = await this.findOne({ refresh_token: refreshToken });
    return token ? token.revoked : false;
  }

  /**
   * Get active sessions for a user
   * @param userId The user ID
   * @returns A promise that resolves to an array of active sessions
   */
  async getActiveSessions(userId: string): Promise<TokenDocument[]> {
    return this.find({
      user_id: userId,
      revoked: false,
      is_compromised: false,
      refresh_expires_at: { $gt: new Date() },
    });
  }

  /**
   * Clean up expired tokens
   * @returns A promise that resolves to the number of cleaned tokens
   */
  async cleanupExpiredTokens(): Promise<number> {
    const result = await this.getModel().updateMany(
      {
        $or: [
          { expires_at: { $lt: new Date() } },
          { refresh_expires_at: { $lt: new Date() } },
        ],
        revoked: false,
      },
      {
        revoked: true,
        revoked_at: new Date(),
        updated_at: new Date(),
      },
    );
    return result.modifiedCount;
  }

  /**
   * Revoke all tokens for a user
   * @param userId The user ID
   * @returns A promise that resolves to the number of tokens revoked
   */
  async revokeAllUserTokens(userId: string): Promise<number> {
    const result = await this.getModel().updateMany(
      { user_id: userId, revoked: false },
      { revoked: true, revoked_at: new Date(), updated_at: new Date() },
    );
    return result.modifiedCount;
  }
}

export default TokenRepository;
