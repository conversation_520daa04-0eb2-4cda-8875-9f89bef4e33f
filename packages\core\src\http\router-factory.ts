import { Hono } from 'hono';
import { Context, NextFunction, RequestHandler } from './types.js';
import { Container } from '../di/container.js';
import { AppEnv } from '../hosting/interfaces.js';
import {
  AUTHORIZED_METADATA,
  handleAuthorization,
} from '../decorators/authorized.decorator.js';
import {
  ERROR_HANDLER_METADATA,
  ErrorHandlerMetadata,
} from '../decorators/error-handling.decorator.js';
import {
  IMiddleware,
  MIDDLEWARE_METADATA,
} from '../decorators/middleware.decorator.js';
import {
  ParamMetadata,
  PARAMS_METADATA,
} from '../decorators/parameter.decorator.js';
import {
  ROUTES_METADATA,
  RouteMetadata,
} from '../decorators/http-method.decorator.js';
import { PATH_METADATA } from '../decorators/controller.decorator.js';
import { logger } from '@c-cam/logger';

/**
 * Factory class for creating Hono routes from decorated controllers
 */
export class RouterFactory {
  /**
   * Create routes from registered controllers
   * @param register Function to register controllers with DI container
   * @returns Configured Hono app
   */
  static createRoutes(register: () => void): Hono<AppEnv> {
    register();
    const app = new Hono<AppEnv>();

    // Find all registered controllers
    const controllers = Container.filter((instance: any) =>
      Reflect.hasMetadata(PATH_METADATA, instance.constructor),
    );

    // Process each controller
    controllers.forEach((controller: any) => {
      const controllerInstance = controller;
      const controllerClass = controller.constructor;

      // Get base path from controller
      const basePath =
        Reflect.getMetadata(PATH_METADATA, controllerClass) || '';

      // Get all routes from controller
      const routes: RouteMetadata[] =
        Reflect.getMetadata(ROUTES_METADATA, controllerClass) || [];

      // Get middleware of controller
      const controllerMiddlewares =
        Reflect.getMetadata(MIDDLEWARE_METADATA, controllerClass) || [];

      // Get authorization metadata of controller
      const controllerAuthOptions = Reflect.getMetadata(
        AUTHORIZED_METADATA,
        controllerClass,
      );

      // If controller has @Authorized decorator, add authorization middleware
      if (controllerAuthOptions) {
        app.use(basePath + '/*', async (c: Context, next: NextFunction) => {
          await handleAuthorization(c, next, controllerAuthOptions);
          return;
        });
      }

      // Apply controller middlewares
      if (controllerMiddlewares.length > 0) {
        // Create a single middleware to handle all controller middlewares
        app.use(basePath + '/*', async (c: Context, next: NextFunction) => {
          // Execute each middleware in sequence
          for (const middleware of controllerMiddlewares) {
            const middlewareHandler =
              typeof middleware === 'function'
                ? middleware
                : (middleware as IMiddleware).use?.bind(middleware);

            if (middlewareHandler) {
              await middlewareHandler(c, next);
            }
          }
          await next();
        });
      }

      // Get error handlers from controller and base classes
      const errorHandlers = getAllErrorHandlers(controllerClass);

      // Register each route
      routes.forEach((route) => {
        const { path, method, handlerName } = route;
        const fullPath = `${basePath}${path === '/' ? '' : path}`;

        // Get method middleware
        const methodMiddlewares =
          Reflect.getMetadata(
            MIDDLEWARE_METADATA,
            controllerInstance,
            handlerName,
          ) || [];

        // Get authorization metadata for method
        const methodAuthOptions = Reflect.getMetadata(
          AUTHORIZED_METADATA,
          controllerInstance,
          handlerName,
        );

        // Apply method-level authorization if present
        if (methodAuthOptions) {
          app.use(fullPath, async (c: Context, next: NextFunction) => {
            await handleAuthorization(c, next, methodAuthOptions);
            return;
          });
        }

        // Apply method middlewares
        if (methodMiddlewares.length > 0) {
          // Create a single middleware to handle all method middlewares
          app.use(fullPath, async (c: Context, next: NextFunction) => {
            // Execute each middleware in sequence
            for (const middleware of methodMiddlewares) {
              const middlewareHandler =
                typeof middleware === 'function'
                  ? middleware
                  : (middleware as IMiddleware).use?.bind(middleware);

              if (middlewareHandler) {
                await middlewareHandler(c, next);
              }
            }
            await next();
          });
        }

        // Get parameter metadata
        const parameters: ParamMetadata[] =
          Reflect.getMetadata(
            PARAMS_METADATA,
            controllerInstance,
            handlerName,
          ) || [];

        // Create route handler
        const routeHandler = createRouteHandler(
          controllerInstance,
          handlerName,
          parameters,
          errorHandlers,
        );

        // Register route with Hono based on HTTP method
        switch (method.toLowerCase()) {
          case 'get':
            app.get(fullPath, routeHandler);
            break;
          case 'post':
            app.post(fullPath, routeHandler);
            break;
          case 'put':
            app.put(fullPath, routeHandler);
            break;
          case 'delete':
            app.delete(fullPath, routeHandler);
            break;
          case 'patch':
            app.patch(fullPath, routeHandler);
            break;
          case 'options':
            app.options(fullPath, routeHandler);
            break;
          default:
            // For other methods, use the .on method
            app.on(method.toUpperCase(), fullPath, routeHandler);
            break;
        }

        logger.info(`Registered route: ${method.toUpperCase()} ${fullPath}`);
      });
    });

    return app;
  }
}
/**
 * Get all error handlers from a class and its base classes
 * @param targetClass Class to get error handlers from
 * @returns Map of error handlers
 */
function getAllErrorHandlers(targetClass: any): Map<any, Function> {
  const controllerClass = targetClass;
  const errorHandlers = new Map<any, Function>();
  let currentClass = targetClass;

  // Traverse the prototype chain to collect error handlers
  while (currentClass && currentClass.prototype) {
    const handlers: ErrorHandlerMetadata[] =
      Reflect.getMetadata(ERROR_HANDLER_METADATA, currentClass) || [];

    handlers.forEach((handler) => {
      if (!errorHandlers.has(handler.errorType)) {
        // Extract the actual handler function using the handlerName from the metadata
        const handlerFn = controllerClass.prototype[handler.handlerName];
        if (handlerFn) {
          errorHandlers.set(handler.errorType, handlerFn);
        }
      }
    });

    // Move up to the base class
    currentClass = Object.getPrototypeOf(currentClass);
  }

  return errorHandlers;
}

/**
 * Create a route handler for a controller method
 * @param controllerInstance Controller instance
 * @param handlerName Name of the handler method
 * @param parameters Parameter metadata
 * @param errorHandlers Error handlers
 * @returns Route handler function
 */
function createRouteHandler(
  controllerInstance: any,
  handlerName: string | symbol,
  parameters: ParamMetadata[],
  errorHandlers: Map<any, Function>,
): RequestHandler {
  return async (c: Context) => {
    try {
      // Prepare parameters for the handler method
      const args: any[] = [];

      if (parameters.length > 0) {
        // Sort parameters by index (do this once on startup instead of on each request)
        const sortedParams = [...parameters].sort((a, b) => a.index - b.index);

        // Create parameter array based on decorators
        for (const param of sortedParams) {
          switch (param.type) {
            case 'http_context':
              args[param.index] = c;
              break;
            case 'body':
              try {
                const body = await c.req.json();
                args[param.index] = param.validator
                  ? param.validator(body)
                  : body;
              } catch (e) {
                args[param.index] = param.validator ? param.validator({}) : {};
              }
              break;
            case 'param':
              const paramValue = param.name
                ? c.req.param(param.name)
                : c.req.param();
              args[param.index] = param.validator
                ? param.validator(paramValue)
                : paramValue;
              break;
            case 'query':
              const queryValue = param.name
                ? c.req.query(param.name)
                : c.req.query();
              args[param.index] = param.validator
                ? param.validator(queryValue)
                : queryValue;
              break;
            case 'headers':
              const headerName = param.name?.toLowerCase() || '';
              const headerValue = headerName
                ? c.req.header(headerName)
                : c.req.header();
              args[param.index] = param.validator
                ? param.validator(headerValue)
                : headerValue;
              break;

            case 'next':
              args[param.index] = () => Promise.resolve();
              break;
            default:
              args[param.index] = undefined;
          }
        }
      }

      // Call the controller method with the prepared parameters
      const result = await controllerInstance[handlerName].apply(
        controllerInstance,
        args,
      );

      // Handle different types of responses
      if (result === undefined || result === null) {
        return c.body(null, 204);
      } else if (result instanceof Response) {
        return result;
      } else if (typeof result === 'string') {
        return c.text(result);
      } else {
        return c.json(result);
      }
    } catch (error: any) {
      // Find and execute appropriate error handler
      for (const [ErrorType, handler] of errorHandlers.entries()) {
        if (error instanceof ErrorType) {
          try {
            const result = handler.call(controllerInstance, error, c);

            // Handle async error handlers
            if (result && typeof result.then === 'function') {
              return await result;
            }

            return result;
          } catch (handlerError: any) {
            // If error handler itself throws, log it and rethrow the original error
            logger.error(
              `Error handler for ${ErrorType.name} failed:`,
              handlerError,
            );
            throw error; // Rethrow original error, not handler error
          }
        }
      }

      // If no handler is found, rethrow the error to be caught by global error middleware
      throw error;
    }
  };
}
