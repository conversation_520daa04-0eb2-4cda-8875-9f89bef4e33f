import { logger } from '@c-cam/logger';
import { Context } from 'hono';

/**
 * Interface for policy evaluation result
 */
export interface PolicyEvaluationResult {
  allowed: boolean;
  reason?: string;
}

/**
 * Interface for authorization policy
 */
export interface IPolicy {
  /**
   * Name of the policy
   */
  name: string;

  /**
   * Evaluate if the policy allows the action
   * @param context Hono context
   * @param resource Optional resource being accessed
   * @param action Optional action being performed
   * @returns Policy evaluation result
   */
  evaluate(
    context: Context,
    resource?: string | Record<string, any>,
    action?: string,
  ): Promise<PolicyEvaluationResult> | PolicyEvaluationResult;
}

/**
 * Abstract base class for implementing policies
 */
export abstract class PolicyBase implements IPolicy {
  constructor(public name: string) {}

  abstract evaluate(
    context: Context,
    resource?: string | Record<string, any>,
    action?: string,
  ): Promise<PolicyEvaluationResult> | PolicyEvaluationResult;
}

/**
 * Policy that combines multiple policies using AND logic
 */
export class AllOfPolicy extends PolicyBase {
  constructor(private policies: IPolicy[]) {
    super('AllOfPolicy');
  }

  async evaluate(
    context: Context,
    resource?: string | Record<string, any>,
    action?: string,
  ): Promise<PolicyEvaluationResult> {
    for (const policy of this.policies) {
      const result = await policy.evaluate(context, resource, action);
      if (!result.allowed) {
        return {
          allowed: false,
          reason: `Policy '${policy.name}' denied access: ${result.reason || 'No reason provided'}`,
        };
      }
    }
    return { allowed: true };
  }
}

/**
 * Policy that combines multiple policies using OR logic
 */
export class AnyOfPolicy extends PolicyBase {
  constructor(private policies: IPolicy[]) {
    super('AnyOfPolicy');
  }

  async evaluate(
    context: Context,
    resource?: string | Record<string, any>,
    action?: string,
  ): Promise<PolicyEvaluationResult> {
    const reasons: string[] = [];

    for (const policy of this.policies) {
      const result = await policy.evaluate(context, resource, action);
      if (result.allowed) {
        return { allowed: true };
      }
      if (result.reason) {
        reasons.push(`Policy '${policy.name}': ${result.reason}`);
      }
    }

    return {
      allowed: false,
      reason:
        reasons.length > 0 ? reasons.join('; ') : 'All policies denied access',
    };
  }
}

/**
 * Role-based policy that checks if the user has any of the required roles
 */
export class RolePolicy extends PolicyBase {
  constructor(private roles: string[]) {
    super('RolePolicy');
  }

  async evaluate(context: Context): Promise<PolicyEvaluationResult> {
    const claimsPrincipal = context.get('claimsPrincipal');

    // Check if claimsPrincipal exists
    if (!claimsPrincipal) {
      return {
        allowed: false,
        reason: 'No claims principal found in context',
      };
    }

    // Check if user has any of the required roles
    const hasRole = this.roles.some((role) => claimsPrincipal.isInRole(role));

    if (hasRole) {
      return { allowed: true };
    }

    // Try to get userId to check roles in database
    const user = context.get('user');
    if (!user || !user.id) {
      return {
        allowed: false,
        reason: 'User has none of the required roles',
      };
    }

    // Check roles in database
    try {
      const hasRoleInDb = await checkUserRoles(user.id, this.roles);
      return {
        allowed: hasRoleInDb,
        reason: hasRoleInDb
          ? undefined
          : `User does not have any of the required roles: ${this.roles.join(', ')}`,
      };
    } catch (error) {
      return {
        allowed: false,
        reason: 'Error checking user roles',
      };
    }
  }
}

/**
 * Helper function to check if a user has any of the required roles
 * This function is imported from the authorized.decorator.ts file
 */
// This is a placeholder - the actual implementation will need to be exported from authorized.decorator.ts
async function checkUserRoles(
  userId: string,
  requiredRoles: string[],
): Promise<boolean> {
  // Implementation will be provided from authorized.decorator.ts
  logger.info(userId, requiredRoles);
  return false;
}

/**
 * Resource ownership policy that checks if the user owns the resource
 */
export class OwnershipPolicy extends PolicyBase {
  constructor(
    private resourceIdExtractor: (
      context: Context,
      resource?: any,
    ) => string | undefined,
    private userIdExtractor: (context: Context) => string | undefined = (ctx) =>
      ctx.get('user')?.id,
  ) {
    super('OwnershipPolicy');
  }

  async evaluate(
    context: Context,
    resource?: string | Record<string, any>,
  ): Promise<PolicyEvaluationResult> {
    const userId = this.userIdExtractor(context);
    if (!userId) {
      return {
        allowed: false,
        reason: 'User ID not found in context',
      };
    }

    const resourceId = this.resourceIdExtractor(context, resource);
    if (!resourceId) {
      return {
        allowed: false,
        reason: 'Resource ID could not be extracted',
      };
    }

    // In this simple implementation, we check if the user ID is part of the resource ID
    // In a real implementation, you would query a database or other source
    // to determine if the user owns the resource
    const isOwner = await this.checkOwnership(userId, resourceId);

    return {
      allowed: isOwner,
      reason: isOwner ? undefined : 'User is not the owner of this resource',
    };
  }

  /**
   * Placeholder method to check ownership
   * This should be overridden in subclasses with actual implementation
   */
  protected async checkOwnership(
    userId: string,
    resourceId: string,
  ): Promise<boolean> {
    // Override this method in subclasses to implement actual ownership checking
    logger.info(userId, resourceId);
    return false;
  }
}

/**
 * Policy registry to manage and retrieve policies
 */
export class PolicyRegistry {
  private static instance: PolicyRegistry;
  private policies = new Map<string, IPolicy>();

  private constructor() {}

  public static getInstance(): PolicyRegistry {
    if (!PolicyRegistry.instance) {
      PolicyRegistry.instance = new PolicyRegistry();
    }
    return PolicyRegistry.instance;
  }

  /**
   * Register a policy
   * @param policy Policy to register
   */
  registerPolicy(policy: IPolicy): void {
    this.policies.set(policy.name, policy);
  }

  /**
   * Get a policy by name
   * @param name Name of the policy
   * @returns The policy or undefined if not found
   */
  getPolicy(name: string): IPolicy | undefined {
    return this.policies.get(name);
  }

  /**
   * Check if a policy exists
   * @param name Name of the policy
   * @returns True if the policy exists
   */
  hasPolicy(name: string): boolean {
    return this.policies.has(name);
  }

  /**
   * Remove a policy
   * @param name Name of the policy to remove
   */
  removePolicy(name: string): void {
    this.policies.delete(name);
  }

  /**
   * Get all registered policies
   * @returns Array of all registered policies
   */
  getAllPolicies(): IPolicy[] {
    return Array.from(this.policies.values());
  }
}
