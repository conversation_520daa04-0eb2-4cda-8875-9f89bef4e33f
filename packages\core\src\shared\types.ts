/**
 * Shared type definitions used across multiple modules
 * This file helps avoid naming conflicts in exports by using namespaces
 */

import { Context, Next } from 'hono';
import { IHttpContext } from '../hosting/interfaces.js';
import { HttpMethod } from './http-method.js';

/**
 * Shared namespace for middleware types
 * This prevents naming conflicts when modules are exported
 */
export namespace Middleware {
    /**
     * Base middleware function type that can be extended for specific use cases
     */
    export type Function = (context: any, next: any) => Promise<void> | void;

    /**
     * HTTP-specific middleware function implementation
     */
    export type HttpFunction = (c: Context, next: Next) => Promise<void> | void;

    /**
     * DI-specific middleware function implementation
     */
    export type DIFunction = (context: IHttpContext, next: () => Promise<void>) => Promise<void>;
}

/**
 * Shared namespace for HTTP types
 */
export namespace Http {
    /**
     * Common HTTP method enum used across the application
     */
    export type Method = (typeof HttpMethod)[keyof typeof HttpMethod];

    /**
     * Common HTTP result interface for standardized API responses
     */
    export interface Result<T = unknown> {
        success: boolean;
        message?: string;
        data?: T;
        error?: {
            code: string;
            details?: unknown[];
        };
    }
}
