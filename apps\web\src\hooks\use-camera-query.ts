import { useApiQuery } from '@/shared/hooks/use-api-query'

// Types for camera data
export interface Camera {
  id: string
  name: string
  location?: string
  status?: string
  created_at?: string
  updated_at?: string
  created_by?: string
}

export interface CameraQueryParams {
  limit?: number
  skip?: number
  sortBy?: string
  sortDirection?: 'asc' | 'desc'
}

export interface CameraListResponse {
  cameras: Array<Camera>
}

export interface CameraResponse {
  camera: Camera
}

/**
 * Hook to fetch all cameras with optional pagination and sorting
 */
export const useCamerasQuery = (params?: CameraQueryParams) => {
  return useApiQuery<CameraListResponse>(
    ['cameras', ...(params ? [JSON.stringify(params)] : [])],
    '/api/cameras',
    params
  )
}

/**
 * Hook to fetch a single camera by ID
 */
export const useCameraQuery = (id: string, enabled = true) => {
  return useApiQuery<CameraResponse>(
    ['cameras', id],
    `/api/cameras/${id}`,
    undefined,
    { enabled: enabled && !!id }
  )
}

/**
 * Hook to fetch cameras by location
 */
export const useCamerasByLocationQuery = (location: string, enabled = true) => {
  return useApiQuery<CameraListResponse>(
    ['cameras', 'location', location],
    `/api/cameras/location/${location}`,
    undefined,
    { enabled: enabled && !!location }
  )
}

/**
 * Hook to fetch cameras by status
 */
export const useCamerasByStatusQuery = (status: string, enabled = true) => {
  return useApiQuery<CameraListResponse>(
    ['cameras', 'status', status],
    `/api/cameras/status/${status}`,
    undefined,
    { enabled: enabled && !!status }
  )
}

/**
 * Hook to fetch cameras created by a specific user
 */
export const useCamerasByCreatorQuery = (createdBy: string, enabled = true) => {
  return useApiQuery<CameraListResponse>(
    ['cameras', 'created-by', createdBy],
    `/api/cameras/created-by/${createdBy}`,
    undefined,
    { enabled: enabled && !!createdBy }
  )
}
