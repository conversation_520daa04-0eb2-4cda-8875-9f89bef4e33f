import { Context } from '../http/types.js';
import { StatusCode } from 'hono/utils/http-status';
import { APIResponse } from '@c-cam/types';

/**
 * Utility functions for handling API responses
 */
export class ResponseUtils {
  /**
   * Format a success response
   * @param data The data to include in the response
   * @param message Success message
   * @param statusCode HTTP status code
   * @returns Formatted API response object
   */
  static formatSuccess<T>(
    data?: T,
    message = 'Success',
    statusCode: StatusCode = 200,
  ): APIResponse<T> {
    return {
      code: 'SUCCESS',
      success: true,
      statusCode,
      message,
      data,
    };
  }

  /**
   * Format an error response
   * @param message Error message
   * @param statusCode HTTP status code
   * @param code Error code
   * @param details Additional error details
   * @returns Formatted API error response object
   */
  static formatError(
    message: string,
    statusCode: StatusCode = 400,
    code = 'BAD_REQUEST',
    details?: any[],
  ): APIResponse {
    return {
      success: false,
      statusCode,
      message,
      code,
      error: { code, details },
      errors: details, // Keep for backward compatibility
    };
  }

  /**
   * Send a text response
   * @param c Hono context
   * @param text Text to send
   * @param statusCode HTTP status code
   */
  static sendText(c: Context, text?: string, statusCode: StatusCode = 200) {
    // Set the status code
    c.status(statusCode);
    return c.text(text || '');
  }

  /**
   * Send a success response
   * @param c Hono context
   * @param data Data to include in the response
   * @param message Success message
   * @param statusCode HTTP status code
   */
  static sendSuccess<T>(
    c: Context,
    data?: T,
    message = 'Success',
    statusCode: StatusCode = 200,
  ) {
    const response = this.formatSuccess(data, message, statusCode);
    // Set the status code
    c.status(statusCode);
    return c.json(response);
  }

  /**
   * Send a created success response (201)
   * @param c Hono context
   * @param data Data to include in the response
   * @param message Success message
   */
  static sendCreated<T>(
    c: Context,
    data?: T,
    message = 'Resource created successfully',
  ) {
    return this.sendSuccess(c, data, message, 201);
  }

  /**
   * Send an error response
   * @param c Hono context
   * @param message Error message
   * @param statusCode HTTP status code
   * @param code Error code
   * @param details Additional error details
   */
  static sendError(
    c: Context,
    message: string,
    statusCode: StatusCode = 400,
    code = 'BAD_REQUEST',
    details?: any[],
  ) {
    const response = this.formatError(message, statusCode, code, details);
    // Set the status code
    c.status(statusCode);
    return c.json(response);
  }

  /**
   * Transform data before sending response (remove sensitive fields)
   * @param data Data to transform
   * @returns Transformed data
   */
  static transformData<T>(data: T): T {
    if (data === null || data === undefined) {
      return data;
    }

    // Remove sensitive fields
    const sensitiveFields = ['password', 'token', 'secret', 'refreshToken'];

    if (Array.isArray(data)) {
      return data.map((item) => this.transformData(item)) as unknown as T;
    }

    if (typeof data === 'object' && data !== null) {
      const transformed = { ...data } as any;
      sensitiveFields.forEach((field) => {
        if (field in transformed) {
          delete transformed[field];
        }
      });
      return transformed as T;
    }

    return data;
  }
}
