type Props = {
  children: React.ReactNode
}

export default function AuthLayout({ children }: Props) {
  return (
    <div className="h-dvh flex items-center justify-center bg-[#343f4a] overflow-hidden">
      <div className="flex flex-col items-center gap-2.5 p-12 rounded-lg bg-white">
        <div className="flex flex-col items-center gap-6 w-[354px]">
          <div className="flex flex-col items-center gap-2">
            <svg
              width={52}
              height={35}
              viewBox="0 0 52 35"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
              className="flex-grow-0 flex-shrink-0 w-[51.51px] h-[34px]"
              preserveAspectRatio="none"
            >
              <path
                d="M15.0463 29.139C14.9948 29.7378 14.6383 30.0871 14.3385 30.2743C13.3905 30.7433 12.3276 31.0119 11.2 31.0119C7.25666 31.0119 4.06068 27.8093 4.06068 23.8585C4.06068 19.9077 7.25666 16.7051 11.2 16.7051C11.8773 16.7051 12.5323 16.8037 13.1535 16.9803V13.0613C12.5184 12.9475 11.8661 12.8833 11.2 12.8833C5.15065 12.8833 0.246094 17.7967 0.246094 23.8585C0.246094 29.9203 5.14999 34.8337 11.2 34.8337C13.9272 34.8337 16.4769 33.756 18.0033 32.4118C18.0983 32.3072 18.204 32.2107 18.3386 31.993C18.3386 31.993 18.4086 31.8819 18.4099 31.8812C18.4535 31.8098 18.5466 31.649 18.5558 31.6278C18.6291 31.4677 18.6945 31.2891 18.746 31.0827C18.746 31.0774 18.748 31.0721 18.7493 31.0668C18.7546 31.0457 18.7565 31.0205 18.7618 31C18.7691 30.961 18.7757 30.9193 18.7836 30.8809C18.7928 30.8181 18.8021 30.7552 18.8107 30.6884C18.8173 30.6328 18.8285 30.4668 18.8338 30.4092V26.3817L15.0469 20.908V29.139H15.0463Z"
                fill="#008FD3"
              />
              <path
                d="M36.9527 13.5708C36.1875 13.8718 34.3872 14.7365 32.9737 16.6417C32.9697 16.6384 32.9651 16.6345 32.9625 16.6325L25.9994 26.6742L19.0371 16.6325C19.0331 16.6345 19.0298 16.6384 19.0258 16.6417C17.6124 14.7365 15.812 13.8725 15.0469 13.5708V17.5818L25.9994 33.3804L36.9533 17.5818V13.5708H36.9527Z"
                fill="#008FD3"
              />
              <path
                d="M40.7998 12.8833C40.1317 12.8833 39.4801 12.9468 38.8463 13.0619V16.9803C39.4675 16.8037 40.1224 16.7051 40.7998 16.7051C44.7425 16.7051 47.9385 19.9077 47.9385 23.8592C47.9385 27.81 44.7425 31.0113 40.7998 31.0113C39.6709 31.0113 38.608 30.7427 37.6599 30.2749C37.3615 30.0864 37.0044 29.7384 36.9529 29.1384V20.9073L33.166 26.381V30.4092C33.1713 30.4668 33.1819 30.6328 33.1891 30.6897C33.1964 30.7559 33.2056 30.8187 33.2162 30.8809C33.2235 30.92 33.2301 30.9616 33.238 31C33.2419 31.0212 33.2452 31.0463 33.2505 31.0668C33.2518 31.0721 33.2525 31.0774 33.2538 31.0827C33.304 31.2891 33.37 31.4677 33.442 31.6278C33.4525 31.649 33.5463 31.8098 33.5905 31.8812C33.5905 31.8825 33.6618 31.993 33.6618 31.993C33.7952 32.2107 33.9021 32.3066 33.9959 32.4118C35.5222 33.7561 38.0719 34.835 40.8005 34.835C46.8491 34.835 51.7544 29.921 51.7544 23.8598C51.7537 17.7967 46.8485 12.8833 40.7998 12.8833Z"
                fill="#008FD3"
              />
              <path
                d="M19.0396 13.3298C18.9333 12.8402 18.8759 12.3328 18.8759 11.8115C18.8759 7.86929 22.0653 4.67397 26 4.67397C29.9347 4.67397 33.1241 7.86929 33.1241 11.8115C33.1241 12.3328 33.0667 12.8402 32.9611 13.3298C34.1336 12.4717 35.4836 11.8433 36.9453 11.5059C36.7836 5.58493 31.947 0.834961 26 0.834961C20.053 0.834961 15.2164 5.58493 15.0547 11.5059C16.5157 11.8433 17.8658 12.4717 19.0396 13.3298Z"
                fill="#008FD3"
              />
            </svg>
            <div className="flex justify-center items-center flex-grow-0 flex-shrink-0 relative">
              <p className="flex-grow-0 flex-shrink-0 text-2xl font-bold text-left text-[#008fd3]">
                C-CAM
              </p>
            </div>
          </div>
          {children}
        </div>
      </div>
    </div>
  )
}
