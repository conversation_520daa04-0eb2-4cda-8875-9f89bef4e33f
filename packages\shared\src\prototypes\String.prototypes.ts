/**
 * String prototype extensions for improved string handling
 */

declare global {
  interface String {
    /**
     * Check if string is null, undefined, or empty
     */
    isNullOrEmpty(): boolean;

    /**
     * Check if string is null, undefined, empty, or whitespace only
     */
    isNullOrWhitespace(): boolean;

    /**
     * Capitalize the first letter of the string
     */
    capitalize(): string;

    /**
     * Convert string to camelCase
     */
    toCamelCase(): string;

    /**
     * Convert string to PascalCase
     */
    toPascalCase(): string;

    /**
     * Convert string to kebab-case
     */
    toKebabCase(): string;

    /**
     * Convert string to snake_case
     */
    toSnakeCase(): string;

    /**
     * Convert string to Title Case
     */
    toTitleCase(): string;

    /**
     * Truncate string to specified length with optional suffix
     * @param length Maximum length
     * @param suffix Suffix to append (default: '...')
     */
    truncate(length: number, suffix?: string): string;

    /**
     * Remove all whitespace from string
     */
    removeWhitespace(): string;

    /**
     * Remove extra whitespace and normalize spaces
     */
    normalizeWhitespace(): string;

    /**
     * Check if string contains only digits
     */
    isNumeric(): boolean;

    /**
     * Check if string is a valid email address
     */
    isEmail(): boolean;

    /**
     * Check if string is a valid URL
     */
    isUrl(): boolean;

    /**
     * Check if string is a valid UUID
     */
    isUuid(): boolean;

    /**
     * Check if string is a valid JSON
     */
    isJson(): boolean;

    /**
     * Parse string as JSON safely
     * @param defaultValue Default value if parsing fails
     */
    parseJson<T>(defaultValue?: T): T | null;

    /**
     * Escape HTML characters
     */
    escapeHtml(): string;

    /**
     * Unescape HTML characters
     */
    unescapeHtml(): string;

    /**
     * Remove HTML tags from string
     */
    stripHtml(): string;

    /**
     * Convert string to slug (URL-friendly)
     */
    toSlug(): string;

    /**
     * Reverse the string
     */
    reverse(): string;

    /**
     * Count occurrences of substring
     * @param substring Substring to count
     * @param caseSensitive Whether to match case (default: true)
     */
    countOccurrences(substring: string, caseSensitive?: boolean): number;

    /**
     * Insert string at specified position
     * @param index Position to insert at
     * @param value String to insert
     */
    insertAt(index: number, value: string): string;

    /**
     * Remove substring from string
     * @param substring Substring to remove
     * @param all Remove all occurrences (default: false)
     */
    remove(substring: string, all?: boolean): string;

    /**
     * Replace multiple substrings at once
     * @param replacements Object with search-replace pairs
     */
    replaceMultiple(replacements: Record<string, string>): string;

    /**
     * Check if string starts with any of the provided prefixes
     * @param prefixes Array of prefixes to check
     */
    startsWithAny(prefixes: string[]): boolean;

    /**
     * Check if string ends with any of the provided suffixes
     * @param suffixes Array of suffixes to check
     */
    endsWithAny(suffixes: string[]): boolean;

    /**
     * Extract numbers from string
     */
    extractNumbers(): number[];

    /**
     * Extract words from string (alphanumeric sequences)
     */
    extractWords(): string[];

    /**
     * Mask string with specified character
     * @param maskChar Character to use for masking (default: '*')
     * @param visibleStart Number of characters to show at start (default: 0)
     * @param visibleEnd Number of characters to show at end (default: 0)
     */
    mask(maskChar?: string, visibleStart?: number, visibleEnd?: number): string;

    /**
     * Generate hash of the string
     * @param algorithm Hash algorithm (default: 'sha256')
     */
    hash(algorithm?: string): string;

    /**
     * Encode string to Base64
     */
    toBase64(): string;

    /**
     * Decode string from Base64
     */
    fromBase64(): string;

    /**
     * Convert string to array of characters
     */
    toCharArray(): string[];

    /**
     * Get string similarity ratio with another string (0-1)
     * @param other String to compare with
     */
    similarity(other: string): number;

    /**
     * Check if string matches pattern (supports wildcards * and ?)
     * @param pattern Pattern to match against
     */
    matchesPattern(pattern: string): boolean;

    /**
     * Wrap string with specified characters
     * @param wrapper Character(s) to wrap with
     */
    wrap(wrapper: string): string;

    /**
     * Unwrap string by removing specified characters from start and end
     * @param wrapper Character(s) to remove
     */
    unwrap(wrapper: string): string;

    /**
     * Pad string to center it within specified length
     * @param length Target length
     * @param padChar Character to pad with (default: ' ')
     */
    padCenter(length: number, padChar?: string): string;

    /**
     * Convert string to boolean
     * Recognizes: true/false, yes/no, 1/0, on/off (case insensitive)
     */
    toBoolean(): boolean;

    /**
     * Format string using template literals syntax
     * @param values Object with replacement values
     */
    format(values: Record<string, any>): string;

    /**
     * Split string into chunks of specified size
     * @param size Chunk size
     */
    chunk(size: number): string[];

    /**
     * Repeat string specified number of times with optional separator
     * @param count Number of repetitions
     * @param separator Separator between repetitions (default: '')
     */
    repeatWith(count: number, separator?: string): string;
  }
}

// Implementation
String.prototype.isNullOrEmpty = function(): boolean {
  return this == null || this.length === 0;
};

String.prototype.isNullOrWhitespace = function(): boolean {
  return this == null || this.trim().length === 0;
};

String.prototype.capitalize = function(): string {
  if (this.length === 0) return this.toString();
  return this.charAt(0).toUpperCase() + this.slice(1).toLowerCase();
};

String.prototype.toCamelCase = function(): string {
  return this.toString()
    .replace(/(?:^\w|[A-Z]|\b\w)/g, (word, index) => {
      return index === 0 ? word.toLowerCase() : word.toUpperCase();
    })
    .replace(/\s+/g, '')
    .replace(/[^a-zA-Z0-9]/g, '');
};

String.prototype.toPascalCase = function(): string {
  return this.toString()
    .replace(/(?:^\w|[A-Z]|\b\w)/g, (word) => {
      return word.toUpperCase();
    })
    .replace(/\s+/g, '')
    .replace(/[^a-zA-Z0-9]/g, '');
};

String.prototype.toKebabCase = function(): string {
  return this.toString()
    .replace(/([a-z])([A-Z])/g, '$1-$2')
    .replace(/[\s_]+/g, '-')
    .toLowerCase()
    .replace(/[^a-z0-9-]/g, '');
};

String.prototype.toSnakeCase = function(): string {
  return this.toString()
    .replace(/([a-z])([A-Z])/g, '$1_$2')
    .replace(/[\s-]+/g, '_')
    .toLowerCase()
    .replace(/[^a-z0-9_]/g, '');
};

String.prototype.toTitleCase = function(): string {
  return this.toString()
    .toLowerCase()
    .replace(/\b\w/g, (char) => char.toUpperCase());
};

String.prototype.truncate = function(length: number, suffix: string = '...'): string {
  if (this.length <= length) return this.toString();
  return this.substring(0, length - suffix.length) + suffix;
};

String.prototype.removeWhitespace = function(): string {
  return this.replace(/\s/g, '');
};

String.prototype.normalizeWhitespace = function(): string {
  return this.replace(/\s+/g, ' ').trim();
};

String.prototype.isNumeric = function(): boolean {
  return /^\d+$/.test(this.toString());
};

String.prototype.isEmail = function(): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(this.toString());
};

String.prototype.isUrl = function(): boolean {
  try {
    new URL(this.toString());
    return true;
  } catch {
    return false;
  }
};

String.prototype.isUuid = function(): boolean {
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  return uuidRegex.test(this.toString());
};

String.prototype.isJson = function(): boolean {
  try {
    JSON.parse(this.toString());
    return true;
  } catch {
    return false;
  }
};

String.prototype.parseJson = function<T>(defaultValue: T | null = null): T | null {
  try {
    return JSON.parse(this.toString()) as T;
  } catch {
    return defaultValue;
  }
};

String.prototype.escapeHtml = function(): string {
  const htmlEscapes: Record<string, string> = {
    '&': '&amp;',
    '<': '&lt;',
    '>': '&gt;',
    '"': '&quot;',
    "'": '&#x27;',
    '/': '&#x2F;'
  };
  return this.replace(/[&<>"'/]/g, (match) => htmlEscapes[match] || match);
};

String.prototype.unescapeHtml = function(): string {
  const htmlUnescapes: Record<string, string> = {
    '&amp;': '&',
    '&lt;': '<',
    '&gt;': '>',
    '&quot;': '"',
    '&#x27;': "'",
    '&#x2F;': '/'
  };
  return this.replace(/&(?:amp|lt|gt|quot|#x27|#x2F);/g, (match) => htmlUnescapes[match] || match);
};

String.prototype.stripHtml = function(): string {
  return this.replace(/<[^>]*>/g, '');
};

String.prototype.toSlug = function(): string {
  return this.toString()
    .toLowerCase()
    .trim()
    .replace(/[^\w\s-]/g, '')
    .replace(/[\s_-]+/g, '-')
    .replace(/^-+|-+$/g, '');
};

String.prototype.reverse = function(): string {
  return this.split('').reverse().join('');
};

String.prototype.countOccurrences = function(substring: string, caseSensitive: boolean = true): number {
  if (substring.length === 0) return 0;

  const str = caseSensitive ? this.toString() : this.toLowerCase();
  const sub = caseSensitive ? substring : substring.toLowerCase();

  let count = 0;
  let position = 0;

  while ((position = str.indexOf(sub, position)) !== -1) {
    count++;
    position += sub.length;
  }

  return count;
};

String.prototype.insertAt = function(index: number, value: string): string {
  if (index < 0) index = 0;
  if (index > this.length) index = this.length;
  return this.slice(0, index) + value + this.slice(index);
};

String.prototype.remove = function(substring: string, all: boolean = false): string {
  if (all) {
    return this.split(substring).join('');
  } else {
    const index = this.indexOf(substring);
    if (index === -1) return this.toString();
    return this.slice(0, index) + this.slice(index + substring.length);
  }
};

String.prototype.replaceMultiple = function(replacements: Record<string, string>): string {
  let result = this.toString();
  for (const [search, replace] of Object.entries(replacements)) {
    result = result.replace(new RegExp(search.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g'), replace);
  }
  return result;
};

String.prototype.startsWithAny = function(prefixes: string[]): boolean {
  return prefixes.some(prefix => this.startsWith(prefix));
};

String.prototype.endsWithAny = function(suffixes: string[]): boolean {
  return suffixes.some(suffix => this.endsWith(suffix));
};

String.prototype.extractNumbers = function(): number[] {
  const matches = this.match(/-?\d+\.?\d*/g);
  return matches ? matches.map(Number).filter(n => !isNaN(n)) : [];
};

String.prototype.extractWords = function(): string[] {
  const matches = this.match(/\b\w+\b/g);
  return matches || [];
};

String.prototype.mask = function(maskChar: string = '*', visibleStart: number = 0, visibleEnd: number = 0): string {
  const str = this.toString();
  if (str.length <= visibleStart + visibleEnd) return str;

  const start = str.substring(0, visibleStart);
  const end = str.substring(str.length - visibleEnd);
  const middle = maskChar.repeat(str.length - visibleStart - visibleEnd);

  return start + middle + end;
};

String.prototype.hash = function(_algorithm: string = 'sha256'): string {
  // Note: This is a simple hash implementation for demonstration
  // In a real application, you might want to use a proper crypto library
  let hash = 0;
  const str = this.toString();

  if (str.length === 0) return hash.toString();

  for (let i = 0; i < str.length; i++) {
    const char = str.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32-bit integer
  }

  return Math.abs(hash).toString(16);
};

String.prototype.toBase64 = function(): string {
  try {
    // Try Node.js Buffer first
    if (typeof globalThis !== 'undefined' && (globalThis as any).Buffer) {
      return (globalThis as any).Buffer.from(this.toString(), 'utf8').toString('base64');
    }
    // Try browser btoa
    if (typeof globalThis !== 'undefined' && (globalThis as any).btoa) {
      return (globalThis as any).btoa(this.toString());
    }
    throw new Error('Base64 encoding not supported');
  } catch {
    throw new Error('Base64 encoding not supported in this environment');
  }
};

String.prototype.fromBase64 = function(): string {
  try {
    // Try Node.js Buffer first
    if (typeof globalThis !== 'undefined' && (globalThis as any).Buffer) {
      return (globalThis as any).Buffer.from(this.toString(), 'base64').toString('utf8');
    }
    // Try browser atob
    if (typeof globalThis !== 'undefined' && (globalThis as any).atob) {
      return (globalThis as any).atob(this.toString());
    }
    throw new Error('Base64 decoding not supported');
  } catch {
    throw new Error('Base64 decoding not supported in this environment');
  }
};

String.prototype.toCharArray = function(): string[] {
  return Array.from(this.toString());
};

String.prototype.similarity = function(other: string): number {
  const str1 = this.toString();
  const str2 = other.toString();

  if (str1 === str2) return 1;
  if (str1.length === 0 || str2.length === 0) return 0;

  // Simple character-based similarity calculation
  const longer = str1.length > str2.length ? str1 : str2;
  const shorter = str1.length > str2.length ? str2 : str1;

  if (longer.length === 0) return 1;

  let matches = 0;
  for (let i = 0; i < shorter.length; i++) {
    if (longer.charAt(i) === shorter.charAt(i)) {
      matches++;
    }
  }

  return matches / longer.length;
};

String.prototype.matchesPattern = function(pattern: string): boolean {
  // Convert wildcard pattern to regex
  const regexPattern = pattern
    .replace(/[.*+?^${}()|[\]\\]/g, '\\$&') // Escape regex special chars
    .replace(/\\\*/g, '.*')                 // Convert * to .*
    .replace(/\\\?/g, '.');                 // Convert ? to .

  const regex = new RegExp(`^${regexPattern}$`, 'i');
  return regex.test(this.toString());
};

String.prototype.wrap = function(wrapper: string): string {
  return wrapper + this.toString() + wrapper;
};

String.prototype.unwrap = function(wrapper: string): string {
  const str = this.toString();
  if (str.startsWith(wrapper) && str.endsWith(wrapper) && str.length >= wrapper.length * 2) {
    return str.slice(wrapper.length, -wrapper.length);
  }
  return str;
};

String.prototype.padCenter = function(length: number, padChar: string = ' '): string {
  const str = this.toString();
  if (str.length >= length) return str;

  const totalPadding = length - str.length;
  const leftPadding = Math.floor(totalPadding / 2);
  const rightPadding = totalPadding - leftPadding;

  return padChar.repeat(leftPadding) + str + padChar.repeat(rightPadding);
};

String.prototype.toBoolean = function(): boolean {
  const str = this.toString().toLowerCase().trim();
  return ['true', 'yes', '1', 'on', 'enabled'].includes(str);
};

String.prototype.format = function(values: Record<string, any>): string {
  let result = this.toString();

  for (const [key, value] of Object.entries(values)) {
    const placeholder = new RegExp(`\\$\\{${key}\\}`, 'g');
    result = result.replace(placeholder, String(value));
  }

  return result;
};

String.prototype.chunk = function(size: number): string[] {
  if (size <= 0) return [this.toString()];

  const chunks: string[] = [];
  const str = this.toString();

  for (let i = 0; i < str.length; i += size) {
    chunks.push(str.slice(i, i + size));
  }

  return chunks;
};

String.prototype.repeatWith = function(count: number, separator: string = ''): string {
  if (count <= 0) return '';
  if (count === 1) return this.toString();

  const parts: string[] = [];
  for (let i = 0; i < count; i++) {
    parts.push(this.toString());
  }

  return parts.join(separator);
};

export {}; // This export is needed to make the file a module
