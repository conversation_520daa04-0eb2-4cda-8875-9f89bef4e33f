# BullMQ Integration

This package provides a comprehensive BullMQ integration for the c-cam application, following the same coding patterns and architecture as other integrations (Kafka, MongoDB, MQTT, Redis).

## Features

- **Singleton Connection Manager**: Centralized connection management with health monitoring
- **Client Interface**: High-level client interface for queue and worker operations
- **Health Monitoring**: Built-in health checks with configurable intervals
- **Event Handling**: Comprehensive event handling for job lifecycle
- **Error Handling**: Robust error handling with retry logic
- **Logging**: Integrated logging using the winston logger
- **Backward Compatibility**: QueueService wrapper for existing video processing service

## Architecture

### Core Components

1. **BullMQConnection**: Singleton connection manager
2. **BullMQClient**: High-level client interface
3. **QueueService**: Backward compatibility wrapper
4. **Example**: Usage examples and documentation

### Files Structure

```
packages/core/src/integrations/bullmq/
├── bullmq.ts          # Connection manager and core types
├── client.ts          # Client interface and implementation
├── queue-service.ts   # Backward compatibility wrapper
├── example.ts         # Usage examples
├── test.ts           # Simple test script
└── index.ts          # Exports
```

## Usage

### Basic Usage

```typescript
import { BullMQConnection, BullMQClient } from '@c-cam/core';

// Initialize connection
const connection = BullMQConnection.getInstance();
await connection.initialize({
  connection: {
    host: 'localhost',
    port: 6379,
  },
  healthCheck: {
    enabled: true,
    interval: 30000,
  },
});

// Create client
const client = new BullMQClient({
  queuePrefix: 'app',
});

// Create queue and worker
await client.createQueue('email-processing');
await client.createWorker('email-processing', async (job) => {
  console.log('Processing job:', job.data);
  return { processed: true };
});

// Add job
await client.addJob('email-processing', {
  type: 'send-email',
  payload: { to: '<EMAIL>', subject: 'Hello' },
});
```

### Backward Compatibility

For existing services using the old QueueService interface:

```typescript
import { QueueService } from '@c-cam/core';

const queueService = new QueueService();

// Create queue
await queueService.createQueue({
  name: 'video-processing',
  connection: {
    host: 'localhost',
    port: 6379,
  },
});

// Create worker
await queueService.createWorker(
  {
    queueName: 'video-processing',
    connection: { host: 'localhost', port: 6379 },
    concurrency: 2,
  },
  async (job) => {
    // Process job
    return { result: 'success' };
  },
  {
    onCompleted: async (job, result) => {
      console.log('Job completed:', job.id);
    },
    onFailed: async (job, error) => {
      console.error('Job failed:', job?.id, error.message);
    },
  }
);
```

## Configuration

### Connection Options

```typescript
interface BullMQConnectionOptions {
  connection: ConnectionOptions;           // Redis connection
  defaultQueueOptions?: QueueOptions;     // Default queue options
  defaultWorkerOptions?: WorkerOptions;   // Default worker options
  healthCheck?: {                         // Health monitoring
    enabled?: boolean;
    interval?: number;
    timeout?: number;
  };
  errorHandling?: {                       // Error handling
    throwOnError?: boolean;
    maxRetries?: number;
    retryDelay?: number;
  };
}
```

### Client Options

```typescript
interface BullMQClientOptions {
  queuePrefix?: string;                   // Queue name prefix
  defaultJobOptions?: JobsOptions;       // Default job options
  defaultQueueOptions?: QueueOptions;    // Default queue options
  defaultWorkerOptions?: WorkerOptions;  // Default worker options
}
```

## Health Monitoring

The integration includes comprehensive health monitoring:

```typescript
// Get health status
const healthStatus = connection.getHealthStatus();
console.log('Health:', healthStatus);

// Perform health check
const isHealthy = await connection.healthCheck();
console.log('Is healthy:', isHealthy);

// Get queue statistics
const stats = await client.getQueueStats('my-queue');
console.log('Queue stats:', stats);
```

## Event Handling

Comprehensive event handling for job lifecycle:

```typescript
const eventHandlers = {
  onCompleted: async (job, result) => {
    console.log(`Job ${job.id} completed:`, result);
  },
  onFailed: async (job, error) => {
    console.error(`Job ${job?.id} failed:`, error.message);
  },
  onProgress: async (job, progress) => {
    console.log(`Job ${job.id} progress:`, progress);
  },
  onActive: (job) => {
    console.log(`Job ${job.id} started`);
  },
  onStalled: (jobId) => {
    console.warn(`Job ${jobId} stalled`);
  },
};

await client.createWorker('my-queue', processor, {}, eventHandlers);
```

## Integration with Existing Services

The BullMQ integration is designed to work seamlessly with the existing video processing service through the QueueService wrapper. The video processing service can continue using the same API while benefiting from the new BullMQ implementation.

## Dependencies

- `bullmq`: ^5.4.0 (already installed)
- `ioredis`: ^5.3.2 (already installed via Redis integration)

## Testing

Run the test script to verify the integration:

```typescript
import { testBullMQIntegration } from '@c-cam/core';

await testBullMQIntegration();
```

## Examples

See `example.ts` for comprehensive usage examples including:
- Basic queue and worker operations
- Job management and monitoring
- Queue statistics and health checks
- Advanced job processing patterns
- Error handling and retry logic
