import PermissionModel, {
  PermissionDocument,
} from '@/database/entities/PermissionModel';
import { Repository, Injectable } from '@c-cam/core';

/**
 * Repository for managing permissions
 * Extends the BaseRepository with PermissionDocument type
 */
@Injectable()
class PermissionRepository extends Repository<PermissionDocument> {
  constructor() {
    super(PermissionModel);
  }

  /**
   * Find permissions by role ID
   * @param roleId The role ID to search for
   * @returns A promise that resolves to an array of permissions
   */
  async findByRoleId(roleId: string): Promise<PermissionDocument[]> {
    return this.find({ role_id: roleId });
  }

  /**
   * Find permissions by module
   * @param module The module to search for
   * @returns A promise that resolves to an array of permissions
   */
  async findByModule(module: string): Promise<PermissionDocument[]> {
    return this.find({ module });
  }

  /**
   * Find permissions by feature
   * @param feature The feature to search for
   * @returns A promise that resolves to an array of permissions
   */
  async findByFeature(feature: string): Promise<PermissionDocument[]> {
    return this.find({ feature });
  }

  /**
   * Find permissions by action
   * @param action The action to search for
   * @returns A promise that resolves to an array of permissions
   */
  async findByAction(action: string): Promise<PermissionDocument[]> {
    return this.find({ action });
  }

  /**
   * Find permissions by module, feature, and action
   * @param module The module to search for
   * @param feature The feature to search for
   * @param action The action to search for
   * @returns A promise that resolves to an array of permissions
   */
  async findByModuleFeatureAction(
    module: string,
    feature: string,
    action: string,
  ): Promise<PermissionDocument[]> {
    return this.find({ module, feature, action });
  }

  /**
   * Delete permissions by role ID
   * @param roleId The role ID to delete permissions for
   * @returns A promise that resolves to true if any permissions were deleted, false otherwise
   */
  async deleteByRoleId(roleId: string): Promise<boolean> {
    const result = await this.getModel().deleteMany({ role_id: roleId }).exec();
    return result.deletedCount > 0;
  }
}

export default PermissionRepository;
