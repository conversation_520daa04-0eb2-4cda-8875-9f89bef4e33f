import * as mqtt from 'mqtt';
import { Mqtt<PERSON>onnection, MqttMessageHandler } from './mqtt.js';
import { logger } from '@c-cam/logger';

/**
 * MQTT client options
 */
export interface MqttClientOptions {
  /**
   * Topic prefix for all operations
   */
  topicPrefix?: string;

  /**
   * Default QoS level for publish and subscribe operations
   * @default 0
   */
  defaultQos?: 0 | 1 | 2;

  /**
   * De<PERSON>ult retain flag for publish operations
   * @default false
   */
  defaultRetain?: boolean;
}

/**
 * MQTT client interface
 */
export interface IMqttClient {
  /**
   * Subscribe to a topic
   * @param topic The topic to subscribe to
   * @param options Subscription options
   */
  subscribe(
    topic: string,
    options?: mqtt.IClientSubscribeOptions,
  ): Promise<void>;

  /**
   * Unsubscribe from a topic
   * @param topic The topic to unsubscribe from
   */
  unsubscribe(topic: string): Promise<void>;

  /**
   * Publish a message to a topic
   * @param topic The topic to publish to
   * @param message The message to publish
   * @param options Publish options
   */
  publish(
    topic: string,
    message: string | Buffer | object,
    options?: mqtt.IClientPublishOptions,
  ): Promise<void>;

  /**
   * Register a message handler for a topic
   * @param topic The topic to handle messages for
   * @param handler The message handler function
   */
  onMessage(topic: string, handler: MqttMessageHandler): void;

  /**
   * Remove a message handler for a topic
   * @param topic The topic to remove the handler for
   * @param handler The message handler function to remove
   */
  offMessage(topic: string, handler: MqttMessageHandler): void;
}

/**
 * MQTT client implementation
 */
export class MqttClient implements IMqttClient {
  private readonly connection: MqttConnection;
  private readonly options: MqttClientOptions;

  /**
   * Create a new MQTT client
   * @param options Client options
   */
  constructor(options: MqttClientOptions = {}) {
    this.options = {
      topicPrefix: '',
      defaultQos: 0,
      defaultRetain: false,
      ...options,
    };

    // Get the MQTT connection
    this.connection = MqttConnection.getInstance();
  }

  /**
   * Format a topic with the prefix
   * @param topic The topic
   * @returns The formatted topic
   */
  private formatTopic(topic: string): string {
    return this.options.topicPrefix
      ? `${this.options.topicPrefix}/${topic}`
      : topic;
  }

  /**
   * Subscribe to a topic
   * @param topic The topic to subscribe to
   * @param options Subscription options
   */
  public async subscribe(
    topic: string,
    options?: mqtt.IClientSubscribeOptions,
  ): Promise<void> {
    const formattedTopic = this.formatTopic(topic);

    // Ensure QoS is a valid value (0, 1, or 2)
    const qos = this.options.defaultQos ?? 0;
    const subscribeOptions = options || { qos };

    logger.debug('Subscribing to MQTT topic', {
      topic: formattedTopic,
      qos: subscribeOptions.qos,
    });

    await this.connection.subscribe(formattedTopic, subscribeOptions);
  }

  /**
   * Unsubscribe from a topic
   * @param topic The topic to unsubscribe from
   */
  public async unsubscribe(topic: string): Promise<void> {
    const formattedTopic = this.formatTopic(topic);

    logger.debug('Unsubscribing from MQTT topic', {
      topic: formattedTopic,
    });

    await this.connection.unsubscribe(formattedTopic);
  }

  /**
   * Publish a message to a topic
   * @param topic The topic to publish to
   * @param message The message to publish
   * @param options Publish options
   */
  public async publish(
    topic: string,
    message: string | Buffer | object,
    options?: mqtt.IClientPublishOptions,
  ): Promise<void> {
    const formattedTopic = this.formatTopic(topic);

    // Ensure QoS is a valid value (0, 1, or 2)
    const qos = this.options.defaultQos ?? 0;
    const retain = this.options.defaultRetain ?? false;

    const publishOptions = options || {
      qos,
      retain,
    };

    // Convert object to JSON string if necessary
    const messageToSend =
      typeof message === 'object' && !(message instanceof Buffer)
        ? JSON.stringify(message)
        : message;

    // Log the publish operation (but don't log the full message content for large messages)
    const messagePreview =
      message instanceof Buffer
        ? `<Buffer of length ${message.length}>`
        : typeof message === 'object'
          ? '<Object>'
          : message.length > 100
            ? `${message.substring(0, 97)}...`
            : message;

    logger.debug('Publishing MQTT message', {
      topic: formattedTopic,
      qos: publishOptions.qos,
      retain: publishOptions.retain,
      messagePreview,
    });

    await this.connection.publish(
      formattedTopic,
      messageToSend,
      publishOptions,
    );
  }

  /**
   * Register a message handler for a topic
   * @param topic The topic to handle messages for
   * @param handler The message handler function
   */
  public onMessage(topic: string, handler: MqttMessageHandler): void {
    const formattedTopic = this.formatTopic(topic);

    logger.debug('Registering MQTT message handler', {
      topic: formattedTopic,
    });

    this.connection.onMessage(formattedTopic, handler);
  }

  /**
   * Remove a message handler for a topic
   * @param topic The topic to remove the handler for
   * @param handler The message handler function to remove
   */
  public offMessage(topic: string, handler: MqttMessageHandler): void {
    const formattedTopic = this.formatTopic(topic);

    logger.debug('Removing MQTT message handler', {
      topic: formattedTopic,
    });

    this.connection.offMessage(formattedTopic, handler);
  }
}
