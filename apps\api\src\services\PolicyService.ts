import { Injectable } from '@c-cam/core';
import { Document } from 'mongoose';
import PolicyModel, {
  PolicyDocument,
} from '../database/entities/PolicyModel';
import { BaseService } from './BaseService';
import { logger } from '@c-cam/logger';

/**
 * Service for managing dynamic policies
 */
@Injectable()
class PolicyService extends BaseService<PolicyDocument> {
  constructor() {
    super(PolicyModel);
  }

  /**
   * Create a new policy
   */
  async createPolicy(policyData: {
    name: string;
    description?: string;
    type: string;
    conditions: Record<string, any>;
    resources?: string[];
    actions?: string[];
    effect?: 'allow' | 'deny';
    priority?: number;
    isActive?: boolean;
    metadata?: Record<string, any>;
    createdBy: string;
  }): Promise<PolicyDocument> {
    // Check if policy with the same name already exists
    const existingPolicy = await this.findByName(policyData.name);
    if (existingPolicy) {
      throw new Error(`Policy with name '${policyData.name}' already exists`);
    }

    // Create the policy
    return await super.create(policyData);
  }

  /**
   * Find a policy by name
   */
  async findByName(name: string): Promise<PolicyDocument | null> {
    return await this.repository.findOne({ name });
  }

  /**
   * Find policies by type
   */
  async findByType(type: string): Promise<PolicyDocument[]> {
    return await this.repository.find({ type, isActive: true });
  }

  /**
   * Find policies by resource
   */
  async findByResource(resource: string): Promise<PolicyDocument[]> {
    return await this.repository.find({
      $or: [{ resources: resource }, { resources: '*' }],
      isActive: true,
    });
  }

  /**
   * Find policies for a specific resource and action
   */
  async findByResourceAndAction(
    resource: string,
    action: string,
  ): Promise<PolicyDocument[]> {
    return await this.repository
      .find({
        $or: [
          {
            $and: [
              { resources: resource },
              { $or: [{ actions: action }, { actions: '*' }] },
            ],
          },
          {
            $and: [
              { resources: '*' },
              { $or: [{ actions: action }, { actions: '*' }] },
            ],
          },
        ],
        isActive: true,
      })
      .sort({ priority: 1 });
  }

  /**
   * Find policies that apply to a user based on roles and other attributes
   * @param userId User ID
   * @param userRoles Array of user role IDs
   * @param attributes Additional user attributes to match against policy conditions
   */
  async findPoliciesForUser(
    userId: string,
    userRoles: string[],
    attributes: Record<string, any> = {},
  ): Promise<PolicyDocument[]> {
    try {
      // Find all active policies
      const allPolicies = await this.repository.find({ isActive: true });

      // Filter policies based on roles and other conditions
      return allPolicies.filter((policy) => {
        // If it's a role policy, check if the user has the required role
        if (policy.type === 'role' && policy.conditions.roleId) {
          return userRoles.includes(policy.conditions.roleId);
        }

        // For tenant policies, check tenant match
        if (policy.type === 'tenant' && policy.conditions.tenantId) {
          return attributes.tenantId === policy.conditions.tenantId;
        }

        // For time-based policies, always include them (they'll be evaluated at runtime)
        if (policy.type === 'time') {
          return true;
        }

        // For custom policies, perform basic condition matching
        if (policy.type === 'custom') {
          // Basic attribute matching (can be extended for more complex conditions)
          for (const [key, value] of Object.entries(policy.conditions)) {
            if (attributes[key] !== value) {
              return false;
            }
          }
          return true;
        }

        // Include resource policies (they'll be evaluated at runtime)
        if (policy.type === 'resource') {
          return true;
        }

        return false;
      });
    } catch (error) {
      logger.error('Error finding policies for user', { error, userId });
      return [];
    }
  }

  /**
   * Get policy statements for inclusion in access token
   * This converts database policies to a format suitable for JWT claims
   * @param userId User ID
   * @param userRoles Array of user role IDs
   * @param attributes Additional user attributes
   */
  async getPolicyStatementsForToken(
    userId: string,
    userRoles: string[],
    attributes: Record<string, any> = {},
  ): Promise<any[]> {
    try {
      const policies = await this.findPoliciesForUser(
        userId,
        userRoles,
        attributes,
      );

      // Convert policies to a simplified format suitable for tokens
      return policies.map((policy: PolicyDocument) => {
        // Ensure we have a valid MongoDB document with _id
        const docId =
          (policy as Document).id || policy._id?.toString() || 'unknown';

        return {
          id: docId,
          name: policy.name,
          effect: policy.effect,
          resources: policy.resources,
          actions: policy.actions,
          conditions: this.simplifyConditions(policy.conditions),
          type: policy.type,
        };
      });
    } catch (error) {
      logger.error('Error generating policy statements for token', {
        error,
        userId,
      });
      return [];
    }
  }

  /**
   * Simplify conditions for inclusion in token
   * Removes complex objects or functions that can't be serialized
   */
  private simplifyConditions(
    conditions: Record<string, any>,
  ): Record<string, any> {
    const simplified: Record<string, any> = {};

    for (const [key, value] of Object.entries(conditions)) {
      // Skip functions or complex objects that can't be easily serialized
      if (typeof value === 'function') {
        continue;
      }

      // Include primitive values and simple objects
      if (
        typeof value === 'string' ||
        typeof value === 'number' ||
        typeof value === 'boolean' ||
        value === null
      ) {
        simplified[key] = value;
      } else if (Array.isArray(value)) {
        // Include arrays of primitive values
        if (
          value.every(
            (item) =>
              typeof item === 'string' ||
              typeof item === 'number' ||
              typeof item === 'boolean',
          )
        ) {
          simplified[key] = value;
        }
      } else if (typeof value === 'object') {
        // Recursively simplify objects
        simplified[key] = this.simplifyConditions(value);
      }
    }

    return simplified;
  }
}

export default PolicyService;
