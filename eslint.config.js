/**
 * Root ESLint configuration for c-cam project
 * @type {import("eslint").Linter.Config[]}
 */
import js from "@eslint/js";
import eslint<PERSON>onfigPrettier from "eslint-config-prettier";
import tseslint from "typescript-eslint";
import only<PERSON>arn from "eslint-plugin-only-warn";

export default [
  js.configs.recommended,
  eslintConfigPrettier,
  ...tseslint.configs.recommended,
  {
    ignores: ["**/node_modules/**", "**/dist/**", "**/.turbo/**", "**/coverage/**"],
  },
  {
    rules: {
      // Global rules that apply to all files
      "no-console": ["warn"],
      "no-unused-vars": "off", // TypeScript handles this
      "@typescript-eslint/no-unused-vars": ["warn"],
      "@typescript-eslint/explicit-function-return-type": ["off"],
      "@typescript-eslint/explicit-module-boundary-types": ["off"],
      "@typescript-eslint/no-explicit-any": ["warn"],
    },
  },
];
