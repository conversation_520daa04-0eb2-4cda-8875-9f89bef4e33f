import { Context } from 'hono';
import {
  Controller,
  ControllerBase,
  HttpContext,
  HttpGet,
  HttpPost,
  HttpPut,
  HttpDelete,
  Inject,
} from '@c-cam/core';
import EdgeDeviceInfoService from '../services/EdgeDeviceInfoService';

@Controller('/api/edge-device-info')
export class EdgeDeviceInfoController extends ControllerBase {
  constructor(
    @Inject(EdgeDeviceInfoService) private edgeDeviceInfoService: EdgeDeviceInfoService,
  ) {
    super();
  }

  /**
   * Get all edge device info
   */
  @HttpGet('/')
  async getEdgeDeviceInfo(@HttpContext() c: Context): Promise<Response> {
    try {
      const userId = c.get('userId');
      if (!userId) {
        return c.json({ error: 'Unauthorized' }, 401);
      }

      const { limit, skip, sortBy, sortDirection } = c.req.query();

      const deviceInfo = await this.edgeDeviceInfoService.find({
        limit: limit ? parseInt(limit) : undefined,
        skip: skip ? parseInt(skip) : undefined,
        sortBy,
        sortDirection,
      });

      return c.json({ deviceInfo });
    } catch (error: any) {
      return c.json({ error: error.message }, 500);
    }
  }

  /**
   * Get edge device info by ID
   */
  @HttpGet('/:id')
  async getEdgeDeviceInfoById(@HttpContext() c: Context): Promise<Response> {
    try {
      const userId = c.get('userId');
      if (!userId) {
        return c.json({ error: 'Unauthorized' }, 401);
      }

      const { id } = c.req.param();
      if (!id) {
        return c.json({ error: 'Edge device info ID is required' }, 400);
      }

      const deviceInfo = await this.edgeDeviceInfoService.findById(id);

      if (!deviceInfo) {
        return c.json({ error: 'Edge device info not found' }, 404);
      }

      return c.json({ deviceInfo });
    } catch (error: any) {
      return c.json({ error: error.message }, 500);
    }
  }

  /**
   * Create new edge device info
   */
  @HttpPost('/')
  async createEdgeDeviceInfo(@HttpContext() c: Context): Promise<Response> {
    try {
      const userId = c.get('userId');
      if (!userId) {
        return c.json({ error: 'Unauthorized' }, 401);
      }

      const infoData = await c.req.json();

      // Add the creator ID
      infoData.created_by = userId;

      const deviceInfo = await this.edgeDeviceInfoService.createEdgeDeviceInfo(infoData);
      return c.json({ deviceInfo }, 201);
    } catch (error: any) {
      return c.json({ error: error.message }, 500);
    }
  }

  /**
   * Update edge device info
   */
  @HttpPut('/:id')
  async updateEdgeDeviceInfo(@HttpContext() c: Context): Promise<Response> {
    try {
      const userId = c.get('userId');
      if (!userId) {
        return c.json({ error: 'Unauthorized' }, 401);
      }

      const { id } = c.req.param();
      if (!id) {
        return c.json({ error: 'Edge device info ID is required' }, 400);
      }
      const infoData = await c.req.json();

      const success = await this.edgeDeviceInfoService.updateEdgeDeviceInfo(id, infoData);

      if (!success) {
        return c.json({ error: 'Failed to update edge device info' }, 400);
      }

      return c.json({ success: true });
    } catch (error: any) {
      return c.json({ error: error.message }, 500);
    }
  }

  /**
   * Delete edge device info
   */
  @HttpDelete('/:id')
  async deleteEdgeDeviceInfo(@HttpContext() c: Context): Promise<Response> {
    try {
      const userId = c.get('userId');
      if (!userId) {
        return c.json({ error: 'Unauthorized' }, 401);
      }

      const { id } = c.req.param();
      if (!id) {
        return c.json({ error: 'Edge device info ID is required' }, 400);
      }
      const success = await this.edgeDeviceInfoService.delete(id);

      if (!success) {
        return c.json({ error: 'Failed to delete edge device info' }, 400);
      }

      return c.json({ success: true });
    } catch (error: any) {
      return c.json({ error: error.message }, 500);
    }
  }

  /**
   * Find edge device info by edge device ID
   */
  @HttpGet('/device/:edgeDeviceId')
  async getEdgeDeviceInfoByDeviceId(@HttpContext() c: Context): Promise<Response> {
    try {
      const userId = c.get('userId');
      if (!userId) {
        return c.json({ error: 'Unauthorized' }, 401);
      }

      const { edgeDeviceId } = c.req.param();
      if (!edgeDeviceId) {
        return c.json({ error: 'Edge device ID is required' }, 400);
      }

      const deviceInfo = await this.edgeDeviceInfoService.findByEdgeDeviceId(edgeDeviceId);

      return c.json({ deviceInfo });
    } catch (error: any) {
      return c.json({ error: error.message }, 500);
    }
  }

  /**
   * Find edge device info by CPU model
   */
  @HttpGet('/cpu/:cpuModel')
  async getEdgeDeviceInfoByCpuModel(@HttpContext() c: Context): Promise<Response> {
    try {
      const userId = c.get('userId');
      if (!userId) {
        return c.json({ error: 'Unauthorized' }, 401);
      }

      const { cpuModel } = c.req.param();
      if (!cpuModel) {
        return c.json({ error: 'CPU model is required' }, 400);
      }

      const deviceInfo = await this.edgeDeviceInfoService.findByCpuModel(cpuModel);

      return c.json({ deviceInfo });
    } catch (error: any) {
      return c.json({ error: error.message }, 500);
    }
  }

  /**
   * Find edge device info by operating system
   */
  @HttpGet('/os/:operatingSystem')
  async getEdgeDeviceInfoByOs(@HttpContext() c: Context): Promise<Response> {
    try {
      const userId = c.get('userId');
      if (!userId) {
        return c.json({ error: 'Unauthorized' }, 401);
      }

      const { operatingSystem } = c.req.param();
      if (!operatingSystem) {
        return c.json({ error: 'Operating system is required' }, 400);
      }

      const deviceInfo = await this.edgeDeviceInfoService.findByOperatingSystem(operatingSystem);

      return c.json({ deviceInfo });
    } catch (error: any) {
      return c.json({ error: error.message }, 500);
    }
  }

  /**
   * Find edge device info by date range
   */
  @HttpGet('/date-range/:startDate/:endDate')
  async getEdgeDeviceInfoByDateRange(@HttpContext() c: Context): Promise<Response> {
    try {
      const userId = c.get('userId');
      if (!userId) {
        return c.json({ error: 'Unauthorized' }, 401);
      }

      const { startDate, endDate } = c.req.param();
      if (!startDate || !endDate) {
        return c.json({ error: 'Start and end dates are required' }, 400);
      }
      const start = new Date(startDate);
      const end = new Date(endDate);

      if (isNaN(start.getTime()) || isNaN(end.getTime())) {
        return c.json({ error: 'Invalid date format' }, 400);
      }

      const deviceInfo = await this.edgeDeviceInfoService.findByDateRange(start, end);

      return c.json({ deviceInfo });
    } catch (error: any) {
      return c.json({ error: error.message }, 500);
    }
  }

  /**
   * Delete edge device info by edge device ID
   */
  @HttpDelete('/device/:edgeDeviceId')
  async deleteEdgeDeviceInfoByDeviceId(@HttpContext() c: Context): Promise<Response> {
    try {
      const userId = c.get('userId');
      if (!userId) {
        return c.json({ error: 'Unauthorized' }, 401);
      }

      const { edgeDeviceId } = c.req.param();
      if (!edgeDeviceId) {
        return c.json({ error: 'Edge device ID is required' }, 400);
      }

      const success = await this.edgeDeviceInfoService.deleteByEdgeDeviceId(edgeDeviceId);

      if (!success) {
        return c.json({ error: 'Failed to delete edge device info' }, 400);
      }

      return c.json({ success: true });
    } catch (error: any) {
      return c.json({ error: error.message }, 500);
    }
  }
}
