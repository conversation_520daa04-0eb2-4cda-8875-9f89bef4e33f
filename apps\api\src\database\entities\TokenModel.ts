import { Document } from 'mongoose';
import { createSchema, createModel } from '@c-cam/core';
import { DeviceInfo } from '@c-cam/types';

/**
 * Token Document Interface
 * Defines the MongoDB document for tokens
 */
export interface TokenDocument extends Document {
  user_id: string;
  token: string;
  refresh_token: string;
  token_type: string;
  expires_at: Date;
  refresh_expires_at: Date;
  revoked: boolean;
  revoked_at?: Date;
  created_at: Date;
  updated_at: Date;
  tenant_id?: string;
  device_info?: DeviceInfo;
  ip_address?: string;
  user_agent?: string;
  // Security tracking
  last_used_at?: Date;
  rotation_count: number;
  parent_token_id?: string; // For token rotation tracking
  is_compromised: boolean;
  compromised_at?: Date;
  // Session tracking
  session_id: string;
}

/**
 * Token Schema
 * Defines the MongoDB schema for tokens
 */
const TokenSchema = createSchema({
  user_id: {
    type: String,
    required: true,
    index: true,
  },
  token: {
    type: String,
    required: true,
  },
  refresh_token: {
    type: String,
    required: true,
  },
  token_type: {
    type: String,
    required: true,
    default: 'Bearer',
  },
  expires_at: {
    type: Date,
    required: true,
  },
  refresh_expires_at: {
    type: Date,
    required: true,
  },
  revoked: {
    type: Boolean,
    required: true,
    default: false,
    index: true,
  },
  revoked_at: {
    type: Date,
    required: false,
  },
  created_at: {
    type: Date,
    required: true,
    default: Date.now,
  },
  updated_at: {
    type: Date,
    required: true,
    default: Date.now,
  },
  tenant_id: {
    type: String,
    required: false,
    index: true,
  },
  device_info: {
    type: Object,
    required: false,
  },
  ip_address: {
    type: String,
    required: false,
  },
  user_agent: {
    type: String,
    required: false,
  },
  // Security tracking
  last_used_at: {
    type: Date,
    required: false,
  },
  rotation_count: {
    type: Number,
    required: true,
    default: 0,
  },
  parent_token_id: {
    type: String,
    required: false,
  },
  is_compromised: {
    type: Boolean,
    required: true,
    default: false,
  },
  compromised_at: {
    type: Date,
    required: false,
  },
  // Session tracking
  session_id: {
    type: String,
    required: true,
  },
});

// Add indexes for performance and security
TokenSchema.index({ user_id: 1, revoked: 1 });
TokenSchema.index({ token: 1 }, { unique: true });
TokenSchema.index({ refresh_token: 1 }, { unique: true });
TokenSchema.index({ expires_at: 1 });
TokenSchema.index({ refresh_expires_at: 1 });
TokenSchema.index({ session_id: 1 });
TokenSchema.index({ user_id: 1, session_id: 1 });
TokenSchema.index({ parent_token_id: 1 });
TokenSchema.index({ is_compromised: 1 });
TokenSchema.index({ user_id: 1, is_compromised: 1 });

// Add pre-save middleware to update updated_at
TokenSchema.pre('save', function (next) {
  this.updated_at = new Date();
  next();
});

// Create and export the model
const TokenModel = createModel<TokenDocument>('tokens', TokenSchema);

export default TokenModel;
