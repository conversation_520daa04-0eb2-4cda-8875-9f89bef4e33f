import TenantModel, {
  TenantDocument,
} from '@/database/entities/TenantModel';
import { Repository, Injectable } from '@c-cam/core';

/**
 * Repository for managing organizations
 * Extends the BaseRepository with OrganizationDocument type
 */
@Injectable()
class TenantRepository extends Repository<TenantDocument> {
  constructor() {
    super(TenantModel);
  }

  /**
   * Find an organization by name
   * @param name The organization name to search for
   * @returns A promise that resolves to an organization or null if not found
   */
  async findByName(name: string): Promise<TenantDocument | null> {
    return this.findOne({ name });
  }

  /**
   * Find organizations by created by
   * @param createdBy The creator ID to search for
   * @returns A promise that resolves to an array of organizations
   */
  async findByCreatedBy(createdBy: string): Promise<TenantDocument[]> {
    return this.find({ created_by: createdBy });
  }
}

export default TenantRepository;
