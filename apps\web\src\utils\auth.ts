import type { TokenPayload } from '@/types/auth'

// Storage keys - using memory for access token, cookies for refresh token
export const AUTH_STORAGE_KEYS = {
  ACCESS_TOKEN: 'auth_token', // Will be stored in memory
  USER: 'auth_user',
} as const

// In-memory storage for access token (more secure than localStorage)
let accessTokenMemory: string | null = null

/**
 * Get device information for authentication
 */
export const getDeviceInfo = () => {
  if (typeof window === 'undefined') return undefined

  return {
    deviceName:
      (navigator as any).userAgentData?.platform ||
      navigator.platform ||
      'Unknown Device',
    deviceType: /Mobile|Android|iPhone|iPad/.test(navigator.userAgent)
      ? 'mobile'
      : 'desktop',
    userAgent: navigator.userAgent,
  }
}

/**
 * Set access token in memory (secure storage)
 */
export const setAccessToken = (token: string) => {
  accessTokenMemory = token
}

/**
 * Get access token from memory
 */
export const getAccessToken = (): string | null => {
  return accessTokenMemory
}

/**
 * Clear access token from memory
 */
export const clearAccessToken = () => {
  accessTokenMemory = null
}

/**
 * Store user data in localStorage
 */
export const storeUser = (user: any) => {
  if (typeof window === 'undefined') return
  localStorage.setItem(AUTH_STORAGE_KEYS.USER, JSON.stringify(user))
}

/**
 * Get user data from localStorage
 */
export const getStoredUser = () => {
  if (typeof window === 'undefined') return null

  try {
    const userStr = localStorage.getItem(AUTH_STORAGE_KEYS.USER)
    return userStr ? JSON.parse(userStr) : null
  } catch {
    return null
  }
}

/**
 * Clear all auth data
 */
export const clearAuthData = () => {
  // Clear access token from memory
  clearAccessToken()

  // Clear user data from localStorage
  if (typeof window !== 'undefined') {
    localStorage.removeItem(AUTH_STORAGE_KEYS.USER)
  }
}

/**
 * Decode JWT token payload (without verification)
 * Note: This is for client-side display purposes only, not for security
 */
export const decodeToken = (token: string): TokenPayload | null => {
  try {
    const parts = token.split('.')
    if (parts.length !== 3 || !parts[1]) return null

    const payload = parts[1]
    const decoded = atob(payload.replace(/-/g, '+').replace(/_/g, '/'))
    return JSON.parse(decoded) as TokenPayload
  } catch {
    return null
  }
}

/**
 * Check if token is expired (client-side check only)
 */
export const isTokenExpired = (token: string): boolean => {
  const payload = decodeToken(token)
  if (!payload) return true

  const currentTime = Math.floor(Date.now() / 1000)
  return payload.exp < currentTime
}

/**
 * Check if token will expire soon (within 5 minutes)
 */
export const isTokenExpiringSoon = (
  token: string,
  bufferMinutes = 5,
): boolean => {
  const payload = decodeToken(token)
  if (!payload) return true

  const currentTime = Math.floor(Date.now() / 1000)
  const bufferTime = bufferMinutes * 60
  return payload.exp < currentTime + bufferTime
}

/**
 * Get time until token expires (in seconds)
 */
export const getTokenExpiryTime = (token: string): number => {
  const payload = decodeToken(token)
  if (!payload) return 0

  const currentTime = Math.floor(Date.now() / 1000)
  return Math.max(0, payload.exp - currentTime)
}

/**
 * Check if user has specific permission
 */
export const hasPermission = (
  userPermissions: Array<string>,
  requiredPermission: string,
): boolean => {
  return userPermissions.includes(requiredPermission)
}

/**
 * Check if user has any of the specified permissions
 */
export const hasAnyPermission = (
  userPermissions: Array<string>,
  requiredPermissions: Array<string>,
): boolean => {
  return requiredPermissions.some((permission) =>
    userPermissions.includes(permission),
  )
}

/**
 * Check if user has all of the specified permissions
 */
export const hasAllPermissions = (
  userPermissions: Array<string>,
  requiredPermissions: Array<string>,
): boolean => {
  return requiredPermissions.every((permission) =>
    userPermissions.includes(permission),
  )
}

/**
 * Check if user has specific role
 */
export const hasRole = (
  userRoles: Array<string>,
  requiredRole: string,
): boolean => {
  return userRoles.includes(requiredRole)
}

/**
 * Check if user has any of the specified roles
 */
export const hasAnyRole = (
  userRoles: Array<string>,
  requiredRoles: Array<string>,
): boolean => {
  return requiredRoles.some((role) => userRoles.includes(role))
}

/**
 * Redirect to login page
 */
export const redirectToLogin = () => {
  if (typeof window === 'undefined') return

  // Clear auth data before redirecting
  clearAuthData()

  // Redirect to login page
  window.location.href = '/auth/login'
}
