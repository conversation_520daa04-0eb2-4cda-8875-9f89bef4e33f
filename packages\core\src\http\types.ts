/**
 * Core HTTP types using Hono
 * This file provides type definitions for HTTP-related components
 */

import { Context as HonoContext, Next } from 'hono';
import { StatusCode } from 'hono/utils/http-status';
import { MiddlewareHandler } from 'hono/types';
import { AppEnv } from '../hosting/interfaces.js';
import { Http } from '../shared/types.js';

/**
 * Application-specific Context extending Hon<PERSON>'s Context
 * This is the main context object passed to all route handlers
 */
export type Context = HonoContext<AppEnv>;

/**
 * Request object derived from Hono's Context
 */
export type Request = Context['req'];

/**
 * Response object derived from Hono's Context
 */
export type Response = Context['res'];

/**
 * NextFunction for middleware chaining
 */
export type NextFunction = Next;

/**
 * RequestHandler type for route handlers
 */
export type RequestHandler = MiddlewareHandler<AppEnv>;

/**
 * HTTP status code type from Hono
 */
export type HttpStatusCode = StatusCode;

/**
 * Standard HTTP methods supported by the framework
 * Uses the shared Http.Method type
 */
export type HttpMethod = Http.Method;

/**
 * Structured HTTP result for controller methods
 * Provides a consistent format for API responses
 * Uses the shared Http.Result interface
 */
export interface HttpResult<T = unknown> extends Http.Result<T> {}

/**
 * Type for a successful HTTP response
 */
export interface SuccessResult<T = unknown> extends HttpResult<T> {
  success: true;
  data?: T;
  message?: string;
}

/**
 * Type for an error HTTP response
 */
export interface ErrorResult extends HttpResult {
  success: false;
  message: string;
  error: {
    code: string;
    details?: unknown[];
  };
}
