import 'reflect-metadata';

export const INJECTABLE_METADATA_KEY = 'ts-core:injectable';
export const INJECT_METADATA_KEY = 'ts-core:inject';

/**
 * Represents a class constructor type that can be instantiated with 'new'
 * @template T The type of instance the constructor creates
 */
export type Constructable<T = unknown> = new (...args: any[]) => T;

export type AbstractConstructable<T = unknown> = abstract new (
  ...args: unknown[]
) => T;

export type InjectableIdentifier<T = unknown> =
  | Constructable<T>
  | AbstractConstructable<T>
  | string;

export interface Metadata<T = unknown> {
  instance?: T;
  factory?: () => T;
}

export interface Options<T> {
  factory?: () => T;
  key?: string;
}

export const instances = new Map<InjectableIdentifier | string, Metadata>();

/**
 * Decorator that marks a class as available for dependency injection.
 */
export function Injectable<T = unknown>(): Function;
export function Injectable<T = unknown>(options: Options<T>): Function;
export function Injectable<T>({ factory, key }: Options<T> = {}) {
  return function (target: Constructable<T>) {
    instances.set(target, { factory });
    if (key) instances.set(key, { factory });
    return target;
  };
}
