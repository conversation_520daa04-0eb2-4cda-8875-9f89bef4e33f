import { useMutation } from '@tanstack/react-query'
import type { UseMutationOptions, UseQueryOptions } from '@tanstack/react-query'
import type {
  LoginRequest,
  LoginResponse,
  RefreshTokenResponse,
  User,
  VerifyTokenRequest,
  VerifyTokenResponse,
} from '@/types/auth'
import { useApiQuery } from '@/shared/hooks/use-api-query'
import { axiosClient } from '@/configs/axios'
import { APIResponse } from '@c-cam/types'

/**
 * Hook to get current user profile (/api/identity/me)
 */
export const useCurrentUserQuery = (
  options?: Omit<
    UseQueryOptions<{ user: User }, Error>,
    'queryKey' | 'queryFn'
  >,
) => {
  return useApiQuery<{ user: User }>(
    ['identity', 'me'],
    '/api/identity/me',
    undefined,
    options,
  )
}

/**
 * Hook for user login mutation - uses credentials and returns access token + user info
 * Refresh token is automatically set as HttpOnly cookie by the server
 */
export const useLoginMutation = (
  options?: UseMutationOptions<APIResponse<LoginResponse>, Error, LoginRequest>,
) => {
  return useMutation<APIResponse<LoginResponse>, Error, LoginRequest>({
    mutationFn: async (credentials: LoginRequest) => {
      const response = await axiosClient.post(
        '/api/identity/login',
        credentials,
      )
      return response.data
    },
    ...options,
  })
}

/**
 * Hook for refresh token mutation - uses HttpOnly cookie automatically
 * No need to pass refresh token manually
 */
export const useRefreshTokenMutation = (
  options?: UseMutationOptions<
    APIResponse<RefreshTokenResponse>,
    Error,
    { deviceInfo?: any }
  >,
) => {
  return useMutation<
    APIResponse<RefreshTokenResponse>,
    Error,
    { deviceInfo?: any }
  >({
    mutationFn: async (request = {}) => {
      const response = await axiosClient.post(
        '/api/identity/refresh-token',
        request,
        {
          withCredentials: true, // Include HttpOnly cookies
        },
      )
      return response.data
    },
    ...options,
  })
}

/**
 * Hook for logout mutation - clears HttpOnly cookie automatically
 */
export const useLogoutMutation = (
  options?: UseMutationOptions<{ message: string }, Error, void>,
) => {
  return useMutation<{ message: string }, Error, void>({
    mutationFn: async () => {
      const response = await axiosClient.post(
        '/api/identity/logout',
        {},
        {
          withCredentials: true, // Include HttpOnly cookies for cleanup
        },
      )
      return response.data
    },
    ...options,
  })
}

/**
 * Hook for logout from all devices mutation
 */
export const useLogoutAllMutation = (
  options?: UseMutationOptions<
    { message: string; revoked_tokens: number },
    Error,
    void
  >,
) => {
  return useMutation<{ message: string; revoked_tokens: number }, Error, void>({
    mutationFn: async () => {
      const response = await axiosClient.post(
        '/api/identity/logout-all',
        {},
        {
          withCredentials: true,
        },
      )
      return response.data
    },
    ...options,
  })
}

/**
 * Hook for token verification mutation
 */
export const useVerifyTokenMutation = (
  options?: UseMutationOptions<VerifyTokenResponse, Error, VerifyTokenRequest>,
) => {
  return useMutation<VerifyTokenResponse, Error, VerifyTokenRequest>({
    mutationFn: async (request: VerifyTokenRequest) => {
      const response = await axiosClient.post(
        '/api/identity/verify-token',
        request,
      )
      return response.data
    },
    ...options,
  })
}
