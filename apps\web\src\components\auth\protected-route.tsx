import React from 'react'
import { usePermissions } from '@/hooks/use-permissions'

interface ProtectedRouteProps {
  children: React.ReactNode
  permission?: string
  permissions?: string[]
  role?: string
  roles?: string[]
  requireAll?: boolean // For permissions array, require all or any
  fallback?: React.ReactNode
}

/**
 * Component to protect routes/content based on permissions or roles
 */
export const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  permission,
  permissions,
  role,
  roles,
  requireAll = false,
  fallback = <AccessDenied />,
}) => {
  const {
    isAuthenticated,
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,
    hasRole,
    hasAnyRole,
  } = usePermissions()

  // If not authenticated, don't render
  if (!isAuthenticated) {
    return fallback
  }

  // Check single permission
  if (permission && !hasPermission(permission)) {
    return fallback
  }

  // Check multiple permissions
  if (permissions && permissions.length > 0) {
    const hasRequiredPermissions = requireAll
      ? hasAllPermissions(permissions)
      : hasAnyPermission(permissions)
    
    if (!hasRequiredPermissions) {
      return fallback
    }
  }

  // Check single role
  if (role && !hasRole(role)) {
    return fallback
  }

  // Check multiple roles
  if (roles && roles.length > 0 && !hasAnyRole(roles)) {
    return fallback
  }

  // All checks passed, render children
  return <>{children}</>
}

/**
 * Default access denied component
 */
const AccessDenied: React.FC = () => {
  return (
    <div className="flex flex-col items-center justify-center min-h-[400px] p-8">
      <div className="text-center">
        <div className="mb-4">
          <svg
            className="mx-auto h-16 w-16 text-gray-400"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={1}
              d="M12 15v2m0 0v2m0-2h2m-2 0H10m9-7a9 9 0 11-18 0 9 9 0 0118 0z"
            />
          </svg>
        </div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">
          Không có quyền truy cập
        </h3>
        <p className="text-sm text-gray-500 mb-4">
          Bạn không có quyền truy cập vào nội dung này. Vui lòng liên hệ quản trị viên nếu bạn cho rằng đây là lỗi.
        </p>
        <button
          onClick={() => window.history.back()}
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          Quay lại
        </button>
      </div>
    </div>
  )
}

/**
 * Higher-order component for protecting routes
 */
export const withPermission = (
  permission: string,
  fallback?: React.ReactNode
) => {
  return function PermissionWrapper<P extends object>(
    Component: React.ComponentType<P>
  ) {
    return function ProtectedComponent(props: P) {
      return (
        <ProtectedRoute permission={permission} fallback={fallback}>
          <Component {...props} />
        </ProtectedRoute>
      )
    }
  }
}

/**
 * Higher-order component for protecting routes with roles
 */
export const withRole = (
  role: string,
  fallback?: React.ReactNode
) => {
  return function RoleWrapper<P extends object>(
    Component: React.ComponentType<P>
  ) {
    return function ProtectedComponent(props: P) {
      return (
        <ProtectedRoute role={role} fallback={fallback}>
          <Component {...props} />
        </ProtectedRoute>
      )
    }
  }
}

/**
 * Admin-only component wrapper
 */
export const AdminOnly: React.FC<{ children: React.ReactNode; fallback?: React.ReactNode }> = ({
  children,
  fallback,
}) => {
  return (
    <ProtectedRoute role="admin" fallback={fallback}>
      {children}
    </ProtectedRoute>
  )
}

/**
 * Manager or Admin component wrapper
 */
export const ManagerOrAdmin: React.FC<{ children: React.ReactNode; fallback?: React.ReactNode }> = ({
  children,
  fallback,
}) => {
  return (
    <ProtectedRoute roles={['manager', 'admin']} fallback={fallback}>
      {children}
    </ProtectedRoute>
  )
}

export default ProtectedRoute
