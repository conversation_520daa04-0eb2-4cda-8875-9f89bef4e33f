import { ValidationError } from '../http/http-error.js';

/**
 * Type for objects that can be sanitized
 * More specific than Record<string, any> to provide better type checking
 */
type SanitizableObject = Record<string, unknown>;

/**
 * Interface for validation error details to ensure consistent structure
 * Used for creating detailed validation error messages
 */
export interface ValidationErrorDetail {
  field: string;
  message: string;
  code?: string;
  value?: unknown;
}

/**
 * Utility functions for validation and sanitization
 */
export class ValidationUtils {
  /**
   * Sanitize a string to prevent common injection attacks
   * @param value String to sanitize
   * @param defaultValue Value to return if input is not a string (default: '')
   * @returns Sanitized string
   */
  static sanitizeString(value: unknown, defaultValue = ''): string {
    if (value === null || value === undefined) return defaultValue;
    if (typeof value !== 'string') return String(value);

    return value
      // Remove HTML tags
      .replace(/<[^>]*>/g, '')
      // Remove script tags and their contents
      .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
      // Remove javascript: protocol
      .replace(/javascript:/gi, '')
      // Remove event handlers
      .replace(/\b(on\w+)\s*=/gi, '')
      // Remove SQL injection patterns
      .replace(/('|--|#|%27|%23|--|\bOR\b|\bAND\b|\bUNION\b|\bSELECT\b|\bDROP\b|\bALTER\b|\bDELETE\b|\bINSERT\b)/gi, '')
      // Remove potentially dangerous characters
      .replace(/[\u0000-\u001F\u007F-\u009F\u00AD\u0600-\u0604\u070F\u17B4\u17B5\u200C-\u200F\u2028-\u202F\u2060-\u206F\uFEFF\uFFF0-\uFFFF]/g, '')
      .trim();
  }

  /**
   * Sanitize an object recursively
   * Processes all string values and recursively sanitizes nested objects and arrays
   * @param obj Object to sanitize
   * @returns Sanitized object with the same structure
   */
  static sanitizeObject<T extends SanitizableObject>(obj: T): T {
    if (obj === null || obj === undefined) return obj;

    const result = { ...obj };

    for (const key in result) {
      if (!Object.prototype.hasOwnProperty.call(result, key)) continue;
      
      const value = result[key];

      if (typeof value === 'string') {
        // Sanitize strings
        result[key] = this.sanitizeString(value) as unknown as T[Extract<keyof T, string>];
      } else if (Array.isArray(value)) {
        // Recursively sanitize arrays
        result[key] = value.map(item => {
          if (typeof item === 'string') return this.sanitizeString(item);
          if (item !== null && typeof item === 'object') return this.sanitizeObject(item as SanitizableObject);
          return item;
        }) as unknown as T[Extract<keyof T, string>];
      } else if (value !== null && typeof value === 'object') {
        // Recursively sanitize nested objects
        result[key] = this.sanitizeObject(value as SanitizableObject) as unknown as T[Extract<keyof T, string>];
      }
    }

    return result;
  }

  /**
   * Validate that required fields are present in an object
   * @param data Object to validate
   * @param requiredFields Array of required field names
   * @param customMessages Optional map of custom error messages for specific fields
   * @throws ValidationError if any required field is missing
   */
  static validateRequiredFields(
    data: Record<string, unknown>,
    requiredFields: string[],
    customMessages: Record<string, string> = {}
  ): void {
    // Check if data is actually an object
    if (!data || typeof data !== 'object' || Array.isArray(data)) {
      const errorDetail: ValidationErrorDetail = { 
        field: '_data', 
        message: 'A valid object is required', 
        code: 'INVALID_DATA_OBJECT' 
      };
      throw new ValidationError('Invalid data object provided', [errorDetail]);
    }
    
    const missingFields = requiredFields.filter((field) => {
      const value = data[field];
      return value === undefined || value === null || value === '';
    });

    if (missingFields.length > 0) {
      throw new ValidationError(
        missingFields.length === 1
          ? `Required field missing: ${missingFields[0]}`
          : `Required fields missing: ${missingFields.join(', ')}`,
        missingFields.map((field): ValidationErrorDetail => ({
          field,
          message: customMessages[field] || `${field} is required`,
          code: 'REQUIRED_FIELD_MISSING'
        }))
      );
    }
  }

  /**
   * Validate that a value is within a specified range
   * @param value Value to validate
   * @param min Minimum allowed value
   * @param max Maximum allowed value
   * @param fieldName Name of the field for error message
   * @param inclusive Whether the range is inclusive (default: true)
   * @throws ValidationError if value is outside the range or not a valid number
   */
  static validateRange(
    value: unknown,
    min: number,
    max: number,
    fieldName: string,
    inclusive = true
  ): void {
    // Ensure value is a number
    const numValue = typeof value === 'string' ? parseFloat(value) : value;
    
    if (typeof numValue !== 'number' || isNaN(numValue)) {
      const errorDetail: ValidationErrorDetail = {
        field: fieldName, 
        message: 'Not a valid number', 
        value, 
        code: 'INVALID_NUMBER_FORMAT'
      };
      throw new ValidationError(`${fieldName} must be a valid number`, [errorDetail]);
    }
    
    const isOutOfRange = inclusive 
      ? (numValue < min || numValue > max) 
      : (numValue <= min || numValue >= max);
      
    if (isOutOfRange) {
      const rangeDescription = inclusive 
        ? `between ${min} and ${max} (inclusive)` 
        : `between ${min} and ${max} (exclusive)`;
        
      const errorDetail: ValidationErrorDetail = {
        field: fieldName,
        message: `Value must be ${rangeDescription}`,
        value: numValue,
        code: 'VALUE_OUT_OF_RANGE'
      };
      throw new ValidationError(`${fieldName} must be ${rangeDescription}`, [errorDetail]);
    }
  }

  /**
   * Validate that a string matches a regular expression pattern
   * @param value String to validate
   * @param pattern Regular expression pattern
   * @param fieldName Name of the field for error message
   * @param customMessage Optional custom error message
   * @throws ValidationError if input is not a string or doesn't match the pattern
   */
  static validatePattern(
    value: unknown,
    pattern: RegExp,
    fieldName: string,
    customMessage?: string
  ): void {
    // Ensure value is a string
    if (typeof value !== 'string') {
      throw new ValidationError(
        `${fieldName} must be a string`,
        [{ field: fieldName, message: 'Not a string value', value, code: 'INVALID_TYPE' }]
      );
    }
    
    if (!pattern.test(value)) {
      const errorDetail: ValidationErrorDetail = {
        field: fieldName,
        message: customMessage || 'Invalid format',
        value,
        code: 'PATTERN_MISMATCH'
      };
      throw new ValidationError(`${fieldName} has invalid format`, [errorDetail]);
    }
  }

  /**
   * Validate that a value is one of the allowed values
   * @param value Value to validate
   * @param allowedValues Array of allowed values
   * @param fieldName Name of the field for error message
   * @throws ValidationError if value is not in the allowed values
   */
  static validateEnum<T>(value: unknown, allowedValues: T[], fieldName: string): void {
    if (!allowedValues.includes(value as T)) {
      const errorDetail: ValidationErrorDetail = {
        field: fieldName,
        message: `Value must be one of: ${allowedValues.join(', ')}`,
        value,
        code: 'INVALID_ENUM_VALUE'
      };
      throw new ValidationError(`${fieldName} must be one of: ${allowedValues.join(', ')}`, [errorDetail]);
    }
  }
  
  /**
   * Validate that a string has a minimum and/or maximum length
   * @param value String to validate
   * @param fieldName Name of the field for error message
   * @param options Validation options with min and max length
   * @throws ValidationError if string doesn't meet length requirements
   */
  static validateLength(
    value: unknown,
    fieldName: string,
    options: { minLength?: number; maxLength?: number }
  ): void {
    // Ensure value is a string
    if (typeof value !== 'string') {
      throw new ValidationError(
        `${fieldName} must be a string`,
        [{ field: fieldName, message: 'Not a string value', value, code: 'INVALID_TYPE' }]
      );
    }
    
    const { minLength, maxLength } = options;
    
    if (minLength !== undefined && value.length < minLength) {
      const errorDetail: ValidationErrorDetail = {
        field: fieldName,
        message: `Minimum length is ${minLength} characters`,
        value,
        code: 'STRING_TOO_SHORT'
      };
      throw new ValidationError(`${fieldName} must be at least ${minLength} characters`, [errorDetail]);
    }
    
    if (maxLength !== undefined && value.length > maxLength) {
      const errorDetail: ValidationErrorDetail = {
        field: fieldName,
        message: `Maximum length is ${maxLength} characters`,
        value: value.length,
        code: 'STRING_TOO_LONG'
      };
      throw new ValidationError(`${fieldName} must be at most ${maxLength} characters`, [errorDetail]);
    }
  }
  
  /**
   * Validate an email address format
   * @param value Email to validate
   * @param fieldName Name of the field for error message
   * @throws ValidationError if email format is invalid
   */
  static validateEmail(value: unknown, fieldName: string): void {
    // Email regex pattern - basic but effective for most common email formats
    const emailPattern = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
    this.validatePattern(value, emailPattern, fieldName, 'Invalid email address format');
  }
}
