import { BaseModel, Inject, Injectable } from '@c-cam/core';
import { DailyAttendanceSummariesDocument } from '@/database/entities/DailyAttendanceSummariesModel';
import DailyAttendanceSummariesRepository from '@/repositories/DailyAttendanceSummariesRepository';

/**
 * Service for managing daily attendance summaries
 * Extends the BaseModel with DailyAttendanceSummariesDocument type
 */
@Injectable()
class DailyAttendanceSummariesService extends BaseModel<DailyAttendanceSummariesDocument> {
  /**
   * Create a new DailyAttendanceSummariesService
   * @param tenantId Optional tenant ID for multi-tenant operations
   */
  constructor(
    @Inject(DailyAttendanceSummariesRepository)
    repository: DailyAttendanceSummariesRepository,
  ) {
    super(repository);
  }

  /**
   * Create a new daily attendance summary
   * @param attendanceData The attendance data
   * @returns The newly created attendance summary
   */
  async createAttendanceSummary(attendanceData: {
    user_id: string;
    shift_id: string;
    work_date: Date;
    checkin_time?: Date;
    checkout_time?: Date;
    created_by: string;
    holiday_id?: string;
    is_late?: boolean;
    is_early_leave?: boolean;
    late_minutes?: number;
    early_leave_minutes?: number;
    expected_work_minutes?: number;
    total_work_minutes?: number;
    note?: string;
  }): Promise<DailyAttendanceSummariesDocument> {
    // Validate work date
    if (attendanceData.work_date > new Date()) {
      throw new Error('Work date cannot be in the future');
    }

    // Calculate late and early leave status if not provided
    if (attendanceData.checkin_time && attendanceData.checkout_time) {
      // This is a simplified calculation - in a real app, you would compare with shift times
      const workMinutes = this.calculateWorkMinutes(
        attendanceData.checkin_time,
        attendanceData.checkout_time,
      );

      if (attendanceData.expected_work_minutes === undefined) {
        // Default to 8 hours (480 minutes) if not specified
        attendanceData.expected_work_minutes = 480;
      }

      attendanceData.total_work_minutes = workMinutes;

      // Set late and early leave flags based on the calculations
      // This is just an example - real logic would be more complex
      if (attendanceData.late_minutes === undefined) {
        attendanceData.late_minutes = 0;
        attendanceData.is_late = false;
      }

      if (attendanceData.early_leave_minutes === undefined) {
        attendanceData.early_leave_minutes = 0;
        attendanceData.is_early_leave = false;
      }
    }

    // Create the new attendance summary
    return this.create(attendanceData);
  }

  /**
   * Update a daily attendance summary
   * @param id The attendance summary ID
   * @param attendanceData The data to update
   * @returns True if the attendance summary was updated, false otherwise
   */
  async updateAttendanceSummary(
    id: string,
    attendanceData: Partial<{
      user_id: string;
      shift_id: string;
      work_date: Date;
      checkin_time: Date;
      checkout_time: Date;
      holiday_id: string;
      is_late: boolean;
      is_early_leave: boolean;
      late_minutes: number;
      early_leave_minutes: number;
      expected_work_minutes: number;
      total_work_minutes: number;
      note: string;
    }>,
  ): Promise<boolean> {
    // Check if the attendance summary exists
    const summary = await this.findById(id);
    if (!summary) {
      throw new Error(`Attendance summary with ID '${id}' not found`);
    }

    // Validate work date if provided
    if (attendanceData.work_date && attendanceData.work_date > new Date()) {
      throw new Error('Work date cannot be in the future');
    }

    // Recalculate work minutes if check-in or check-out times are updated
    if (
      (attendanceData.checkin_time && !attendanceData.checkout_time) ||
      (!attendanceData.checkin_time && attendanceData.checkout_time)
    ) {
      const checkinTime = attendanceData.checkin_time || summary.checkin_time;
      const checkoutTime =
        attendanceData.checkout_time || summary.checkout_time;

      if (checkinTime && checkoutTime) {
        attendanceData.total_work_minutes = this.calculateWorkMinutes(
          checkinTime,
          checkoutTime,
        );
      }
    }

    // Update the attendance summary
    return this.update(id, attendanceData);
  }

  /**
   * Find attendance summaries by user ID
   * @param userId The user ID
   * @returns An array of attendance summaries
   */
  async findByUserId(
    userId: string,
  ): Promise<DailyAttendanceSummariesDocument[]> {
    return (this.repository as DailyAttendanceSummariesRepository).findByUserId(
      userId,
    );
  }

  /**
   * Find attendance summaries by shift ID
   * @param shiftId The shift ID
   * @returns An array of attendance summaries
   */
  async findByShiftId(
    shiftId: string,
  ): Promise<DailyAttendanceSummariesDocument[]> {
    return (
      this.repository as DailyAttendanceSummariesRepository
    ).findByShiftId(shiftId);
  }

  /**
   * Find attendance summaries by holiday ID
   * @param holidayId The holiday ID
   * @returns An array of attendance summaries
   */
  async findByHolidayId(
    holidayId: string,
  ): Promise<DailyAttendanceSummariesDocument[]> {
    return (
      this.repository as DailyAttendanceSummariesRepository
    ).findByHolidayId(holidayId);
  }

  /**
   * Find attendance summaries by work date
   * @param workDate The work date
   * @returns An array of attendance summaries
   */
  async findByWorkDate(
    workDate: Date,
  ): Promise<DailyAttendanceSummariesDocument[]> {
    return (
      this.repository as DailyAttendanceSummariesRepository
    ).findByWorkDate(workDate);
  }

  /**
   * Find attendance summaries by date range
   * @param startDate The start date of the range
   * @param endDate The end date of the range
   * @returns An array of attendance summaries
   */
  async findByDateRange(
    startDate: Date,
    endDate: Date,
  ): Promise<DailyAttendanceSummariesDocument[]> {
    return (
      this.repository as DailyAttendanceSummariesRepository
    ).findByDateRange(startDate, endDate);
  }

  /**
   * Find attendance summaries by user ID and date range
   * @param userId The user ID
   * @param startDate The start date of the range
   * @param endDate The end date of the range
   * @returns An array of attendance summaries
   */
  async findByUserIdAndDateRange(
    userId: string,
    startDate: Date,
    endDate: Date,
  ): Promise<DailyAttendanceSummariesDocument[]> {
    return (
      this.repository as DailyAttendanceSummariesRepository
    ).findByUserIdAndDateRange(userId, startDate, endDate);
  }

  /**
   * Find late attendance summaries
   * @returns An array of late attendance summaries
   */
  async findLateAttendances(): Promise<DailyAttendanceSummariesDocument[]> {
    return (
      this.repository as DailyAttendanceSummariesRepository
    ).findLateAttendances();
  }

  /**
   * Find early leave attendance summaries
   * @returns An array of early leave attendance summaries
   */
  async findEarlyLeaveAttendances(): Promise<
    DailyAttendanceSummariesDocument[]
  > {
    return (
      this.repository as DailyAttendanceSummariesRepository
    ).findEarlyLeaveAttendances();
  }

  /**
   * Calculate work minutes between check-in and check-out times
   * @param checkinTime The check-in time
   * @param checkoutTime The check-out time
   * @returns The number of minutes worked
   */
  private calculateWorkMinutes(checkinTime: Date, checkoutTime: Date): number {
    // Calculate the difference in milliseconds
    const diffMs = checkoutTime.getTime() - checkinTime.getTime();

    // Convert to minutes
    return Math.floor(diffMs / (1000 * 60));
  }
}

export default DailyAttendanceSummariesService;
