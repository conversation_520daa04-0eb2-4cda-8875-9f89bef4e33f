import { Context } from 'hono';
import {
  Controller,
  ControllerBase,
  HttpContext,
  HttpGet,
  HttpPost,
  HttpPut,
  HttpDelete,
  Inject,
  Param,
  Query,
  UnauthorizedError,
} from '@c-cam/core';
import UnitService from '../services/UnitService';

@Controller('/api/units')
export class UnitController extends ControllerBase {
  constructor(
    @Inject(UnitService) private unitService: UnitService,
  ) {
    super();
  }

  /**
   * Helper method to get authenticated user ID
   */
  private getAuthenticatedUserId(c: Context): string {
    const userId = c.get('userId');
    if (!userId) {
      throw new UnauthorizedError('You must be authenticated to access this resource');
    }
    return userId;
  }

  /**
   * Get all units
   */
  @HttpGet('/')
  async getUnits(
    @HttpContext() c: Context,
    @Query('limit') limit?: string,
    @Query('skip') skip?: string,
    @Query('sortBy') sortBy?: string,
    @Query('sortDirection') sortDirection?: string,
  ): Promise<Response> {
    this.getAuthenticatedUserId(c);

    const units = await this.unitService.find({
      limit: limit ? parseInt(limit) : undefined,
      skip: skip ? parseInt(skip) : undefined,
      sortBy,
      sortDirection,
    });

    return this.success(c, { units });
  }

  /**
   * Get a unit by ID
   */
  @HttpGet('/:id')
  async getUnitById(
    @HttpContext() c: Context,
    @Param('id') id: string,
  ): Promise<Response> {
    this.getAuthenticatedUserId(c);
    this.validateIf(!id, 'Unit ID is required');

    const unit = await this.unitService.findById(id);
    this.notFoundIf(!unit, 'Unit not found');

    return this.success(c, { unit });
  }

  /**
   * Create a new organizational unit
   */
  @HttpPost('/')
  async createUnit(@HttpContext() c: Context): Promise<Response> {
    const userId = this.getAuthenticatedUserId(c);
    const unitData = await c.req.json();

    // Sanitize the data
    const sanitizedData = this.sanitizeData(unitData);

    // Add the creator ID
    sanitizedData.created_by = userId;

    const unit = await this.unitService.createUnit(sanitizedData);
    return this.created(c, { unit }, 'Unit created successfully');
  }

  /**
   * Update an organizational unit
   */
  @HttpPut('/:id')
  async updateUnit(
    @HttpContext() c: Context,
    @Param('id') id: string,
  ): Promise<Response> {
    this.getAuthenticatedUserId(c);
    this.validateIf(!id, 'Unit ID is required');

    const unitData = await c.req.json();

    // Sanitize the data
    const sanitizedData = this.sanitizeData(unitData);

    const success = await this.unitService.updateUnit(id, sanitizedData);
    this.validateIf(!success, 'Failed to update unit');

    return this.success(c, { success: true }, 'Unit updated successfully');
  }

  /**
   * Delete an organizational unit
   */
  @HttpDelete('/:id')
  async deleteUnit(
    @HttpContext() c: Context,
    @Param('id') id: string,
  ): Promise<Response> {
    this.getAuthenticatedUserId(c);
    this.validateIf(!id, 'Unit ID is required');

    const success = await this.unitService.delete(id);
    this.validateIf(!success, 'Failed to delete unit');

    return this.success(c, { success: true }, 'Unit deleted successfully');
  }

  /**
   * Get organizational hierarchy for an organization
   */
  @HttpGet('/organization/:organizationId/hierarchy')
  async getOrganizationalHierarchy(
    @HttpContext() c: Context,
    @Param('organizationId') organizationId: string,
  ): Promise<Response> {
    this.getAuthenticatedUserId(c);
    this.validateIf(!organizationId, 'Organization ID is required');

    const hierarchy = await this.unitService.getOrganizationalHierarchy(organizationId);
    return this.success(c, { hierarchy });
  }

  /**
   * Find units by organization ID
   */
  @HttpGet('/organization/:organizationId')
  async getUnitsByOrganizationId(
    @HttpContext() c: Context,
    @Param('organizationId') organizationId: string,
  ): Promise<Response> {
    this.getAuthenticatedUserId(c);
    this.validateIf(!organizationId, 'Organization ID is required');

    const units = await this.unitService.findByOrganizationId(organizationId);
    return this.success(c, { units });
  }

  /**
   * Find units by user ID
   */
  @HttpGet('/user/:userId')
  async getUnitsByUserId(
    @HttpContext() c: Context,
    @Param('userId') userId: string,
  ): Promise<Response> {
    this.getAuthenticatedUserId(c);
    this.validateIf(!userId, 'User ID is required');

    const units = await this.unitService.findByUserId(userId);
    return this.success(c, { units });
  }

  /**
   * Find units by parent unit ID
   */
  @HttpGet('/parent/:parentUnitId')
  async getUnitsByParentUnitId(
    @HttpContext() c: Context,
    @Param('parentUnitId') parentUnitId: string,
  ): Promise<Response> {
    this.getAuthenticatedUserId(c);
    this.validateIf(!parentUnitId, 'Parent unit ID is required');

    const units = await this.unitService.findByParentUnitId(parentUnitId);
    return this.success(c, { units });
  }

  /**
   * Get root units (units without a parent)
   */
  @HttpGet('/roots')
  async getRootUnits(@HttpContext() c: Context): Promise<Response> {
    this.getAuthenticatedUserId(c);

    const units = await this.unitService.findRootUnits();
    return this.success(c, { units });
  }
}
