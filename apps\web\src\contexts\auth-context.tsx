import React, { createContext, useContext } from 'react'
import type { AuthContextType } from '@/types/auth'
import { useIdentityActions } from '@/hooks/use-identity-actions'

// Create context
const AuthContext = createContext<AuthContextType | undefined>(undefined)

// Auth provider component
export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  // Use the identity actions hook which contains all the auth logic
  const authActions = useIdentityActions()

  const contextValue: AuthContextType = {
    // State from useIdentityActions
    user: authActions.user,
    accessToken: null, // We don't expose access token directly for security
    refreshToken: null, // Refresh token is in HttpOnly cookie
    isAuthenticated: authActions.isAuthenticated,
    isLoading: authActions.isLoading,
    error: authActions.error?.message || null,

    // Actions from useIdentityActions
    login: authActions.login,
    logout: authActions.logout,
    logoutAll: authActions.logoutAll,
    refreshAccessToken: async () => {
      await authActions.refreshAccessToken()
    },
    verifyToken: authActions.verifyToken,
    clearError: () => {}, // Error clearing is handled in the hook
  }

  return (
    <AuthContext.Provider value={contextValue}>{children}</AuthContext.Provider>
  )
}

// Hook to use auth context
export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}
