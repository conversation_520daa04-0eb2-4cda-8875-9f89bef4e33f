import { useApiQuery } from '@/shared/hooks/use-api-query'

// Types for unit data
export interface Unit {
  id: string
  name: string
  description?: string
  organization_id?: string
  parent_unit_id?: string
  unit_type?: string
  status?: string
  created_at?: string
  updated_at?: string
  created_by?: string
}

export interface UnitQueryParams {
  limit?: number
  skip?: number
  sortBy?: string
  sortDirection?: 'asc' | 'desc'
}

export interface UnitListResponse {
  units: Array<Unit>
}

export interface UnitResponse {
  unit: Unit
}

/**
 * Hook to fetch all units with optional pagination and sorting
 */
export const useUnitsQuery = (params?: UnitQueryParams) => {
  return useApiQuery<UnitListResponse>(
    ['units', ...(params ? [JSON.stringify(params)] : [])],
    '/api/units',
    params
  )
}

/**
 * Hook to fetch a single unit by ID
 */
export const useUnitQuery = (id: string, enabled = true) => {
  return useApiQuery<UnitResponse>(
    ['units', id],
    `/api/units/${id}`,
    undefined,
    { enabled: enabled && !!id }
  )
}

/**
 * Hook to fetch unit by name
 */
export const useUnitByNameQuery = (name: string, enabled = true) => {
  return useApiQuery<UnitResponse>(
    ['units', 'name', name],
    `/api/units/name/${name}`,
    undefined,
    { enabled: enabled && !!name }
  )
}

/**
 * Hook to fetch units by organization ID
 */
export const useUnitsByOrganizationQuery = (organizationId: string, enabled = true) => {
  return useApiQuery<UnitListResponse>(
    ['units', 'organization', organizationId],
    `/api/units/organization/${organizationId}`,
    undefined,
    { enabled: enabled && !!organizationId }
  )
}

/**
 * Hook to fetch units by parent unit ID
 */
export const useUnitsByParentQuery = (parentUnitId: string, enabled = true) => {
  return useApiQuery<UnitListResponse>(
    ['units', 'parent', parentUnitId],
    `/api/units/parent/${parentUnitId}`,
    undefined,
    { enabled: enabled && !!parentUnitId }
  )
}

/**
 * Hook to fetch root units (units without a parent)
 */
export const useRootUnitsQuery = () => {
  return useApiQuery<UnitListResponse>(
    ['units', 'roots'],
    '/api/units/roots'
  )
}

/**
 * Hook to fetch units by status
 */
export const useUnitsByStatusQuery = (status: string, enabled = true) => {
  return useApiQuery<UnitListResponse>(
    ['units', 'status', status],
    `/api/units/status/${status}`,
    undefined,
    { enabled: enabled && !!status }
  )
}

/**
 * Hook to fetch units by type
 */
export const useUnitsByTypeQuery = (unitType: string, enabled = true) => {
  return useApiQuery<UnitListResponse>(
    ['units', 'type', unitType],
    `/api/units/type/${unitType}`,
    undefined,
    { enabled: enabled && !!unitType }
  )
}

/**
 * Hook to fetch units created by a specific user
 */
export const useUnitsByCreatorQuery = (createdBy: string, enabled = true) => {
  return useApiQuery<UnitListResponse>(
    ['units', 'created-by', createdBy],
    `/api/units/created-by/${createdBy}`,
    undefined,
    { enabled: enabled && !!createdBy }
  )
}
