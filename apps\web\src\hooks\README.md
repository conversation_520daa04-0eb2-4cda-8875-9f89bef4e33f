# API Query Hooks Documentation

This directory contains custom React Query hooks for interacting with the API endpoints. All hooks are built using the shared `useApiQuery` hook from `@/shared/hooks/use-api-query.ts`.

## Available Query Hooks

### 1. Camera Query Hooks (`use-camera-query.ts`)

**Available Hooks:**
- `useCamerasQuery(params?)` - Fetch all cameras with pagination/sorting
- `useCameraQuery(id, enabled?)` - Fetch single camera by ID
- `useCamerasByLocationQuery(location, enabled?)` - Fetch cameras by location
- `useCamerasByStatusQuery(status, enabled?)` - Fetch cameras by status
- `useCamerasByCreatorQuery(createdBy, enabled?)` - Fetch cameras by creator

**Example Usage:**
```typescript
import { useCamerasQuery, useCameraQuery } from '@/hooks/use-camera-query'

// Fetch all cameras with pagination
const { data, isLoading, error } = useCamerasQuery({
  limit: 10,
  sortBy: 'name',
  sortDirection: 'asc'
})

// Fetch single camera
const { data: camera } = useCameraQuery('camera-id')
```

### 2. Device Query Hooks (`use-device-query.ts`)

**Available Hooks:**
- `useDevicesQuery(params?)` - Fetch all edge devices
- `useDeviceQuery(id, enabled?)` - Fetch single device by ID
- `useDevicesByStatusQuery(status, enabled?)` - Fetch devices by status
- `useDevicesByLocationQuery(location, enabled?)` - Fetch devices by location
- `useDeviceByMacAddressQuery(macAddress, enabled?)` - Fetch device by MAC address
- `useDeviceByIpAddressQuery(ipAddress, enabled?)` - Fetch device by IP address
- `useDevicesByCreatorQuery(createdBy, enabled?)` - Fetch devices by creator

### 3. Member Query Hooks (`use-member-query.ts`)

**Available Hooks:**
- `useMembersQuery(params?)` - Fetch all members/users
- `useMemberQuery(id, enabled?)` - Fetch single member by ID
- `useMembersByUnitQuery(unitId, enabled?)` - Fetch members by unit
- `useMemberByUsernameQuery(username, enabled?)` - Fetch member by username
- `useMemberByEmailQuery(email, enabled?)` - Fetch member by email
- `useMembersByCreatorQuery(createdBy, enabled?)` - Fetch members by creator

### 4. Organization Query Hooks (`use-organization-query.ts`)

**Available Hooks:**
- `useOrganizationsQuery(params?)` - Fetch all organizations/tenants
- `useOrganizationQuery(id, enabled?)` - Fetch single organization by ID
- `useOrganizationByNameQuery(name, enabled?)` - Fetch organization by name
- `useOrganizationsByStatusQuery(status, enabled?)` - Fetch organizations by status
- `useOrganizationsByCreatorQuery(createdBy, enabled?)` - Fetch organizations by creator

### 5. Role Query Hooks (`use-role-query.ts`)

**Available Hooks:**
- `useRolesQuery(params?)` - Fetch all roles
- `useRoleQuery(id, enabled?)` - Fetch single role by ID
- `useRoleByNameQuery(name, enabled?)` - Fetch role by name
- `useRolesByMemberRoleQuery(memberRoleId, enabled?)` - Fetch roles by member role ID
- `useRolesByPermissionQuery(permissionId, enabled?)` - Fetch roles by permission ID
- `useRolesByCreatorQuery(createdBy, enabled?)` - Fetch roles by creator
- `useMemberRolesQuery(params?)` - Fetch all member roles
- `useUserRolesQuery(userId, enabled?)` - Fetch roles for specific user
- `useUsersWithRoleQuery(roleId, enabled?)` - Fetch users with specific role

### 6. Unit Query Hooks (`use-unit-query.ts`)

**Available Hooks:**
- `useUnitsQuery(params?)` - Fetch all units
- `useUnitQuery(id, enabled?)` - Fetch single unit by ID
- `useUnitByNameQuery(name, enabled?)` - Fetch unit by name
- `useUnitsByOrganizationQuery(organizationId, enabled?)` - Fetch units by organization
- `useUnitsByParentQuery(parentUnitId, enabled?)` - Fetch units by parent unit
- `useRootUnitsQuery()` - Fetch root units (no parent)
- `useUnitsByStatusQuery(status, enabled?)` - Fetch units by status
- `useUnitsByTypeQuery(unitType, enabled?)` - Fetch units by type
- `useUnitsByCreatorQuery(createdBy, enabled?)` - Fetch units by creator

## Common Parameters

### Query Parameters
All list queries support these optional parameters:
```typescript
interface QueryParams {
  limit?: number           // Number of items to fetch
  skip?: number           // Number of items to skip (pagination)
  sortBy?: string         // Field to sort by
  sortDirection?: 'asc' | 'desc'  // Sort direction
}
```

### Hook Parameters
- `enabled?: boolean` - Whether the query should run (default: true)
- Most hooks with parameters have `enabled` that defaults to `true` when the required parameter is provided

## Error Handling

All hooks return the standard React Query result object:
```typescript
const { data, isLoading, error, refetch, isError } = useHook()
```

## TypeScript Support

All hooks are fully typed with TypeScript interfaces for:
- Request parameters
- Response data structures
- Error types

Each hook file exports the relevant TypeScript interfaces for use in your components.
