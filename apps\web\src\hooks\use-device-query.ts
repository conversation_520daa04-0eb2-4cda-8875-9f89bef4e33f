import { useApiQuery } from '@/shared/hooks/use-api-query'

// Types for device data
export interface Device {
  id: string
  name: string
  status?: string
  location?: string
  ip_address?: string
  mac_address?: string
  device_type?: string
  created_at?: string
  updated_at?: string
  created_by?: string
}

export interface DeviceQueryParams {
  limit?: number
  skip?: number
  sortBy?: string
  sortDirection?: 'asc' | 'desc'
}

export interface DeviceListResponse {
  devices: Array<Device>
}

export interface DeviceResponse {
  device: Device
}

/**
 * Hook to fetch all edge devices with optional pagination and sorting
 */
export const useDevicesQuery = (params?: DeviceQueryParams) => {
  return useApiQuery<DeviceListResponse>(
    ['devices', ...(params ? [JSON.stringify(params)] : [])],
    '/api/edge-devices',
    params
  )
}

/**
 * Hook to fetch a single device by ID
 */
export const useDeviceQuery = (id: string, enabled = true) => {
  return useApiQuery<DeviceResponse>(
    ['devices', id],
    `/api/edge-devices/${id}`,
    undefined,
    { enabled: enabled && !!id }
  )
}

/**
 * Hook to fetch devices by status
 */
export const useDevicesByStatusQuery = (status: string, enabled = true) => {
  return useApiQuery<DeviceListResponse>(
    ['devices', 'status', status],
    `/api/edge-devices/status/${status}`,
    undefined,
    { enabled: enabled && !!status }
  )
}

/**
 * Hook to fetch devices by location
 */
export const useDevicesByLocationQuery = (location: string, enabled = true) => {
  return useApiQuery<DeviceListResponse>(
    ['devices', 'location', location],
    `/api/edge-devices/location/${location}`,
    undefined,
    { enabled: enabled && !!location }
  )
}

/**
 * Hook to fetch devices by MAC address
 */
export const useDeviceByMacAddressQuery = (macAddress: string, enabled = true) => {
  return useApiQuery<DeviceResponse>(
    ['devices', 'mac', macAddress],
    `/api/edge-devices/mac/${macAddress}`,
    undefined,
    { enabled: enabled && !!macAddress }
  )
}

/**
 * Hook to fetch devices by IP address
 */
export const useDeviceByIpAddressQuery = (ipAddress: string, enabled = true) => {
  return useApiQuery<DeviceResponse>(
    ['devices', 'ip', ipAddress],
    `/api/edge-devices/ip/${ipAddress}`,
    undefined,
    { enabled: enabled && !!ipAddress }
  )
}

/**
 * Hook to fetch devices created by a specific user
 */
export const useDevicesByCreatorQuery = (createdBy: string, enabled = true) => {
  return useApiQuery<DeviceListResponse>(
    ['devices', 'created-by', createdBy],
    `/api/edge-devices/created-by/${createdBy}`,
    undefined,
    { enabled: enabled && !!createdBy }
  )
}
