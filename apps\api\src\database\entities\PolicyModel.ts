import { Document } from 'mongoose';
import { createSchema, createModel } from '@c-cam/core';

/**
 * Policy Document Interface
 * Defines the structure of a policy document in the database
 */
export interface PolicyDocument extends Document {
  name: string;
  description?: string;
  type: string;
  conditions: Record<string, any>;
  resources: string[];
  actions: string[];
  effect: 'allow' | 'deny';
  priority: number;
  isActive: boolean;
  metadata?: Record<string, any>;
  createdBy: string;
  updatedBy?: string;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Policy Schema
 * Defines the MongoDB schema for dynamic policies
 */
const PolicySchema = createSchema(
  {
    name: {
      type: String,
      required: true,
      unique: true,
      index: true,
    },
    description: {
      type: String,
    },
    type: {
      type: String,
      required: true,
      enum: ['role', 'resource', 'time', 'tenant', 'custom'],
      index: true,
    },
    conditions: {
      type: Object,
      required: true,
      default: {},
    },
    resources: {
      type: [String],
      default: ['*'],
      index: true, // Individual index on array field
    },
    actions: {
      type: [String],
      default: ['*'],
      index: true, // Individual index on array field
    },
    effect: {
      type: String,
      enum: ['allow', 'deny'],
      required: true,
      default: 'allow',
    },
    priority: {
      type: Number,
      required: true,
      default: 100,
      index: true,
    },
    isActive: {
      type: Boolean,
      required: true,
      default: true,
      index: true,
    },
    metadata: {
      type: Object,
      default: {},
    },
    createdBy: {
      type: String,
      required: true,
    },
    updatedBy: {
      type: String,
    },
  },
  {
    timestamps: {
      createdAt: 'createdAt',
      updatedAt: 'updatedAt',
    },
  },
);

// Add compound indexes for efficient querying
PolicySchema.index({ type: 1, isActive: 1 });
// Note: Cannot create compound index on two array fields (resources and actions)
// MongoDB limitation: "cannot index parallel arrays"
// Using separate indexes instead for better query performance

// Create and export the model
const PolicyModel = createModel<PolicyDocument>('policy', PolicySchema);

export default PolicyModel;
