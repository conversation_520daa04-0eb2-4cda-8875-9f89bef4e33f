import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from './types.js';
import { Middleware as SharedMiddleware } from '../shared/types.js';

// Use the shared interface for middleware functions
export type MiddlewareFunction = SharedMiddleware.HttpFunction;

/**
 * Middleware utilities for Hono HTTP pipeline
 */
export class MiddlewareUtils {
  /**
   * Create a Hono middleware handler from a middleware function
   * @param middleware The middleware function to convert to a Hono middleware
   * @returns A Hono-compatible middleware handler
   */
  public static createMiddleware(middleware: MiddlewareFunction): RequestHandler {
    return async (c, next) => {
      try {
        await middleware(c, next);
      } catch (error) {
        // Let Hono error handlers process the error
        throw error;
      }
    };
  }
  
  /**
   * Combines multiple middleware functions into a single middleware
   * @param middlewares Array of middleware functions to combine
   * @returns A single middleware function that executes all provided middlewares in sequence
   */
  public static compose(...middlewares: MiddlewareFunction[]): RequestHandler {
    return async (c, next) => {
      // Handle empty middleware array case
      if (!middlewares || middlewares.length === 0) {
        return next ? next() : Promise.resolve();
      }

      // Create a function that executes each middleware in sequence
      const executeMiddleware = async (index: number): Promise<void> => {
        if (index >= middlewares.length) {
          // If we've executed all middlewares, call the next middleware in the chain
          return next ? next() : Promise.resolve();
        }
        
        // Get the current middleware function and check if it exists
        const middleware = middlewares[index];
        if (!middleware) {
          // Skip undefined middleware and move to the next
          return executeMiddleware(index + 1);
        }
        
        // Execute the current middleware, passing a function that executes the next middleware
        await middleware(c, () => executeMiddleware(index + 1));
      };
      
      // Start executing from the first middleware
      await executeMiddleware(0);
    };
  }
}
