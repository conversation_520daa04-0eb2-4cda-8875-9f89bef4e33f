import mongoose, { Document, Schema, Model } from 'mongoose';
import { Repository, QueryOptions } from './mongoose.js';
import { logger } from '@c-cam/logger';

/**
 * Base document interface for Mongoose models
 */
export interface IDocument extends Document {
  /**
   * Creation date
   */
  createdAt?: Date;

  /**
   * Last update date
   */
  updatedAt?: Date;

  /**
   * Soft delete flag
   */
  isDeleted?: boolean;

  /**
   * Deletion date
   */
  deletedAt?: Date;
}

/**
 * Model configuration options
 */
export interface ModelOptions {
  /**
   * Enable soft delete functionality
   */
  softDelete?: boolean;

  /**
   * Enable audit logging
   */
  auditLog?: boolean;
}

/**
 * Base schema options with timestamps
 */
export const baseSchemaOptions: mongoose.SchemaOptions = {
  timestamps: true, // Automatically add createdAt and updatedAt fields
  toJSON: {
    virtuals: true,
    transform: (_doc, ret) => {
      ret.id = ret._id;
      delete ret.__v;
      return ret;
    },
  },
  toObject: {
    virtuals: true,
    transform: (_doc, ret) => {
      ret.id = ret._id;
      delete ret.__v;
      return ret;
    },
  },
};

/**
 * Create a Mongoose model with the given name and schema
 * @param modelName The name of the model
 * @param schema The schema definition
 * @param connection Optional Mongoose connection (defaults to the default connection)
 * @returns A Mongoose model
 */
export function createModel<T extends IDocument>(
  modelName: string,
  schema: Schema,
  connection?: mongoose.Connection,
): Model<T & Document> {
  // If a connection is provided, use it
  if (connection) {
    // Check if the model already exists in this connection
    return (connection.models[modelName] || connection.model(modelName, schema)) as Model<T & Document>;
  }

  // Otherwise, use the default connection
  return (mongoose.models[modelName] || mongoose.model(modelName, schema)) as Model<T & Document>;
}

/**
 * Base model class for Mongoose documents
 */
export abstract class BaseModel<T extends IDocument> {
  protected repository: Repository<T>;
  protected options: ModelOptions;

  /**
   * Create a new model
   * @param repository The repository to use
   * @param options Model configuration options
   */
  constructor(repository: Repository<T>, options: ModelOptions = {}) {
    this.repository = repository;
    this.options = {
      softDelete: false,
      auditLog: false,
      ...options,
    };
  }

  /**
   * Apply soft delete filters to a query filter
   * @param filter The base filter
   * @returns Enhanced filter with soft delete conditions
   */
  protected applyBaseFilters(
    filter: Record<string, any> = {},
  ): Record<string, any> {
    const enhancedFilter = { ...filter };

    // Apply soft delete filter if enabled
    if (this.options.softDelete) {
      enhancedFilter.isDeleted = { $ne: true };
    }

    return enhancedFilter;
  }

  /**
   * Find a document by ID
   * @param id Document ID
   * @param options Query options
   * @returns Promise resolving to the document or null if not found
   */
  public async findById(
    id: string | mongoose.Types.ObjectId,
    options?: QueryOptions,
  ): Promise<T | null> {
    try {
      return await this.repository.findById(id, options);
    } catch (error) {
      if (this.options.auditLog) {
        logger.error('Error finding document by ID', {
          id: id.toString(),
          error: error instanceof Error ? error.message : String(error),
        });
      }
      throw error;
    }
  }

  /**
   * Find documents by a filter
   * @param filter Query filter
   * @param options Query options
   * @returns Promise resolving to an array of documents
   */
  public async find(
    filter: Record<string, any> = {},
    options?: QueryOptions,
  ): Promise<T[]> {
    try {
      const enhancedFilter = this.applyBaseFilters(filter);
      return await this.repository.find(enhancedFilter, options);
    } catch (error) {
      if (this.options.auditLog) {
        logger.error('Error finding documents', {
          filter,
          error: error instanceof Error ? error.message : String(error),
        });
      }
      throw error;
    }
  }

  /**
   * Find a single document by a filter
   * @param filter Query filter
   * @param options Query options
   * @returns Promise resolving to the document or null if not found
   */
  public async findOne(
    filter: Record<string, any>,
    options?: QueryOptions,
  ): Promise<T | null> {
    try {
      const enhancedFilter = this.applyBaseFilters(filter);
      return await this.repository.findOne(enhancedFilter, options);
    } catch (error) {
      if (this.options.auditLog) {
        logger.error('Error finding single document', {
          filter,
          error: error instanceof Error ? error.message : String(error),
        });
      }
      throw error;
    }
  }

  /**
   * Create a new document
   * @param data Document data
   * @returns Promise resolving to the created document
   */
  public async create(data: Partial<T>): Promise<T> {
    try {
      const result = await this.repository.create(data);

      if (this.options.auditLog) {
        logger.info('Document created', {
          id: (result as any)._id?.toString() || 'unknown',
        });
      }

      return result;
    } catch (error) {
      if (this.options.auditLog) {
        logger.error('Error creating document', {
          error: error instanceof Error ? error.message : String(error),
        });
      }
      throw error;
    }
  }

  /**
   * Update a document by ID
   * @param id Document ID
   * @param data Update data
   * @returns Promise resolving to true if document was modified
   */
  public async update(
    id: string | mongoose.Types.ObjectId,
    data: Partial<T>,
  ): Promise<boolean> {
    try {
      const result = await this.repository.update(id, data);

      if (this.options.auditLog && result) {
        logger.info('Document updated', {
          id: id.toString(),
        });
      }

      return result;
    } catch (error) {
      if (this.options.auditLog) {
        logger.error('Error updating document', {
          id: id.toString(),
          error: error instanceof Error ? error.message : String(error),
        });
      }
      throw error;
    }
  }

  /**
   * Delete a document by ID (soft delete if enabled)
   * @param id Document ID
   * @returns Promise resolving to true if document was deleted
   */
  public async delete(id: string | mongoose.Types.ObjectId): Promise<boolean> {
    try {
      let result: boolean;

      if (this.options.softDelete) {
        // Perform soft delete
        result = await this.repository.update(id, {
          isDeleted: true,
          deletedAt: new Date(),
        } as Partial<T>);
      } else {
        // Perform hard delete
        result = await this.repository.delete(id);
      }

      if (this.options.auditLog && result) {
        logger.info('Document deleted', {
          id: id.toString(),
          softDelete: this.options.softDelete,
        });
      }

      return result;
    } catch (error) {
      if (this.options.auditLog) {
        logger.error('Error deleting document', {
          id: id.toString(),
          error: error instanceof Error ? error.message : String(error),
        });
      }
      throw error;
    }
  }

  /**
   * Restore a soft-deleted document
   * @param id Document ID
   * @returns Promise resolving to true if document was restored
   */
  public async restore(id: string | mongoose.Types.ObjectId): Promise<boolean> {
    if (!this.options.softDelete) {
      throw new Error(
        'Restore operation is only available when soft delete is enabled',
      );
    }

    try {
      const result = await this.repository.update(id, {
        isDeleted: false,
        deletedAt: null,
      } as unknown as Partial<T>);

      if (this.options.auditLog && result) {
        logger.info('Document restored', {
          id: id.toString(),
        });
      }

      return result;
    } catch (error) {
      if (this.options.auditLog) {
        logger.error('Error restoring document', {
          id: id.toString(),
          error: error instanceof Error ? error.message : String(error),
        });
      }
      throw error;
    }
  }

  /**
   * Count documents by filter
   * @param filter Query filter
   * @returns Promise resolving to the count of documents
   */
  public async count(filter: Record<string, any> = {}): Promise<number> {
    try {
      const enhancedFilter = this.applyBaseFilters(filter);
      return await this.repository.count(enhancedFilter);
    } catch (error) {
      if (this.options.auditLog) {
        logger.error('Error counting documents', {
          filter,
          error: error instanceof Error ? error.message : String(error),
        });
      }
      throw error;
    }
  }

  /**
   * Check if a document exists
   * @param filter Query filter
   * @returns Promise resolving to true if document exists
   */
  public async exists(filter: Record<string, any>): Promise<boolean> {
    try {
      const enhancedFilter = this.applyBaseFilters(filter);
      return await this.repository.exists(enhancedFilter);
    } catch (error) {
      if (this.options.auditLog) {
        logger.error('Error checking document existence', {
          filter,
          error: error instanceof Error ? error.message : String(error),
        });
      }
      throw error;
    }
  }
}

/**
 * Create a schema with timestamps and other base options
 * @param definition The schema definition
 * @param options Additional schema options
 * @param modelOptions Model configuration options
 * @returns A Mongoose schema
 */
export function createSchema(
  definition: mongoose.SchemaDefinition,
  options?: mongoose.SchemaOptions,
  modelOptions?: ModelOptions,
): Schema {
  const enhancedDefinition = { ...definition };

  // Add soft delete fields if enabled
  if (modelOptions?.softDelete) {
    enhancedDefinition.isDeleted = {
      type: Boolean,
      default: false,
      index: true,
    };
    enhancedDefinition.deletedAt = {
      type: Date,
      default: null,
    };
  }

  const schema = new Schema(enhancedDefinition, {
    ...baseSchemaOptions,
    ...(options || {}),
  });

  // Add indexes for performance
  if (modelOptions?.softDelete) {
    schema.index({ isDeleted: 1 });
  }

  return schema;
}

/**
 * Create an enhanced schema with built-in support for common patterns
 * @param definition The schema definition
 * @param modelOptions Model configuration options
 * @param schemaOptions Additional schema options
 * @returns A Mongoose schema with enhanced functionality
 */
export function createEnhancedSchema(
  definition: mongoose.SchemaDefinition,
  modelOptions: ModelOptions = {},
  schemaOptions?: mongoose.SchemaOptions,
): Schema {
  return createSchema(definition, schemaOptions, modelOptions);
}
