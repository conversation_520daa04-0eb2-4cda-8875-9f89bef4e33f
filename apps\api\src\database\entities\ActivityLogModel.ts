import { Document } from 'mongoose';
import { createSchema, createModel } from '@c-cam/core';
import { ActivityLogAttributes } from '@c-cam/types';

/**
 * Activity Log Document Interface
 * Extends the ActivityLogAttributes (excluding id) and Document
 */
export interface ActivityLogDocument
  extends Omit<ActivityLogAttributes, 'id'>,
    Document {}

/**
 * Activity Log Schema
 * Defines the MongoDB schema for activity logs
 */
const ActivityLogSchema = createSchema({
  user_id: {
    type: String,
    ref: 'users',
    required: true,
  },
  action: { type: String, required: true },
  resource_type: { type: String, required: true },
  resource_id: { type: String, required: false },
  description: { type: String, required: true },
  metadata: { type: Object, required: false },
  ip_address: { type: String, required: false },
  user_agent: { type: String, required: false },
  created_by: { type: String, required: true },
});

// Add indexes for better query performance
ActivityLogSchema.index({ user_id: 1, created_at: -1 });
ActivityLogSchema.index({ action: 1, created_at: -1 });
ActivityLogSchema.index({ resource_type: 1, created_at: -1 });
ActivityLogSchema.index({ resource_id: 1, created_at: -1 });

// Create and export the model
const ActivityLogModel = createModel<ActivityLogDocument>(
  'activity_logs',
  ActivityLogSchema,
);

export default ActivityLogModel;
