import { Document, Model } from 'mongoose';

/**
 * Base service class that provides common CRUD operations for MongoDB models
 */
export class BaseService<T extends Document> {
  protected repository: Model<T>;

  constructor(model: Model<T>) {
    this.repository = model;
  }

  /**
   * Find multiple documents with optional pagination and sorting
   */
  async find(options: {
    limit?: number;
    skip?: number;
    sortBy?: string;
    sortDirection?: string;
    filter?: Record<string, any>;
  }): Promise<T[]> {
    const {
      limit = 100,
      skip = 0,
      sortBy,
      sortDirection,
      filter = {},
    } = options;

    let query = this.repository.find(filter);

    if (sortBy) {
      const sortOptions: Record<string, 1 | -1> = {};
      sortOptions[sortBy] = sortDirection === 'desc' ? -1 : 1;
      query = query.sort(sortOptions);
    }

    return query.limit(limit).skip(skip).exec();
  }

  /**
   * Find a document by ID
   */
  async findById(id: string): Promise<T | null> {
    return this.repository.findById(id).exec();
  }

  /**
   * Create a new document
   */
  async create(data: Partial<T>): Promise<T> {
    return this.repository.create(data);
  }

  /**
   * Update a document by ID
   */
  async update(id: string, data: Partial<T>): Promise<T | null> {
    return this.repository.findByIdAndUpdate(id, data, { new: true }).exec();
  }

  /**
   * Delete a document by ID
   */
  async delete(id: string): Promise<boolean> {
    const result = await this.repository.findByIdAndDelete(id).exec();
    return !!result;
  }

  /**
   * Count documents matching a filter
   */
  async count(filter: Record<string, any> = {}): Promise<number> {
    return this.repository.countDocuments(filter).exec();
  }
}
