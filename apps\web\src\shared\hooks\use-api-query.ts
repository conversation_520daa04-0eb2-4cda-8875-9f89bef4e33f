import { toast } from 'sonner'
import { useMutation, useQuery } from '@tanstack/react-query'
import type { UseMutationOptions, UseQueryOptions } from '@tanstack/react-query'
import { api } from '@/configs/axios'

/**
 * Custom hook for API queries using React Query
 * @param queryKey The query key for React Query cache
 * @param url The API endpoint URL
 * @param params Optional query parameters
 * @param options Additional React Query options
 * @returns React Query result
 */
export function useApiQuery<TData = unknown, TError = Error>(
  queryKey: string | Array<string>,
  url: string,
  params?: Record<string, any>,
  options?: Omit<UseQueryOptions<TData, TError, TData>, 'queryKey' | 'queryFn'>,
) {
  const queryKeyArray = Array.isArray(queryKey) ? queryKey : [queryKey]

  return useQuery<TData, TError>({
    queryKey: queryKeyArray,
    queryFn: async () => {
      try {
        // The api.get function already extracts the data from the response
        return await api.get<TData>(url, params)
      } catch (error) {
        console.error(`Error fetching data from ${url}:`, error)
        throw error
      }
    },
    ...options,
  })
}

/**
 * Custom hook for API mutations using React Query
 * @param url The API endpoint URL
 * @param method The HTTP method to use
 * @param options Additional React Query options
 * @returns React Query mutation result
 */
export function useApiMutation<
  TData = unknown,
  TVariables = unknown,
  TError = Error,
>(
  url: string,
  method: 'POST' | 'PUT' | 'PATCH' | 'DELETE' = 'POST',
  options?: Omit<UseMutationOptions<TData, TError, TVariables>, 'mutationFn'>,
) {
  const mutationFn = async (variables: TVariables) => {
    switch (method) {
      case 'POST':
        return await api.post<TData>(url, variables)
      case 'PUT':
        return await api.put<TData>(url, variables)
      case 'PATCH':
        return await api.patch<TData>(url, variables)
      case 'DELETE':
        return await api.delete<TData>(url, variables)
      default:
        return await api.post<TData>(url, variables)
    }
  }

  return useMutation<TData, TError, TVariables>({
    mutationFn,
    onSuccess: (data, variables, context) => {
      // Call the onSuccess callback if provided
      if (options?.onSuccess) {
        options.onSuccess(data, variables, context)
      }

      // Show success toast if not disabled
      if (options?.meta?.showSuccessToast !== false) {
        const message = (options?.meta?.successMessage ||
          'Operation completed successfully') as string
        toast.success(message)
      }
    },
    onError: (error, variables, context) => {
      // Call the onError callback if provided
      if (options?.onError) {
        options.onError(error, variables, context)
      }

      // Show error toast if not disabled
      if (options?.meta?.showErrorToast !== false) {
        const message = (options?.meta?.errorMessage ||
          (error instanceof Error
            ? error.message
            : 'An error occurred')) as string
        toast.error(message)
      }
    },
    ...options,
  })
}

/**
 * Custom hook for creating a new resource
 */
export function useCreateMutation<
  TData = unknown,
  TVariables = unknown,
  TError = Error,
>(
  url: string,
  options?: Omit<UseMutationOptions<TData, TError, TVariables>, 'mutationFn'>,
) {
  return useApiMutation<TData, TVariables, TError>(url, 'POST', {
    meta: {
      successMessage: 'Resource created successfully',
      ...options?.meta,
    },
    ...options,
  })
}

/**
 * Custom hook for updating a resource
 */
export function useUpdateMutation<
  TData = unknown,
  TVariables = unknown,
  TError = Error,
>(
  url: string,
  options?: Omit<UseMutationOptions<TData, TError, TVariables>, 'mutationFn'>,
) {
  return useApiMutation<TData, TVariables, TError>(url, 'PUT', {
    meta: {
      successMessage: 'Resource updated successfully',
      ...options?.meta,
    },
    ...options,
  })
}

/**
 * Custom hook for deleting a resource
 */
export function useDeleteMutation<
  TData = unknown,
  TVariables = unknown,
  TError = Error,
>(
  url: string,
  options?: Omit<UseMutationOptions<TData, TError, TVariables>, 'mutationFn'>,
) {
  return useApiMutation<TData, TVariables, TError>(url, 'DELETE', {
    meta: {
      successMessage: 'Resource deleted successfully',
      ...options?.meta,
    },
    ...options,
  })
}
