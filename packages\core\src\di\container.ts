import 'reflect-metadata';
import {
  Constructor,
  IServiceCollection,
  IServiceProvider,
  IServiceScope,
  ServiceDescriptor,
  ServiceLifetime,
} from './type';
import { INJECT_PARAM_METADATA_KEY } from './injectable';
import { IDisposable } from '../hosting/interfaces';
import { logger } from '@c-cam/logger';
import {
  InjectableIdentifier,
  instances,
  Metadata,
  Injectable,
  Constructable,
  INJECT_METADATA_KEY,
} from '../decorators/injectable.decorator';

const disposableServices = new Set<IDisposable>();

/**
 * Container implementation for dependency injection
 */
export class ServiceCollection implements IServiceCollection {
  private readonly _services: ServiceDescriptor[] = [];

  /**
   * Add a singleton service to the collection
   */
  public AddSingleton<T>(
    serviceType: Constructor<T>,
    implementationType?: Constructor<T>,
  ): IServiceCollection {
    return this.Add(
      serviceType,
      implementationType || serviceType,
      ServiceLifetime.Singleton,
    );
  }

  /**
   * Add a scoped service to the collection
   */
  public AddScoped<T>(
    serviceType: Constructor<T>,
    implementationType?: Constructor<T>,
  ): IServiceCollection {
    return this.Add(
      serviceType,
      implementationType || serviceType,
      ServiceLifetime.Scoped,
    );
  }

  /**
   * Add a transient service to the collection
   */
  public AddTransient<T>(
    serviceType: Constructor<T>,
    implementationType?: Constructor<T>,
  ): IServiceCollection {
    return this.Add(
      serviceType,
      implementationType || serviceType,
      ServiceLifetime.Transient,
    );
  }

  /**
   * Add a service to the collection
   */
  private Add<T>(
    serviceType: Constructor<T>,
    implementationType: Constructor<T>,
    lifetime: ServiceLifetime,
  ): IServiceCollection {
    this._services.push({
      serviceType,
      implementationType,
      lifetime,
    });
    return this;
  }

  /**
   * Build the service provider
   */
  public BuildServiceProvider(): IServiceProvider {
    return new ServiceProvider(this._services);
  }
}

class ContainerClass {
  /** Stack to track types being resolved to detect circular dependencies */
  private readonly resolutionStack: InjectableIdentifier[] = [];

  /**
   * Checks if a type is registered in the container
   */
  has<T>(type: InjectableIdentifier<T>): boolean {
    return instances.has(type);
  }

  /**
   * Lọc các instance theo predicate cho trước
   */
  filter<T>(predicate: (instance: T) => boolean): T[] {
    const foundInstances: T[] = [];

    for (const metadata of instances.values()) {
      if (metadata.instance && predicate(metadata.instance as T)) {
        foundInstances.push(metadata.instance as T);
      }
    }

    return foundInstances;
  }

  /**
   * Lọc các instance theo predicate cho trước
   */
  find<T>(predicate: (instance: T) => boolean): T | null {
    for (const metadata of instances.values()) {
      if (metadata.instance && predicate(metadata.instance as T)) {
        return metadata.instance as T;
      }
    }

    return null;
  }

  /**
   * Retrieves or creates an instance of the specified type from the container
   */
  get<T>(type: InjectableIdentifier<T>): T {
    const { resolutionStack } = this;
    const metadata = instances.get(type) as Metadata<T>;
    if (!metadata) {
      if (resolutionStack.length) return undefined as T;

      const typeName = typeof type === 'string' ? type : type.name;
      throw new Error(`${typeName} is not decorated with ${Injectable.name}`);
    }

    if (metadata?.instance) return metadata.instance as T;

    if (typeof type === 'string' && !metadata.factory) {
      throw new Error(`String identifier '${type}' has no factory defined`);
    }

    resolutionStack.push(type);

    try {
      let instance: T;

      if (metadata?.factory) {
        instance = metadata.factory();
      } else {
        // Get parameter types from TypeScript metadata
        const paramTypes = (Reflect.getMetadata('design:paramtypes', type) ??
          []) as Constructable[];

        // Get injected types from our custom metadata
        // Try to get metadata from both the constructor and prototype
        let injectMetadata =
          Reflect.getMetadata(INJECT_PARAM_METADATA_KEY, type) || [];

        // If we didn't find metadata on the constructor, try the prototype
        if (
          injectMetadata.filter(Boolean).length === 0 &&
          typeof type === 'function' &&
          type.prototype
        ) {
          const prototypeMetadata = Reflect.getMetadata(
            INJECT_PARAM_METADATA_KEY,
            type.prototype,
          );
          if (
            prototypeMetadata &&
            prototypeMetadata.filter(Boolean).length > 0
          ) {
            injectMetadata = prototypeMetadata;
            logger.debug(`Found metadata on prototype instead of constructor`);
          }
        }

        // Log for debugging
        logger.debug(
          `Creating instance of ${typeof type === 'string' ? type : type.name}`,
        );
        logger.debug(`TypeScript metadata: ${paramTypes.length} parameters`);
        logger.debug(
          `Custom metadata: ${
            injectMetadata.filter(Boolean).length
          } injected parameters`,
        );

        // Determine the number of parameters to inject
        // Use the maximum length between TypeScript metadata and our custom metadata
        const paramCount = Math.max(
          paramTypes.length,
          injectMetadata.filter(Boolean).length,
        );
        logger.debug(`Will inject ${paramCount} parameters`);

        // Create dependencies array by processing each parameter
        const dependencies: any[] = [];
        for (let index = 0; index < paramCount; index++) {
          // Get the parameter type from both sources
          const injectedType = injectMetadata[index];
          const paramType = paramTypes[index];

          let dependency: any;

          // If we have an explicitly injected type for this parameter, use it
          if (injectedType !== undefined) {
            logger.debug(
              `Using explicitly injected type for parameter ${index}: ${
                typeof injectedType === 'string'
                  ? injectedType
                  : injectedType.name
              }`,
            );
            try {
              dependency = this.get(injectedType);
              logger.debug(
                `Successfully resolved injected dependency for parameter ${index}`,
              );
            } catch (error) {
              logger.warn(
                `Failed to inject parameter ${index} in ${
                  typeof type === 'string' ? type : type.name
                }:`,
                error,
              );
              // Fall back to the TypeScript type if injection fails
              logger.debug(
                `Falling back to TypeScript type for parameter ${index}: ${
                  paramType?.name || 'undefined'
                }`,
              );

              // If we have a TypeScript type, try to use it
              if (paramType !== undefined) {
                try {
                  dependency = this.get(paramType);
                  logger.debug(
                    `Successfully resolved TypeScript dependency for parameter ${index}`,
                  );
                } catch (fallbackError) {
                  logger.error(
                    `Failed to resolve dependency for parameter ${index} using both methods:`,
                    fallbackError,
                  );
                  throw new Error(
                    `Failed to resolve dependency for parameter ${index} in ${
                      typeof type === 'string' ? type : type.name
                    }`,
                  );
                }
              } else {
                throw new Error(
                  `No type information available for parameter ${index} in ${
                    typeof type === 'string' ? type : type.name
                  }`,
                );
              }
            }
          } else if (paramType !== undefined) {
            // Use TypeScript type if no explicit injection
            logger.debug(
              `No explicit injection for parameter ${index}, using TypeScript type: ${
                paramType.name || 'undefined'
              }`,
            );
            try {
              dependency = this.get(paramType);
              logger.debug(
                `Successfully resolved TypeScript dependency for parameter ${index}`,
              );
            } catch (error) {
              logger.error(
                `Failed to resolve TypeScript dependency for parameter ${index}:`,
                error,
              );
              throw new Error(
                `Failed to resolve dependency for parameter ${index} in ${
                  typeof type === 'string' ? type : type.name
                }`,
              );
            }
          } else {
            // No type information available for this parameter
            logger.error(
              `No type information available for parameter ${index} in ${
                typeof type === 'string' ? type : type.name
              }`,
            );
            throw new Error(
              `No type information available for parameter ${index} in ${
                typeof type === 'string' ? type : type.name
              }`,
            );
          }

          // Add the resolved dependency to the array
          dependencies.push(dependency);
        }

        logger.debug(
          `Creating instance of ${
            typeof type === 'string' ? type : type.name
          } with ${dependencies.length} resolved dependencies`,
        );
        try {
          instance = new (type as Constructable)(...dependencies) as T;
          logger.debug(
            `Successfully created instance of ${
              typeof type === 'string' ? type : type.name
            }`,
          );
        } catch (error) {
          logger.error(
            `Failed to create instance of ${
              typeof type === 'string' ? type : type.name
            }:`,
            error,
          );
          throw error;
        }
      }

      instances.set(type, { ...metadata, instance });

      // Register as disposable if it implements IDisposable
      if (this.isDisposable(instance)) {
        this.registerDisposable(instance);
      }

      return instance;
    } catch (error) {
      if (
        error instanceof TypeError &&
        error.message.toLowerCase().includes('abstract')
      ) {
        const typeName = typeof type === 'string' ? type : type.name;
        throw new Error(
          `${typeName} is an abstract class, and cannot be instantiated`,
        );
      }
      throw error;
    } finally {
      resolutionStack.pop();
    }
  }

  /**
   * Manually sets an instance for a specific type in the container
   */
  set<T>(type: InjectableIdentifier<T> | string, instance: T): T {
    const metadata = instances.get(type) ?? {};
    instances.set(type, { ...metadata, instance });
    return instance;
  }

  /**
   * Register a type with the container and optionally create an instance immediately
   * @param type Type to register
   * @param factory Optional factory function to create the instance
   * @param instantiate Whether to instantiate the type immediately (default: true)
   */
  register<T>(
    type: Constructable<T> | string,
    factory?: () => T,
    instantiate = true,
  ): void {
    // Check if already registered
    if (instances.has(type)) {
      const existing = instances.get(type);
      // Update factory if provided
      if (factory) {
        instances.set(type, { ...existing, factory });
      }
    } else {
      // Register new type
      const metadata: Metadata<T> = { factory };
      instances.set(type, metadata);
    }

    // Instantiate if requested
    if (instantiate) {
      try {
        this.get(type);
      } catch (error) {
        logger.error(
          `Failed to instantiate ${
            typeof type === 'string' ? type : type.name
          }:`,
          error,
        );
      }
    }
  }

  /** Clears all instantiated instances from the container while preserving type registrations */
  reset(): void {
    for (const metadata of instances.values()) {
      delete metadata.instance;
    }
  }

  /**
   * Register a service instance as disposable for cleanup
   * @param instance Service instance that implements IDisposable
   */
  registerDisposable(instance: IDisposable): void {
    disposableServices.add(instance);
  }

  /**
   * Dispose all registered disposable services
   * @returns Promise that resolves when all services have been disposed
   */
  async disposeAll(): Promise<void> {
    const disposePromises: Promise<void>[] = [];

    // Call dispose on all registered services
    for (const service of disposableServices) {
      try {
        disposePromises.push(service.dispose());
      } catch (error) {
        logger.error('Error disposing service:', error);
      }
    }

    // Wait for all dispose operations to complete
    await Promise.all(disposePromises);

    // Clear the registry
    disposableServices.clear();
  }

  /**
   * Check if an object implements IDisposable
   * @param obj Object to check
   * @returns True if the object implements IDisposable
   */
  private isDisposable(obj: any): obj is IDisposable {
    return obj && typeof obj.dispose === 'function';
  }
}

export const Container = new ContainerClass();

/**
 * Service provider implementation
 */
class ServiceProvider implements IServiceProvider {
  private readonly _singletonInstances = new Map<any, any>();
  private readonly _descriptors = new Map<any, ServiceDescriptor>();

  constructor(descriptors: ServiceDescriptor[]) {
    // Register the service provider itself
    this._singletonInstances.set(ServiceProvider, this);
    // Register this instance as the IServiceProvider implementation
    // Using type as string to avoid TS2693 error
    this._singletonInstances.set('IServiceProvider', this);

    // Register all descriptors
    for (const descriptor of descriptors) {
      this._descriptors.set(descriptor.serviceType, descriptor);
    }
  }

  /**
   * Get a service from the provider
   */
  public GetService<T>(serviceType: Constructor<T>): T {
    return this.resolveService(serviceType);
  }

  /**
   * Create a new scope
   */
  public CreateScope(): IServiceScope {
    return new ServiceScope(this);
  }

  /**
   * Resolve a service
   */
  private resolveService<T>(
    serviceType: Constructor<T>,
    scopedInstances?: Map<any, any>,
  ): T {
    // Check if we already have an instance for singletons
    if (this._singletonInstances.has(serviceType)) {
      return this._singletonInstances.get(serviceType);
    }

    // Check if we have a scoped instance
    if (scopedInstances && scopedInstances.has(serviceType)) {
      return scopedInstances.get(serviceType);
    }

    // Get the descriptor
    const descriptor = this._descriptors.get(serviceType);
    if (!descriptor) {
      throw new Error(`Service ${serviceType.name} is not registered`);
    }

    // Create the instance
    const instance = this.createInstance(
      descriptor.implementationType,
      scopedInstances,
    );

    // Store the instance based on lifetime
    if (descriptor.lifetime === ServiceLifetime.Singleton) {
      this._singletonInstances.set(serviceType, instance);
    } else if (
      descriptor.lifetime === ServiceLifetime.Scoped &&
      scopedInstances
    ) {
      scopedInstances.set(serviceType, instance);
    }

    return instance as T;
  }

  /**
   * Create an instance of a type
   */
  private createInstance<T>(
    type: Constructor<T>,
    scopedInstances?: Map<any, any>,
  ): T {
    // Get constructor parameters
    const paramTypes = Reflect.getMetadata('design:paramtypes', type) || [];
    const injections = Reflect.getMetadata(INJECT_METADATA_KEY, type) || [];

    // Resolve dependencies
    const params = paramTypes.map((paramType: any, index: number) => {
      // Check if we have an explicit injection
      const injection = injections.find((i: any) => i.index === index);
      const serviceType = injection ? injection.type : paramType;

      return this.resolveService(serviceType, scopedInstances);
    });

    // Create the instance
    return new type(...params);
  }
}

/**
 * Service scope implementation
 */
class ServiceScope implements IServiceScope {
  private readonly _scopedInstances = new Map<any, any>();
  private _disposed = false;

  constructor(private readonly _rootProvider: ServiceProvider) {}

  /**
   * Get the service provider for this scope
   */
  public get serviceProvider(): IServiceProvider {
    if (this._disposed) {
      throw new Error('Service scope has been disposed');
    }

    return {
      GetService: <T>(serviceType: Constructor<T>): T => {
        return this._rootProvider['resolveService'](
          serviceType,
          this._scopedInstances,
        );
      },
      CreateScope: (): IServiceScope => {
        return this._rootProvider.CreateScope();
      },
    };
  }

  /**
   * Dispose the scope
   */
  public dispose(): void {
    this._disposed = true;
    this._scopedInstances.clear();
  }
}
