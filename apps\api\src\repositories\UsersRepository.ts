import UsersModel, { UsersDocument } from '@/database/entities/UsersModel';
import { Repository, Injectable, Inject } from '@c-cam/core';

/**
 * Repository for managing users
 * Extends the BaseRepository with UsersDocument type
 */
@Injectable()
class UsersRepository extends Repository<UsersDocument> {
  constructor() {
    super(UsersModel);
  }

  /**
   * Find a user by username
   * @param username The username to search for
   * @returns A promise that resolves to a user or null if not found
   */
  async findByUsername(username: string): Promise<UsersDocument | null> {
    return this.findOne({ username });
  }

  async findByUsernameSelectPassword(username: string): Promise<UsersDocument | null> {
    const user = (await this.getModel()
      .findOne({ username })
      .select('+password')
      .exec()) as UsersDocument | null;
    return user;
  }

  /**
   * Find a user by email
   * @param email The email to search for
   * @returns A promise that resolves to a user or null if not found
   */
  async findByEmail(email: string): Promise<UsersDocument | null> {
    return this.findOne({ email });
  }

  /**
   * Find a user by code
   * @param code The user code to search for
   * @returns A promise that resolves to a user or null if not found
   */
  async findByCode(code: string): Promise<UsersDocument | null> {
    return this.findOne({ code });
  }

  /**
   * Find users by unit ID
   * @param unitId The unit ID to search for
   * @returns A promise that resolves to an array of users
   */
  async findByUnitId(unitId: string): Promise<UsersDocument[]> {
    return this.find({ unit_id: unitId });
  }

  /**
   * Find users by member role ID
   * @param memberRoleId The member role ID to search for
   * @returns A promise that resolves to an array of users
   */
  async findByMemberRoleId(memberRoleId: string): Promise<UsersDocument[]> {
    return this.find({ member_role_id: memberRoleId });
  }

  /**
   * Find users by face ID
   * @param faceId The face ID to search for
   * @returns A promise that resolves to an array of users
   */
  async findByFaceId(faceId: string): Promise<UsersDocument[]> {
    return this.find({ face_id: faceId });
  }
}

export default UsersRepository;
