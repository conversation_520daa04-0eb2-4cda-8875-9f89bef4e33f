import { LogOut } from 'lucide-react'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'

export function NavUser({
  user,
  onLogout,
}: {
  user: {
    name: string
    email: string
    avatar: string
  }
  onLogout?: () => void
}) {
  const handleLogout = () => {
    if (onLogout) {
      onLogout()
    } else {
      // Default logout behavior - you can customize this
      console.log('Logout clicked')
      // Example: redirect to login page or clear auth state
      // window.location.href = '/auth/login'
    }
  }
  return (
    <div className="flex items-center">
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <a className="flex items-center gap-2 rounded-lg transition-colors cursor-pointer">
            <div className="grid flex-1 text-right text-sm leading-tight">
              <span className="truncate font-medium">{user.name}</span>
              <span className="truncate text-xs">{user.email}</span>
            </div>
            <div className="flex items-center">
              <div className="bg-[#008FD31A] text-[#008FD3] flex items-center justify-center rounded-full px-1.5 py-1.5 min-w-[32px] max-h-[32px]">
                <span className="text-[12px] font-bold">
                  {(() => {
                    const words = user.name.split(' ').filter(Boolean)
                    if (words.length === 1) {
                      const firstWord = words[0]
                      if (firstWord && firstWord.length > 0) {
                        return firstWord.slice(0, 2).toUpperCase()
                      }
                    } else if (words.length > 1) {
                      const firstWord = words[0]
                      const lastWord = words[words.length - 1]
                      if (firstWord && firstWord.length > 0 && lastWord && lastWord.length > 0) {
                        return (firstWord.charAt(0) + lastWord.charAt(0)).toUpperCase()
                      }
                    }
                    return ''
                  })()}
                </span>
              </div>
            </div>
          </a>
        </DropdownMenuTrigger>
        <DropdownMenuContent
          className="w-(--radix-dropdown-menu-trigger-width) mt-2 rounded-lg"
          align="end"
          sideOffset={4}
        >
          <DropdownMenuItem onClick={handleLogout}>
            <LogOut className="mr-2 h-4 w-4" />
            <span>Sign out</span>
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  )
}
