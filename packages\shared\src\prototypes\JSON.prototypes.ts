/**
 * JSON prototype extensions for improved JSON handling
 */

declare global {
  interface JSON {
    /**
     * Safely parse JSON string without throwing exceptions
     * @param text The string to parse as JSON
     * @param defaultValue The default value to return if parsing fails
     */
    tryParse<T>(text: string, defaultValue?: T): T | null;

    /**
     * Safely stringify an object without throwing exceptions
     * @param value The value to convert to a JSON string
     * @param defaultValue The default value to return if stringify fails
     */
    tryStringify(value: any, defaultValue?: string): string | null;

    /**
     * Pretty print a JSON object with indentation
     * @param value The value to pretty print
     * @param space Number of spaces for indentation
     */
    prettyPrint(value: any, space?: number): string;
  }
}

// Implementation
JSON.tryParse = function<T>(text: string, defaultValue: T | null = null): T | null {
  try {
    return JSON.parse(text) as T;
  } catch {
    return defaultValue;
  }
};

JSON.tryStringify = function(value: any, defaultValue: string | null = null): string | null {
  try {
    return JSON.stringify(value);
  } catch {
    return defaultValue;
  }
};

JSON.prettyPrint = function(value: any, space: number = 2): string {
  try {
    return JSON.stringify(value, null, space);
  } catch {
    return String(value);
  }
};

export {}; // This export is needed to make the file a module