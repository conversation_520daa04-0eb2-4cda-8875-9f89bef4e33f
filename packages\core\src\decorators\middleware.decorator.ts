import 'reflect-metadata';
import { Context, Next } from 'hono';

// Metadata key
export const MIDDLEWARE_METADATA = 'middlewares';

// Middleware interface
export interface IMiddleware {
  use(c: Context, next: Next): Promise<void> | void;
}

/**
 * Decorator for applying middleware to controllers or controller methods
 */
export function UseMiddleware(...middlewares: (IMiddleware | Function)[]) {
  return function (
    target: any,
    propertyKey?: string | symbol,
    descriptor?: PropertyDescriptor
  ) {
    if (propertyKey && descriptor) {
      // Method middleware
      const existingMiddlewares =
        Reflect.getMetadata(MIDDLEWARE_METADATA, target, propertyKey) || [];
      Reflect.defineMetadata(
        MIDDLEWARE_METADATA,
        [...existingMiddlewares, ...middlewares],
        target,
        propertyKey
      );
    } else {
      // Class middleware
      const existingMiddlewares =
        Reflect.getMetadata(MIDDLEWARE_METADATA, target) || [];
      Reflect.defineMetadata(
        MIDDLEWARE_METADATA,
        [...existingMiddlewares, ...middlewares],
        target
      );
    }
  };
}
