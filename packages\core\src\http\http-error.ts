import { StatusCode } from 'hono/utils/http-status';

/**
 * Base class for HTTP errors
 * Extends the built-in Error class to provide HTTP-specific error handling
 */
export class HttpError extends Error {
  /**
   * HTTP status code for the error
   */
  statusCode: StatusCode;
  
  /**
   * Error code for API clients (e.g., 'NOT_FOUND', 'VALIDATION_ERROR')
   */
  code: string;
  
  /**
   * Optional additional error details
   */
  details?: unknown[];

  /**
   * Creates a new HTTP error
   * @param message Error message
   * @param statusCode HTTP status code
   * @param code Error code for API clients
   * @param details Additional error details
   */
  constructor(
    message: string,
    statusCode: StatusCode = 500,
    code = 'INTERNAL_SERVER_ERROR',
    details?: unknown[]
  ) {
    super(message);
    this.name = this.constructor.name;
    this.statusCode = statusCode;
    this.code = code;
    this.details = details;
    
    // Captures the stack trace in modern environments
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, this.constructor);
    }
  }
}

/**
 * Error representing a 400 Bad Request response
 * Used when the request is malformed or contains invalid data
 */
export class BadRequestError extends HttpError {
  /**
   * Creates a new Bad Request error
   * @param message Error message (default: 'Bad Request')
   * @param code Error code for API clients (default: 'BAD_REQUEST')
   * @param details Additional error details
   */
  constructor(message = 'Bad Request', code = 'BAD_REQUEST', details?: unknown[]) {
    super(message, 400, code, details);
  }
}

/**
 * Error representing a 401 Unauthorized response
 * Used when authentication is required but missing or invalid
 */
export class UnauthorizedError extends HttpError {
  /**
   * Creates a new Unauthorized error
   * @param message Error message (default: 'Unauthorized')
   * @param code Error code for API clients (default: 'UNAUTHORIZED')
   * @param details Additional error details
   */
  constructor(message = 'Unauthorized', code = 'UNAUTHORIZED', details?: unknown[]) {
    super(message, 401, code, details);
  }
}

/**
 * Error representing a 403 Forbidden response
 * Used when the user is authenticated but lacks sufficient permissions
 */
export class ForbiddenError extends HttpError {
  /**
   * Creates a new Forbidden error
   * @param message Error message (default: 'Forbidden')
   * @param code Error code for API clients (default: 'FORBIDDEN')
   * @param details Additional error details
   */
  constructor(message = 'Forbidden', code = 'FORBIDDEN', details?: unknown[]) {
    super(message, 403, code, details);
  }
}

/**
 * Error representing a 404 Not Found response
 * Used when the requested resource does not exist
 */
export class NotFoundError extends HttpError {
  /**
   * Creates a new Not Found error
   * @param message Error message (default: 'Not Found')
   * @param code Error code for API clients (default: 'NOT_FOUND')
   * @param details Additional error details
   */
  constructor(message = 'Not Found', code = 'NOT_FOUND', details?: unknown[]) {
    super(message, 404, code, details);
  }
}

/**
 * Error representing validation failures
 * Extends BadRequestError as validation errors are a specific type of 400 errors
 */
export class ValidationError extends BadRequestError {
  /**
   * Creates a new Validation error
   * @param message Error message (default: 'Validation Error')
   * @param details Additional error details, typically validation constraint violations
   */
  constructor(message = 'Validation Error', details?: unknown[]) {
    super(message, 'VALIDATION_ERROR', details);
  }
}

/**
 * Error representing a 409 Conflict response
 * Used when the request conflicts with the current state of the server
 * For example, when trying to create a resource that already exists
 */
export class ConflictError extends HttpError {
  /**
   * Creates a new Conflict error
   * @param message Error message (default: 'Conflict')
   * @param code Error code for API clients (default: 'CONFLICT')
   * @param details Additional error details
   */
  constructor(message = 'Conflict', code = 'CONFLICT', details?: unknown[]) {
    super(message, 409, code, details);
  }
}

/**
 * Error representing a 500 Internal Server Error response
 * Used for unexpected server errors that should be logged and investigated
 */
export class InternalServerError extends HttpError {
  /**
   * Creates a new Internal Server Error
   * @param message Error message (default: 'Internal Server Error')
   * @param code Error code for API clients (default: 'INTERNAL_SERVER_ERROR')
   * @param details Additional error details
   */
  constructor(
    message = 'Internal Server Error',
    code = 'INTERNAL_SERVER_ERROR',
    details?: unknown[]
  ) {
    super(message, 500, code, details);
  }
}
