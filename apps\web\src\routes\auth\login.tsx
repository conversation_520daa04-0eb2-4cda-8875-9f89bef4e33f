import { useEffect } from 'react'
import { Link, createFileRoute, useNavigate } from '@tanstack/react-router'
import { useForm } from 'react-hook-form'
import { z } from 'zod'
import { zodResolver } from '@hookform/resolvers/zod'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import AuthLayout from '@/components/layout/auth.layout'
import { useAuth } from '@/providers/global-provider'

const loginSchema = z.object({
  username: z.string().min(1, 'Vui lòng nhập tên đăng nhập'),
  password: z.string().min(1, 'Vui lòng nhập mật khẩu'),
})

type LoginFormValues = z.infer<typeof loginSchema>

export const Route = createFileRoute('/auth/login')({
  component: RouteComponent,
})

function RouteComponent() {
  const { login, isAuthenticated, isLoading, error } = useAuth()
  const navigate = useNavigate()

  // Check if currently logging in (for button state)
  const isLoggingIn = isLoading

  const form = useForm<LoginFormValues>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      username: '',
      password: '',
    },
  })

  // Redirect to dashboard if already authenticated
  useEffect(() => {
    if (isAuthenticated) {
      navigate({ to: '/dashboard' })
    }
  }, [isAuthenticated, navigate])

  // No need to clear error manually as it's handled in the hook

  const onSubmit = async (data: LoginFormValues) => {
    try {
      await login({
        username: data.username,
        password: data.password,
      })
      // Navigation will be handled by the useEffect above
    } catch (loginError) {
      // Error is handled by the auth context
      console.error('Login failed:', loginError)
    }
  }

  return (
    <AuthLayout>
      <div className="mb-6 space-y-1 w-full">
        <h1 className="text-2xl font-semibold text-[#1f2329]">Đăng nhập</h1>
        <p className="text-base text-[#1f2329]/50">
          Nhập tài khoản và mật khẩu để đăng nhập
        </p>
        {error && (
          <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-md">
            <p className="text-sm text-red-600">
              {error || 'Có lỗi xảy ra'}
            </p>
          </div>
        )}
      </div>

      <Form {...form}>
        <form
          onSubmit={form.handleSubmit(onSubmit)}
          className="space-y-6 w-full max-w-md"
        >
          <FormField
            control={form.control}
            name="username"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-[#1f2329]">
                  Tên đăng nhập <span className="text-[#e03e59]">*</span>
                </FormLabel>
                <FormControl>
                  <Input placeholder="Nhập tên đăng nhập của bạn" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="password"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-[#1f2329]">
                  Mật khẩu <span className="text-[#e03e59]">*</span>
                </FormLabel>
                <FormControl>
                  <Input
                    type="password"
                    placeholder="Nhập mật khẩu của bạn"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
                <div className="flex justify-end">
                  <Link
                    to="/auth/forgot-password"
                    className="text-xs font-medium text-[#008fd3] hover:underline"
                  >
                    Quên mật khẩu?
                  </Link>
                </div>
              </FormItem>
            )}
          />

          <Button
            type="submit"
            className="w-full bg-[#008fd3] text-white"
            disabled={isLoggingIn}
          >
            {isLoggingIn ? (
              <div className="flex items-center gap-2">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                Đang đăng nhập...
              </div>
            ) : (
              'Đăng nhập'
            )}
          </Button>
        </form>
      </Form>
    </AuthLayout>
  )
}
