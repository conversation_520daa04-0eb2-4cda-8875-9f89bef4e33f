# C-CAM Cloud Platform

A monorepo for the C-CAM cloud platform, built with Turborepo.

## Project Structure

### Apps

- `api`: Hono-based API server for the C-CAM backend
- `web`: React-based frontend application using TanStack Router and Radix UI

### Packages

- `@c-cam/core`: Core functionality shared across applications
- `@c-cam/eslint`: ESLint configurations
- `@c-cam/logger`: Shared logging functionality
- `@c-cam/shared`: Shared utilities and components
- `@c-cam/tsconfig`: TypeScript configurations
- `@c-cam/types`: TypeScript type definitions

## Technology Stack

- **Language**: TypeScript
- **Backend**: Hono.js with Node
- **Frontend**: React 19 with TanStack Router and Radix UI
- **Database**: MongoDB
- **Cache**: Redis
- **Package Management**: PNPM Workspaces
- **Build Tool**: Turborepo
- **Code Quality**: ESLint, Prettier

## Getting Started

### Prerequisites

- Node.js (v18+)
- PNPM (v9.0.0+)
- Docker and <PERSON>er Compose

### Setup

1. Clone the repository
2. Install dependencies

```bash
pnpm install
```

3. Start the development infrastructure

```bash
docker-compose up -d
```

4. Start the development servers

```bash
pnpm dev
```

This will start both the API (http://localhost:8080) and web app (http://localhost:3000).

### Build

To build all apps and packages:

```bash
pnpm build
```

To build specific apps:

```bash
pnpm build:api  # Build API only
pnpm build:web  # Build web app only
```

## Development Workflow

### Code Quality

The repository includes standardized linting and formatting rules:

```bash
# Run linting across all projects
pnpm lint

# Format code with Prettier
pnpm format

# Type checking
pnpm check-types
```

### Docker Environment

The project includes Docker configuration for development:

- MongoDB: Database server (port 27017)
- Mongo Express: MongoDB web admin (port 8081)
- Redis: Caching server (port 6379)

## Project Configuration

### ESLint Configuration

The project uses a structured ESLint setup:

- `packages/eslint/base.js`: Core TypeScript rules
- `packages/eslint/backend.js`: Backend-specific rules
- `packages/eslint/react-internal.js`: React-specific rules

### Prettier Configuration

Prettier is configured at the root level with app-specific overrides available in each app directory.

## API Documentation

The API server includes a health endpoint at `/health` and RESTful endpoints for resources like users, tenants, and cameras.

## License

Private - All rights reserved
