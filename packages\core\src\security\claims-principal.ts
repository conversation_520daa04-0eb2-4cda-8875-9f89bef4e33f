/**
 * Represents a claim in a claims-based identity system
 */
export interface Claim {
  type: string;
  value: string;
}

/**
 * Represents a claims-based identity
 */
export class ClaimsIdentity {
  private _claims: Claim[] = [];
  private _authenticationType: string;
  private _name?: string;
  private _roleClaimType = 'role';
  private _nameClaimType = 'name';

  constructor(
    claims: Claim[] = [],
    authenticationType = '',
    nameClaimType = 'name',
    roleClaimType = 'role'
  ) {
    this._claims = claims;
    this._authenticationType = authenticationType;
    this._nameClaimType = nameClaimType;
    this._roleClaimType = roleClaimType;

    // Set name from claims if available
    const nameClaim = this.findFirst(this._nameClaimType);
    if (nameClaim) {
      this._name = nameClaim.value;
    }
  }

  /**
   * Gets the authentication type for this identity
   */
  get authenticationType(): string {
    return this._authenticationType;
  }

  /**
   * Gets the name of the identity
   */
  get name(): string | undefined {
    return this._name;
  }

  /**
   * Gets all claims from this identity
   */
  get claims(): Claim[] {
    return [...this._claims];
  }

  /**
   * Gets the claim type that will be used for the role claim
   */
  get roleClaimType(): string {
    return this._roleClaimType;
  }

  /**
   * Gets the claim type that will be used for the name claim
   */
  get nameClaimType(): string {
    return this._nameClaimType;
  }

  /**
   * Adds a claim to this identity
   * @param claim The claim to add
   */
  addClaim(claim: Claim): void {
    this._claims.push(claim);

    // Update name if this is a name claim
    if (claim.type === this._nameClaimType) {
      this._name = claim.value;
    }
  }

  /**
   * Adds multiple claims to this identity
   * @param claims The claims to add
   */
  AddClaims(claims: Claim[]): void {
    for (const claim of claims) {
      this.addClaim(claim);
    }
  }

  /**
   * Finds all claims with the specified type
   * @param type The claim type to find
   * @returns An array of claims with the specified type
   */
  findAll(type: string): Claim[] {
    return this._claims.filter((c) => c.type === type);
  }

  /**
   * Finds the first claim with the specified type
   * @param type The claim type to find
   * @returns The first claim with the specified type, or undefined if not found
   */
  findFirst(type: string): Claim | undefined {
    return this._claims.find((c) => c.type === type);
  }

  /**
   * Checks if this identity has a claim with the specified type and value
   * @param type The claim type
   * @param value The claim value (optional)
   * @returns True if the identity has the claim, false otherwise
   */
  HasClaim(type: string, value?: string): boolean {
    if (value === undefined) {
      return this._claims.some((c) => c.type === type);
    }
    return this._claims.some((c) => c.type === type && c.value === value);
  }

  /**
   * Gets all role claims for this identity
   * @returns An array of role values
   */
  get roles(): string[] {
    return this.findAll(this._roleClaimType).map((c) => c.value);
  }

  /**
   * Checks if this identity has the specified role
   * @param role The role to check
   * @returns True if the identity has the role, false otherwise
   */
  isInRole(role: string): boolean {
    return this.HasClaim(this._roleClaimType, role);
  }
}

/**
 * Represents a claims-based principal
 */
export class ClaimsPrincipal {
  private _identities: ClaimsIdentity[] = [];

  constructor(identity?: ClaimsIdentity) {
    if (identity) {
      this._identities.push(identity);
    }
  }

  /**
   * Gets all identities for this principal
   */
  get identities(): ClaimsIdentity[] {
    return [...this._identities];
  }

  /**
   * Gets the primary identity for this principal
   */
  get identity(): ClaimsIdentity | undefined {
    return this._identities.length > 0 ? this._identities[0] : undefined;
  }

  /**
   * Adds an identity to this principal
   * @param identity The identity to add
   */
  addIdentity(identity: ClaimsIdentity): void {
    this._identities.push(identity);
  }

  /**
   * Finds all claims with the specified type across all identities
   * @param type The claim type to find
   * @returns An array of claims with the specified type
   */
  findAll(type: string): Claim[] {
    const result: Claim[] = [];
    for (const identity of this._identities) {
      result.push(...identity.findAll(type));
    }
    return result;
  }

  /**
   * Finds the first claim with the specified type across all identities
   * @param type The claim type to find
   * @returns The first claim with the specified type, or undefined if not found
   */
  findFirst(type: string): Claim | undefined {
    for (const identity of this._identities) {
      const claim = identity.findFirst(type);
      if (claim) {
        return claim;
      }
    }
    return undefined;
  }

  /**
   * Checks if any identity has a claim with the specified type and value
   * @param type The claim type
   * @param value The claim value (optional)
   * @returns True if any identity has the claim, false otherwise
   */
  hasClaim(type: string, value?: string): boolean {
    return this._identities.some((identity) => identity.HasClaim(type, value));
  }

  /**
   * Checks if any identity has the specified role
   * @param role The role to check
   * @returns True if any identity has the role, false otherwise
   */
  isInRole(role: string): boolean {
    return this._identities.some((identity) => identity.isInRole(role));
  }

  /**
   * Gets all role claims for this principal
   * @returns An array of role values
   */
  get roles(): string[] {
    const result: string[] = [];
    for (const identity of this._identities) {
      result.push(...identity.roles);
    }
    return [...new Set(result)]; // Remove duplicates
  }
}

/**
 * Creates a ClaimsPrincipal from a JWT token payload
 * @param payload The JWT token payload
 * @returns A ClaimsPrincipal with claims from the payload
 */
export function createClaimsPrincipalFromJwt(payload: any): ClaimsPrincipal {
  const claims: Claim[] = [];

  // Convert payload properties to claims
  for (const [key, value] of Object.entries(payload)) {
    if (value !== undefined && value !== null) {
      // Handle arrays (like roles)
      if (Array.isArray(value)) {
        for (const item of value) {
          if (typeof item === 'string') {
            claims.push({ type: key, value: item });
          } else if (typeof item === 'object' && item.name) {
            claims.push({ type: key, value: item.name });
          }
        }
      }
      // Handle special case for sub claim (subject/user id)
      else if (key === 'sub') {
        claims.push({ type: 'sub', value: String(value) });
        claims.push({ type: 'id', value: String(value) });
      }
      // Handle all other claims
      else {
        claims.push({ type: key, value: String(value) });
      }
    }
  }

  // Create identity with claims
  const identity = new ClaimsIdentity(
    claims,
    'JWT',
    payload.name_claim_type || 'name',
    payload.role_claim_type || 'role'
  );

  return new ClaimsPrincipal(identity);
}
