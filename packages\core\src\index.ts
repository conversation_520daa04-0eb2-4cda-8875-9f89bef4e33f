/**
 * Main core package exports
 * This file serves as the central export point for all modules
 */

export * from './shared/index.js';

export * from './hosting/index.js';

export * from './di/container.js';
export * from './di/injectable.js';

export * from './http/controller.js';
export * from './http/http-error.js';
export * from './http/router-factory.js';

export * from './security/authorization.js';
export * from './security/claims-principal.js';
export * from './security/dynamic-policy-loader.js';
export * from './security/policy.js';

export * from './decorators/authorized.decorator.js';
export * from './decorators/controller.decorator.js';
export * from './decorators/error-handling.decorator.js';
export * from './decorators/http-method.decorator.js';
export * from './decorators/middleware.decorator.js';
export * from './decorators/parameter.decorator.js';
export * from './decorators/injectable.decorator.js';

export * from './middlewares/cors.middleware.js';
export * from './middlewares/request-logger.middleware.js';
export {
  createErrorHandler,
  createDevelopmentErrorHandler,
  createProductionErrorHandler,
} from './middlewares/error-handler.middleware.js';

export * from './utilities/index.js';

export * from './di/type.js';

export * from './http/types.js';

export * from './http/middleware.js';

export * from './integrations/index.js';
