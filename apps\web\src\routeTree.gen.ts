/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

// Import Routes

import { Route as rootRoute } from './routes/__root'
import { Route as IndexImport } from './routes/index'
import { Route as UnitsIndexImport } from './routes/units/index'
import { Route as RolesIndexImport } from './routes/roles/index'
import { Route as OrganizationsIndexImport } from './routes/organizations/index'
import { Route as MembersIndexImport } from './routes/members/index'
import { Route as DevicesIndexImport } from './routes/devices/index'
import { Route as DashboardIndexImport } from './routes/dashboard/index'
import { Route as CamerasIndexImport } from './routes/cameras/index'
import { Route as RolesPermissionImport } from './routes/roles/permission'
import { Route as OrganizationsDetailImport } from './routes/organizations/detail'
import { Route as DevicesOnboardImport } from './routes/devices/onboard'
import { Route as DevicesDetailImport } from './routes/devices/detail'
import { Route as AuthLoginImport } from './routes/auth/login'
import { Route as AuthForgotPasswordImport } from './routes/auth/forgot-password'

// Create/Update Routes

const IndexRoute = IndexImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => rootRoute,
} as any)

const UnitsIndexRoute = UnitsIndexImport.update({
  id: '/units/',
  path: '/units/',
  getParentRoute: () => rootRoute,
} as any)

const RolesIndexRoute = RolesIndexImport.update({
  id: '/roles/',
  path: '/roles/',
  getParentRoute: () => rootRoute,
} as any)

const OrganizationsIndexRoute = OrganizationsIndexImport.update({
  id: '/organizations/',
  path: '/organizations/',
  getParentRoute: () => rootRoute,
} as any)

const MembersIndexRoute = MembersIndexImport.update({
  id: '/members/',
  path: '/members/',
  getParentRoute: () => rootRoute,
} as any)

const DevicesIndexRoute = DevicesIndexImport.update({
  id: '/devices/',
  path: '/devices/',
  getParentRoute: () => rootRoute,
} as any)

const DashboardIndexRoute = DashboardIndexImport.update({
  id: '/dashboard/',
  path: '/dashboard/',
  getParentRoute: () => rootRoute,
} as any)

const CamerasIndexRoute = CamerasIndexImport.update({
  id: '/cameras/',
  path: '/cameras/',
  getParentRoute: () => rootRoute,
} as any)

const RolesPermissionRoute = RolesPermissionImport.update({
  id: '/roles/permission',
  path: '/roles/permission',
  getParentRoute: () => rootRoute,
} as any)

const OrganizationsDetailRoute = OrganizationsDetailImport.update({
  id: '/organizations/detail',
  path: '/organizations/detail',
  getParentRoute: () => rootRoute,
} as any)

const DevicesOnboardRoute = DevicesOnboardImport.update({
  id: '/devices/onboard',
  path: '/devices/onboard',
  getParentRoute: () => rootRoute,
} as any)

const DevicesDetailRoute = DevicesDetailImport.update({
  id: '/devices/detail',
  path: '/devices/detail',
  getParentRoute: () => rootRoute,
} as any)

const AuthLoginRoute = AuthLoginImport.update({
  id: '/auth/login',
  path: '/auth/login',
  getParentRoute: () => rootRoute,
} as any)

const AuthForgotPasswordRoute = AuthForgotPasswordImport.update({
  id: '/auth/forgot-password',
  path: '/auth/forgot-password',
  getParentRoute: () => rootRoute,
} as any)

// Populate the FileRoutesByPath interface

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/': {
      id: '/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof IndexImport
      parentRoute: typeof rootRoute
    }
    '/auth/forgot-password': {
      id: '/auth/forgot-password'
      path: '/auth/forgot-password'
      fullPath: '/auth/forgot-password'
      preLoaderRoute: typeof AuthForgotPasswordImport
      parentRoute: typeof rootRoute
    }
    '/auth/login': {
      id: '/auth/login'
      path: '/auth/login'
      fullPath: '/auth/login'
      preLoaderRoute: typeof AuthLoginImport
      parentRoute: typeof rootRoute
    }
    '/devices/detail': {
      id: '/devices/detail'
      path: '/devices/detail'
      fullPath: '/devices/detail'
      preLoaderRoute: typeof DevicesDetailImport
      parentRoute: typeof rootRoute
    }
    '/devices/onboard': {
      id: '/devices/onboard'
      path: '/devices/onboard'
      fullPath: '/devices/onboard'
      preLoaderRoute: typeof DevicesOnboardImport
      parentRoute: typeof rootRoute
    }
    '/organizations/detail': {
      id: '/organizations/detail'
      path: '/organizations/detail'
      fullPath: '/organizations/detail'
      preLoaderRoute: typeof OrganizationsDetailImport
      parentRoute: typeof rootRoute
    }
    '/roles/permission': {
      id: '/roles/permission'
      path: '/roles/permission'
      fullPath: '/roles/permission'
      preLoaderRoute: typeof RolesPermissionImport
      parentRoute: typeof rootRoute
    }
    '/cameras/': {
      id: '/cameras/'
      path: '/cameras'
      fullPath: '/cameras'
      preLoaderRoute: typeof CamerasIndexImport
      parentRoute: typeof rootRoute
    }
    '/dashboard/': {
      id: '/dashboard/'
      path: '/dashboard'
      fullPath: '/dashboard'
      preLoaderRoute: typeof DashboardIndexImport
      parentRoute: typeof rootRoute
    }
    '/devices/': {
      id: '/devices/'
      path: '/devices'
      fullPath: '/devices'
      preLoaderRoute: typeof DevicesIndexImport
      parentRoute: typeof rootRoute
    }
    '/members/': {
      id: '/members/'
      path: '/members'
      fullPath: '/members'
      preLoaderRoute: typeof MembersIndexImport
      parentRoute: typeof rootRoute
    }
    '/organizations/': {
      id: '/organizations/'
      path: '/organizations'
      fullPath: '/organizations'
      preLoaderRoute: typeof OrganizationsIndexImport
      parentRoute: typeof rootRoute
    }
    '/roles/': {
      id: '/roles/'
      path: '/roles'
      fullPath: '/roles'
      preLoaderRoute: typeof RolesIndexImport
      parentRoute: typeof rootRoute
    }
    '/units/': {
      id: '/units/'
      path: '/units'
      fullPath: '/units'
      preLoaderRoute: typeof UnitsIndexImport
      parentRoute: typeof rootRoute
    }
  }
}

// Create and export the route tree

export interface FileRoutesByFullPath {
  '/': typeof IndexRoute
  '/auth/forgot-password': typeof AuthForgotPasswordRoute
  '/auth/login': typeof AuthLoginRoute
  '/devices/detail': typeof DevicesDetailRoute
  '/devices/onboard': typeof DevicesOnboardRoute
  '/organizations/detail': typeof OrganizationsDetailRoute
  '/roles/permission': typeof RolesPermissionRoute
  '/cameras': typeof CamerasIndexRoute
  '/dashboard': typeof DashboardIndexRoute
  '/devices': typeof DevicesIndexRoute
  '/members': typeof MembersIndexRoute
  '/organizations': typeof OrganizationsIndexRoute
  '/roles': typeof RolesIndexRoute
  '/units': typeof UnitsIndexRoute
}

export interface FileRoutesByTo {
  '/': typeof IndexRoute
  '/auth/forgot-password': typeof AuthForgotPasswordRoute
  '/auth/login': typeof AuthLoginRoute
  '/devices/detail': typeof DevicesDetailRoute
  '/devices/onboard': typeof DevicesOnboardRoute
  '/organizations/detail': typeof OrganizationsDetailRoute
  '/roles/permission': typeof RolesPermissionRoute
  '/cameras': typeof CamerasIndexRoute
  '/dashboard': typeof DashboardIndexRoute
  '/devices': typeof DevicesIndexRoute
  '/members': typeof MembersIndexRoute
  '/organizations': typeof OrganizationsIndexRoute
  '/roles': typeof RolesIndexRoute
  '/units': typeof UnitsIndexRoute
}

export interface FileRoutesById {
  __root__: typeof rootRoute
  '/': typeof IndexRoute
  '/auth/forgot-password': typeof AuthForgotPasswordRoute
  '/auth/login': typeof AuthLoginRoute
  '/devices/detail': typeof DevicesDetailRoute
  '/devices/onboard': typeof DevicesOnboardRoute
  '/organizations/detail': typeof OrganizationsDetailRoute
  '/roles/permission': typeof RolesPermissionRoute
  '/cameras/': typeof CamerasIndexRoute
  '/dashboard/': typeof DashboardIndexRoute
  '/devices/': typeof DevicesIndexRoute
  '/members/': typeof MembersIndexRoute
  '/organizations/': typeof OrganizationsIndexRoute
  '/roles/': typeof RolesIndexRoute
  '/units/': typeof UnitsIndexRoute
}

export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths:
    | '/'
    | '/auth/forgot-password'
    | '/auth/login'
    | '/devices/detail'
    | '/devices/onboard'
    | '/organizations/detail'
    | '/roles/permission'
    | '/cameras'
    | '/dashboard'
    | '/devices'
    | '/members'
    | '/organizations'
    | '/roles'
    | '/units'
  fileRoutesByTo: FileRoutesByTo
  to:
    | '/'
    | '/auth/forgot-password'
    | '/auth/login'
    | '/devices/detail'
    | '/devices/onboard'
    | '/organizations/detail'
    | '/roles/permission'
    | '/cameras'
    | '/dashboard'
    | '/devices'
    | '/members'
    | '/organizations'
    | '/roles'
    | '/units'
  id:
    | '__root__'
    | '/'
    | '/auth/forgot-password'
    | '/auth/login'
    | '/devices/detail'
    | '/devices/onboard'
    | '/organizations/detail'
    | '/roles/permission'
    | '/cameras/'
    | '/dashboard/'
    | '/devices/'
    | '/members/'
    | '/organizations/'
    | '/roles/'
    | '/units/'
  fileRoutesById: FileRoutesById
}

export interface RootRouteChildren {
  IndexRoute: typeof IndexRoute
  AuthForgotPasswordRoute: typeof AuthForgotPasswordRoute
  AuthLoginRoute: typeof AuthLoginRoute
  DevicesDetailRoute: typeof DevicesDetailRoute
  DevicesOnboardRoute: typeof DevicesOnboardRoute
  OrganizationsDetailRoute: typeof OrganizationsDetailRoute
  RolesPermissionRoute: typeof RolesPermissionRoute
  CamerasIndexRoute: typeof CamerasIndexRoute
  DashboardIndexRoute: typeof DashboardIndexRoute
  DevicesIndexRoute: typeof DevicesIndexRoute
  MembersIndexRoute: typeof MembersIndexRoute
  OrganizationsIndexRoute: typeof OrganizationsIndexRoute
  RolesIndexRoute: typeof RolesIndexRoute
  UnitsIndexRoute: typeof UnitsIndexRoute
}

const rootRouteChildren: RootRouteChildren = {
  IndexRoute: IndexRoute,
  AuthForgotPasswordRoute: AuthForgotPasswordRoute,
  AuthLoginRoute: AuthLoginRoute,
  DevicesDetailRoute: DevicesDetailRoute,
  DevicesOnboardRoute: DevicesOnboardRoute,
  OrganizationsDetailRoute: OrganizationsDetailRoute,
  RolesPermissionRoute: RolesPermissionRoute,
  CamerasIndexRoute: CamerasIndexRoute,
  DashboardIndexRoute: DashboardIndexRoute,
  DevicesIndexRoute: DevicesIndexRoute,
  MembersIndexRoute: MembersIndexRoute,
  OrganizationsIndexRoute: OrganizationsIndexRoute,
  RolesIndexRoute: RolesIndexRoute,
  UnitsIndexRoute: UnitsIndexRoute,
}

export const routeTree = rootRoute
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()

/* ROUTE_MANIFEST_START
{
  "routes": {
    "__root__": {
      "filePath": "__root.tsx",
      "children": [
        "/",
        "/auth/forgot-password",
        "/auth/login",
        "/devices/detail",
        "/devices/onboard",
        "/organizations/detail",
        "/roles/permission",
        "/cameras/",
        "/dashboard/",
        "/devices/",
        "/members/",
        "/organizations/",
        "/roles/",
        "/units/"
      ]
    },
    "/": {
      "filePath": "index.tsx"
    },
    "/auth/forgot-password": {
      "filePath": "auth/forgot-password.tsx"
    },
    "/auth/login": {
      "filePath": "auth/login.tsx"
    },
    "/devices/detail": {
      "filePath": "devices/detail.tsx"
    },
    "/devices/onboard": {
      "filePath": "devices/onboard.tsx"
    },
    "/organizations/detail": {
      "filePath": "organizations/detail.tsx"
    },
    "/roles/permission": {
      "filePath": "roles/permission.tsx"
    },
    "/cameras/": {
      "filePath": "cameras/index.tsx"
    },
    "/dashboard/": {
      "filePath": "dashboard/index.tsx"
    },
    "/devices/": {
      "filePath": "devices/index.tsx"
    },
    "/members/": {
      "filePath": "members/index.tsx"
    },
    "/organizations/": {
      "filePath": "organizations/index.tsx"
    },
    "/roles/": {
      "filePath": "roles/index.tsx"
    },
    "/units/": {
      "filePath": "units/index.tsx"
    }
  }
}
ROUTE_MANIFEST_END */
