import { logger } from '@c-cam/logger';
import { Context, Next } from 'hono';
import { MiddlewareHandler } from 'hono/types';
import { IMiddleware } from '../decorators/middleware.decorator.js';

/**
 * Options for the request logger middleware
 */
export interface RequestLoggerOptions {
  /**
   * Whether to log the request body
   * Default: false
   */
  logRequestBody?: boolean;

  /**
   * Whether to log the response body
   * Default: false
   */
  logResponseBody?: boolean;

  /**
   * Whether to use color coding for status codes
   * Default: true
   */
  useColors?: boolean;

  /**
   * Custom logger instance
   * Default: global logger
   */
  logger?: typeof logger;
}

/**
 * Default options for the request logger middleware
 */
const defaultOptions: RequestLoggerOptions = {
  logRequestBody: false,
  logResponseBody: false,
  useColors: true,
};

/**
 * Request logger middleware implementation
 * Logs request and response details with timing information
 */
export class RequestLoggerMiddleware implements IMiddleware {
  private readonly options: RequestLoggerOptions;
  private readonly loggerInstance: typeof logger;

  constructor(options: RequestLoggerOptions = {}) {
    this.options = { ...defaultOptions, ...options };
    this.loggerInstance = this.options.logger || logger;
  }

  /**
   * Handle request logging
   */
  public async use(c: Context, next: Next): Promise<void> {
    const startTime = Date.now();
    const req = c.req;

    // Log request start
    this.loggerInstance.info(`[${req.method}] Request started: ${req.url}`);

    // Log request body if enabled
    if (this.options.logRequestBody) {
      try {
        // Hono doesn't provide a direct clone method, so we'll handle this differently
        // Try to get the body content without consuming the original request
        const contentType = req.header('content-type') || '';
        if (contentType.includes('application/json')) {
          const body = await c.req.json().catch(() => null);
          if (body) {
            this.loggerInstance.debug(`Request body: ${JSON.stringify(body)}`);
          }
        } else if (contentType.includes('text/')) {
          const body = await c.req.text().catch(() => null);
          if (body) {
            this.loggerInstance.debug(`Request body: ${body}`);
          }
        }
      } catch (error) {
        this.loggerInstance.debug('Could not log request body');
      }
    }

    // Process the request through the middleware chain
    await next();

    // Calculate elapsed time
    const elapsedTime = Date.now() - startTime;

    // Get status code and determine color
    const res = c.res;
    const statusCode = res.status;
    let statusColor = '';
    let resetColor = '';

    if (this.options.useColors) {
      statusColor =
        statusCode >= 500
          ? '\x1b[31m' // Red for 5xx errors
          : statusCode >= 400
            ? '\x1b[33m' // Yellow for 4xx errors
            : statusCode >= 300
              ? '\x1b[36m' // Cyan for 3xx redirects
              : '\x1b[32m'; // Green for 2xx success
      resetColor = '\x1b[0m';
    }

    // Log response completion
    this.loggerInstance.info(
      `[${req.method}] ${statusColor}${statusCode}${resetColor} ${req.url} - ${elapsedTime}ms`,
    );

    // Log response body if enabled
    if (this.options.logResponseBody) {
      try {
        // We can't easily get the response body in Hono without affecting the response
        // Hono doesn't provide a simple way to clone the response
        this.loggerInstance.debug('Response body logging not available in Hono');
      } catch (error) {
        this.loggerInstance.debug('Could not log response body');
      }
    }
  }
}

/**
 * Create request logger middleware with the specified options
 * @param options Request logger options
 * @returns Hono middleware function
 */
export function createRequestLoggerMiddleware(
  options?: RequestLoggerOptions,
): MiddlewareHandler {
  const middleware = new RequestLoggerMiddleware(options);
  return (c, next) => middleware.use(c, next);
}
