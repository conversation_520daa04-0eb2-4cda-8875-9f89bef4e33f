import { BaseModel, Inject, Injectable } from '@c-cam/core';
import { TenantDocument } from '@/database/entities/TenantModel';
import TenantRepository from '@/repositories/TenantRepository';

/**
 * Service for managing tenants
 * Extends the BaseModel with TenantDocument type
 */
@Injectable()
class TenantService extends BaseModel<TenantDocument> {
  /**
   * Create a new TenantService
   * @param tenantId Optional tenant ID for multi-tenant operations
   */
  constructor(
    @Inject(TenantRepository)
    repository: TenantRepository,
  ) {
    super(repository);
  }

  /**
   * Create a new organization
   * @param name The organization name
   * @param createdBy The ID of the user creating the organization
   * @returns The newly created organization
   */
  async createOrganization(
    name: string,
    createdBy: string,
  ): Promise<TenantDocument> {
    // Check if an organization with the same name already exists
    const existingOrg = await (this.repository as TenantRepository).findByName(
      name,
    );

    if (existingOrg) {
      throw new Error(`Organization with name '${name}' already exists`);
    }

    // Create the new organization
    return this.create({
      name,
      created_by: createdBy,
    });
  }

  /**
   * Update an organization
   * @param id The organization ID
   * @param name The new organization name
   * @returns True if the organization was updated, false otherwise
   */
  async updateOrganization(id: string, name: string): Promise<boolean> {
    // Check if the organization exists
    const org = await this.findById(id);
    if (!org) {
      throw new Error(`Organization with ID '${id}' not found`);
    }

    // If name is being updated, check for duplicates
    if (name !== org.name) {
      const existingOrg = await (
        this.repository as TenantRepository
      ).findByName(name);

      if (existingOrg && existingOrg.id !== id) {
        throw new Error(`Organization with name '${name}' already exists`);
      }
    }

    // Update the organization
    return this.update(id, { name });
  }

  /**
   * Find an organization by name
   * @param name The organization name
   * @returns The organization or null if not found
   */
  async findByName(name: string): Promise<TenantDocument | null> {
    return (this.repository as TenantRepository).findByName(name);
  }

  /**
   * Find organizations by creator
   * @param createdBy The creator ID
   * @returns An array of organizations
   */
  async findByCreatedBy(createdBy: string): Promise<TenantDocument[]> {
    return (this.repository as TenantRepository).findByCreatedBy(createdBy);
  }
}

export default TenantService;
