# Git Rules for Software Development

This document defines the Git workflow and conventions used in this project to ensure clean, structured, and collaborative development.

---

## 1. Branching Strategy

### Main Branches
- `main`: Contains stable production-ready code.
- `develop`: Integrates features before they are released to `main`.

### Supporting Branches
- `feature/<name>`: Used for new features.
- `bugfix/<name>`: Fixes non-critical bugs during development.
- `hotfix/<name>`: Fixes critical bugs in production.
- `release/<version>`: Prepares code for release.

#### Branch Naming Convention
Use lowercase, hyphen-separated English names.
Examples:
```
feature/user-login  
bugfix/password-validation  
hotfix/api-crash-fix  
release/v1.2.0
```

---

## 2. Commit Message Guidelines

### Commit Message Format
```
<type>(<scope>): <short description>

[Optional body description]

[Optional footer: issue reference, breaking change note, etc.]
```

### Commit Types
- `feat`: Add new features
- `fix`: Fix a bug
- `docs`: Documentation changes
- `style`: Code style changes (formatting, whitespace)
- `refactor`: Refactoring code (no behavior change)
- `test`: Add or update tests
- `chore`: Minor tasks like config or dependencies

### Commit Description
- The **short description** should be written in the imperative mood (e.g., "add", "fix", not "added" or "fixed").
- Keep it concise (max 72 characters).
- The **body (optional)** can explain the **why** behind the change.
- The **footer (optional)** can include issue IDs, links, or notes like `BREAKING CHANGE:`.

### Examples
```
feat(auth): add Google login

Allow users to authenticate using their Google account via OAuth2.
This improves accessibility and convenience for external users.

Closes #123
```

```
fix(api): handle null user data

Prevent crash when user data is null by adding validation.
```

---

## 3. Merge Policy

- Always create a **Pull Request (PR)** before merging.
- **Never commit directly to `main` or `develop`**.
- Code must pass code review and CI/CD checks before merging.
- Prefer `Squash and Merge` to keep history clean.

---

## 4. Releases & Tagging

- Tag releases using semantic versioning (SemVer): `v<major>.<minor>.<patch>`
- Example:
```
git tag -a v1.0.0 -m "Release v1.0.0"
```

### SemVer:
- **Major**: Breaking changes
- **Minor**: New features, backwards compatible
- **Patch**: Bug fixes and small improvements

---

## 5. Pull Requests & Code Reviews

- Every PR must:
  - Have a clear and descriptive title
  - Link to related tasks/tickets
  - Assign reviewers
- Reviewers should:
  - Check for code style and logic issues
  - Give constructive feedback
  - Approve or request changes

---

## 6. Branch Cleanup

After merging, delete feature/bugfix branches if no longer needed:
```
git branch -d feature/some-feature  
git push origin --delete feature/some-feature
```

---

## 7. Branch Protection

- Enable protection rules for `main` and `develop`:
  - No direct pushes
  - Require PR reviews
  - Require passing CI checks

---

## 8. Recommended Tools

- Use `husky` + `lint-staged` for pre-commit checks.
- Use `pre-push` hooks to run tests before pushing.
- Use PR templates to maintain consistency.

---

## Summary

| Branch       | Purpose                       |
|--------------|-------------------------------|
| `main`       | Stable production code         |
| `develop`    | Integration of all features    |
| `feature/*`  | New features                   |
| `bugfix/*`   | Minor development bug fixes    |
| `hotfix/*`   | Critical production bug fixes  |
| `release/*`  | Code preparation for release   |

Let's keep our Git history clean and our workflow consistent 🚀