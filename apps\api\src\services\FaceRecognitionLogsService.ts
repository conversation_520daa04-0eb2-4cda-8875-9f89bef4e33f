import { BaseModel, Inject, Injectable } from '@c-cam/core';
import { FaceRecognitionLogsDocument } from '@/database/entities/FaceRecognitionLogsModel';
import FaceRecognitionLogsRepository from '@/repositories/FaceRecognitionLogsRepository';

/**
 * Service for managing face recognition logs
 * Extends the BaseModel with FaceRecognitionLogsDocument type
 */
@Injectable()
class FaceRecognitionLogsService extends BaseModel<FaceRecognitionLogsDocument> {
  constructor(
    @Inject(FaceRecognitionLogsRepository)
    repository: FaceRecognitionLogsRepository,
  ) {
    super(repository);
  }

  /**
   * Log a face recognition event
   * @param logData The log data
   * @returns The newly created log
   */
  async logRecognitionEvent(logData: {
    user_id?: string;
    camera_id: string;
    edge_device_id: string;
    device_id: string;
    status: string;
    similarity_percent?: number;
    image_url?: string;
    created_by: string;
  }): Promise<FaceRecognitionLogsDocument> {
    // Validate status
    const validStatuses = ['success', 'failure', 'pending'];
    if (!validStatuses.includes(logData.status.toLowerCase())) {
      throw new Error(
        `Invalid status. Must be one of: ${validStatuses.join(', ')}`,
      );
    }

    // Validate similarity percent if provided
    if (
      logData.similarity_percent !== undefined &&
      (logData.similarity_percent < 0 || logData.similarity_percent > 100)
    ) {
      throw new Error('Similarity percent must be between 0 and 100');
    }

    // Validate image URL if provided
    if (
      logData.image_url &&
      !logData.image_url.startsWith('http') &&
      !logData.image_url.startsWith('data:image')
    ) {
      throw new Error(
        'Invalid image URL format. Must be a valid URL or base64 encoded image data',
      );
    }

    // Create the new log
    return this.create({
      user_id: logData.user_id,
      camera_id: logData.camera_id,
      edge_device_id: logData.edge_device_id,
      device_id: logData.device_id,
      recognized_at: new Date(), // Set current date as recognition time
      status: logData.status.toLowerCase(),
      similarity_percent: logData.similarity_percent,
      image_url: logData.image_url,
      created_by: logData.created_by,
    });
  }

  /**
   * Find face recognition logs by user ID
   * @param userId The user ID
   * @returns An array of face recognition logs
   */
  async findByUserId(userId: string): Promise<FaceRecognitionLogsDocument[]> {
    return (this.repository as FaceRecognitionLogsRepository).findByUserId(
      userId,
    );
  }

  /**
   * Find face recognition logs by camera ID
   * @param cameraId The camera ID
   * @returns An array of face recognition logs
   */
  async findByCameraId(
    cameraId: string,
  ): Promise<FaceRecognitionLogsDocument[]> {
    return (this.repository as FaceRecognitionLogsRepository).findByCameraId(
      cameraId,
    );
  }

  /**
   * Find face recognition logs by edge device ID
   * @param edgeDeviceId The edge device ID
   * @returns An array of face recognition logs
   */
  async findByEdgeDeviceId(
    edgeDeviceId: string,
  ): Promise<FaceRecognitionLogsDocument[]> {
    return (
      this.repository as FaceRecognitionLogsRepository
    ).findByEdgeDeviceId(edgeDeviceId);
  }

  /**
   * Find face recognition logs by status
   * @param status The status
   * @returns An array of face recognition logs
   */
  async findByStatus(status: string): Promise<FaceRecognitionLogsDocument[]> {
    // Validate status
    const validStatuses = ['success', 'failure', 'pending'];
    if (!validStatuses.includes(status.toLowerCase())) {
      throw new Error(
        `Invalid status. Must be one of: ${validStatuses.join(', ')}`,
      );
    }

    // Use the find method directly if the repository doesn't have a specific method
    return this.find({ status: status.toLowerCase() });
  }

  /**
   * Find face recognition logs by similarity percent range
   * @param minSimilarity The minimum similarity percent
   * @param maxSimilarity The maximum similarity percent
   * @returns An array of face recognition logs
   */
  async findBySimilarityRange(
    minSimilarity: number,
    maxSimilarity: number,
  ): Promise<FaceRecognitionLogsDocument[]> {
    // Validate similarity percent range
    if (minSimilarity < 0 || minSimilarity > 100) {
      throw new Error('Minimum similarity percent must be between 0 and 100');
    }

    if (maxSimilarity < 0 || maxSimilarity > 100) {
      throw new Error('Maximum similarity percent must be between 0 and 100');
    }

    if (minSimilarity > maxSimilarity) {
      throw new Error(
        'Minimum similarity percent must be less than or equal to maximum similarity percent',
      );
    }

    // Use the find method directly if the repository doesn't have a specific method
    return this.find({
      similarity_percent: { $gte: minSimilarity, $lte: maxSimilarity },
    });
  }

  /**
   * Find face recognition logs by date range
   * @param startDate The start date of the range
   * @param endDate The end date of the range
   * @returns An array of face recognition logs
   */
  async findByDateRange(
    startDate: Date,
    endDate: Date,
  ): Promise<FaceRecognitionLogsDocument[]> {
    return (this.repository as FaceRecognitionLogsRepository).findByDateRange(
      startDate,
      endDate,
    );
  }

  /**
   * Find face recognition logs by user ID and date range
   * @param userId The user ID
   * @param startDate The start date of the range
   * @param endDate The end date of the range
   * @returns An array of face recognition logs
   */
  async findByUserIdAndDateRange(
    userId: string,
    startDate: Date,
    endDate: Date,
  ): Promise<FaceRecognitionLogsDocument[]> {
    return (
      this.repository as FaceRecognitionLogsRepository
    ).findByUserIdAndDateRange(userId, startDate, endDate);
  }

  /**
   * Generate a recognition summary for a user
   * @param userId The user ID
   * @param startDate The start date of the summary
   * @param endDate The end date of the summary
   * @returns A summary of recognition events
   */
  async generateUserRecognitionSummary(
    userId: string,
    startDate: Date,
    endDate: Date,
  ): Promise<{ status: string; count: number }[]> {
    const logs = await (
      this.repository as FaceRecognitionLogsRepository
    ).findByUserIdAndDateRange(userId, startDate, endDate);

    // Group logs by status and count
    const statusMap = new Map<string, number>();

    logs.forEach((log) => {
      const count = statusMap.get(log.status) || 0;
      statusMap.set(log.status, count + 1);
    });

    // Convert map to array of objects
    return Array.from(statusMap.entries()).map(([status, count]) => ({
      status,
      count,
    }));
  }
}

export default FaceRecognitionLogsService;
