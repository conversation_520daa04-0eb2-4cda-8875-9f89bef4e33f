import { BaseModel, Inject, Injectable } from '@c-cam/core';
import { MemberRoleDocument } from '@/database/entities/MemberRoleModel';
import MemberRoleRepository from '@/repositories/MemberRoleRepository';

/**
 * Service for managing member roles
 * Extends the BaseModel with MemberRoleDocument type
 */
@Injectable()
class MemberRoleService extends BaseModel<MemberRoleDocument> {
  /**
   * Create a new MemberRoleService
   * @param tenantId Optional tenant ID for multi-tenant operations
   */
  constructor(
    @Inject(MemberRoleRepository)
    repository: MemberRoleRepository,
  ) {
    super(repository);
  }

  /**
   * Assign a role to a user
   * @param userId The user ID
   * @param roleId The role ID
   * @param createdBy The ID of the user creating the assignment
   * @returns The newly created member role
   */
  async assignRoleToUser(
    userId: string,
    roleId: string,
    createdBy: string,
  ): Promise<MemberRoleDocument> {
    // Check if the assignment already exists
    const existingAssignment = await (
      this.repository as MemberRoleRepository
    ).findByUserIdAndRoleId(userId, roleId);

    if (existingAssignment) {
      throw new Error('User already has this role assigned');
    }

    // Create the new assignment
    return this.create({
      user_id: userId,
      role_id: roleId,
      created_by: createdBy,
    });
  }

  /**
   * Remove a role from a user
   * @param userId The user ID
   * @param roleId The role ID
   * @returns True if the role was removed, false otherwise
   */
  async removeRoleFromUser(userId: string, roleId: string): Promise<boolean> {
    // Find the assignment
    const assignment = await (
      this.repository as MemberRoleRepository
    ).findByUserIdAndRoleId(userId, roleId);

    if (!assignment) {
      return false;
    }

    // Delete the assignment
    return this.delete(assignment.id);
  }

  /**
   * Find member role by user ID and role ID
   * @param userId User ID
   * @param roleId Role ID
   * @returns Member role document or null if not found
   */
  async findByUserIdAndRoleId(
    userId: string,
    roleId: string,
  ): Promise<MemberRoleDocument | null> {
    return (this.repository as MemberRoleRepository).findByUserIdAndRoleId(
      userId,
      roleId,
    );
  }

  /**
   * Find all member roles for a user
   * @param userId User ID
   * @returns Array of member role documents
   */
  async findByUserId(userId: string): Promise<MemberRoleDocument[]> {
    return (this.repository as MemberRoleRepository).find({ user_id: userId });
  }

  /**
   * Get all roles for a user
   * @param userId The user ID
   * @returns An array of member roles
   */
  async getRolesForUser(userId: string): Promise<MemberRoleDocument[]> {
    return (this.repository as MemberRoleRepository).findByUserId(userId);
  }

  /**
   * Get all users with a specific role
   * @param roleId The role ID
   * @returns An array of member roles
   */
  async getUsersWithRole(roleId: string): Promise<MemberRoleDocument[]> {
    return (this.repository as MemberRoleRepository).findByRoleId(roleId);
  }

  /**
   * Remove all roles from a user
   * @param userId The user ID
   * @returns True if any roles were removed, false otherwise
   */
  async removeAllRolesFromUser(userId: string): Promise<boolean> {
    return (this.repository as MemberRoleRepository).deleteByUserId(userId);
  }

  /**
   * Remove a role from all users
   * @param roleId The role ID
   * @returns True if any roles were removed, false otherwise
   */
  async removeRoleFromAllUsers(roleId: string): Promise<boolean> {
    return (this.repository as MemberRoleRepository).deleteByRoleId(roleId);
  }
}

export default MemberRoleService;
