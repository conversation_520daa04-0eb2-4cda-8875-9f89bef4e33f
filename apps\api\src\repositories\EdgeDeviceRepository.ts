import EdgeDeviceModel, {
  EdgeDeviceDocument,
} from '@/database/entities/EdgeDeviceModel';
import { Repository, Injectable } from '@c-cam/core';

/**
 * Repository for managing edge devices
 * Extends the BaseRepository with EdgeDeviceDocument type
 */
@Injectable()
class EdgeDeviceRepository extends Repository<EdgeDeviceDocument> {
  constructor() {
    super(EdgeDeviceModel);
  }

  /**
   * Find edge devices by camera ID
   * @param cameraId The camera ID to search for
   * @returns A promise that resolves to an array of edge devices
   */
  async findByCameraId(cameraId: string): Promise<EdgeDeviceDocument[]> {
    return this.find({ camera_id: cameraId });
  }

  /**
   * Find edge devices by name
   * @param name The device name to search for
   * @returns A promise that resolves to an array of edge devices
   */
  async findByName(name: string): Promise<EdgeDeviceDocument[]> {
    return this.find({ name });
  }

  /**
   * Find edge devices by type
   * @param type The device type to search for
   * @returns A promise that resolves to an array of edge devices
   */
  async findByType(type: string): Promise<EdgeDeviceDocument[]> {
    return this.find({ type });
  }

  /**
   * Find an edge device by IP address
   * @param ipAddress The IP address to search for
   * @returns A promise that resolves to an edge device or null if not found
   */
  async findByIpAddress(ipAddress: string): Promise<EdgeDeviceDocument | null> {
    return this.findOne({ ip_address: ipAddress });
  }

  /**
   * Find an edge device by MAC address
   * @param macAddress The MAC address to search for
   * @returns A promise that resolves to an edge device or null if not found
   */
  async findByMacAddress(
    macAddress: string,
  ): Promise<EdgeDeviceDocument | null> {
    return this.findOne({ mac_address: macAddress });
  }

  /**
   * Find edge devices by firmware version
   * @param firmwareVersion The firmware version to search for
   * @returns A promise that resolves to an array of edge devices
   */
  async findByFirmwareVersion(
    firmwareVersion: string,
  ): Promise<EdgeDeviceDocument[]> {
    return this.find({ firmware_version: firmwareVersion });
  }

  /**
   * Find attendance edge devices
   * @returns A promise that resolves to an array of attendance edge devices
   */
  async findAttendanceDevices(): Promise<EdgeDeviceDocument[]> {
    return this.find({ is_attendance_device: true });
  }

  /**
   * Find edge devices by status
   * @param status The device status to search for
   * @returns A promise that resolves to an array of edge devices
   */
  async findByStatus(status: string): Promise<EdgeDeviceDocument[]> {
    return this.find({ status });
  }
}

export default EdgeDeviceRepository;
