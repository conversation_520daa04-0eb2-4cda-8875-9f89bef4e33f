import { logger } from '@c-cam/logger';
import { KafkaConnection, KafkaMessageHandler } from './kafka.js';

/**
 * Kafka client options
 */
export interface KafkaClientOptions {
  /**
   * Topic prefix for all operations
   */
  topicPrefix?: string;

  /**
   * Whether to read from the beginning of topics when subscribing
   * @default false
   */
  fromBeginning?: boolean;
}

/**
 * Kafka message
 */
export interface KafkaMessage {
  /**
   * Message key
   */
  key: string | null;

  /**
   * Message value
   */
  value: Buffer | null;

  /**
   * Message headers
   */
  headers: Record<string, string>;

  /**
   * Message timestamp
   */
  timestamp: string;

  /**
   * Message offset
   */
  offset: string;
}

/**
 * Kafka client interface
 */
export interface IKafkaClient {
  /**
   * Subscribe to a topic
   * @param topic The topic to subscribe to
   * @param fromBeginning Whether to read from the beginning of the topic
   */
  subscribe(topic: string, fromBeginning?: boolean): Promise<void>;

  /**
   * Register a message handler for a topic
   * @param topic The topic to handle messages for
   * @param handler The message handler function
   */
  onMessage(topic: string, handler: KafkaMessageHandler): void;

  /**
   * Remove a message handler for a topic
   * @param topic The topic to remove the handler for
   * @param handler The message handler function to remove
   */
  offMessage(topic: string, handler: KafkaMessageHandler): void;

  /**
   * Produce a message to a topic
   * @param topic The topic to produce to
   * @param message The message to produce
   * @param key Optional message key
   * @param headers Optional message headers
   */
  produce(
    topic: string,
    message: string | Buffer | object,
    key?: string,
    headers?: Record<string, string>,
  ): Promise<void>;

  /**
   * Create a topic
   * @param topic The topic to create
   * @param numPartitions The number of partitions
   * @param replicationFactor The replication factor
   */
  createTopic(
    topic: string,
    numPartitions?: number,
    replicationFactor?: number,
  ): Promise<void>;

  /**
   * List all topics
   * @returns List of topics
   */
  listTopics(): Promise<string[]>;
}

/**
 * Kafka client implementation
 */
export class KafkaClient implements IKafkaClient {
  private readonly connection: KafkaConnection;
  private readonly options: KafkaClientOptions;

  /**
   * Create a new Kafka client
   * @param options Client options
   */
  constructor(options: KafkaClientOptions = {}) {
    this.options = {
      topicPrefix: '',
      fromBeginning: false,
      ...options,
    };

    // Get the Kafka connection
    this.connection = KafkaConnection.getInstance();
  }

  /**
   * Format a topic with the prefix
   * @param topic The topic
   * @returns The formatted topic
   */
  private formatTopic(topic: string): string {
    return this.options.topicPrefix
      ? `${this.options.topicPrefix}.${topic}`
      : topic;
  }

  /**
   * Subscribe to a topic
   * @param topic The topic to subscribe to
   * @param fromBeginning Whether to read from the beginning of the topic
   */
  public async subscribe(
    topic: string,
    fromBeginning?: boolean,
  ): Promise<void> {
    const formattedTopic = this.formatTopic(topic);
    const readFromBeginning =
      fromBeginning ?? this.options.fromBeginning ?? false;

    logger.debug('Subscribing to Kafka topic', {
      topic: formattedTopic,
      fromBeginning: readFromBeginning,
    });

    await this.connection.subscribe(formattedTopic, readFromBeginning);
  }

  /**
   * Register a message handler for a topic
   * @param topic The topic to handle messages for
   * @param handler The message handler function
   */
  public onMessage(topic: string, handler: KafkaMessageHandler): void {
    const formattedTopic = this.formatTopic(topic);

    logger.debug('Registering Kafka message handler', {
      topic: formattedTopic,
    });

    this.connection.onMessage(formattedTopic, handler);
  }

  /**
   * Remove a message handler for a topic
   * @param topic The topic to remove the handler for
   * @param handler The message handler function to remove
   */
  public offMessage(topic: string, handler: KafkaMessageHandler): void {
    const formattedTopic = this.formatTopic(topic);

    logger.debug('Removing Kafka message handler', {
      topic: formattedTopic,
    });

    this.connection.offMessage(formattedTopic, handler);
  }

  /**
   * Produce a message to a topic
   * @param topic The topic to produce to
   * @param message The message to produce
   * @param key Optional message key
   * @param headers Optional message headers
   */
  public async produce(
    topic: string,
    message: string | Buffer | object,
    key?: string,
    headers?: Record<string, string>,
  ): Promise<void> {
    const formattedTopic = this.formatTopic(topic);

    // Log the produce operation (but don't log the full message content for large messages)
    const messagePreview =
      message instanceof Buffer
        ? `<Buffer of length ${message.length}>`
        : typeof message === 'object'
          ? '<Object>'
          : typeof message === 'string' && message.length > 100
            ? `${message.substring(0, 97)}...`
            : message;

    logger.debug('Producing Kafka message', {
      topic: formattedTopic,
      key,
      messagePreview,
    });

    await this.connection.produce(formattedTopic, message, key, headers);
  }

  /**
   * Create a topic
   * @param topic The topic to create
   * @param numPartitions The number of partitions
   * @param replicationFactor The replication factor
   */
  public async createTopic(
    topic: string,
    numPartitions = 1,
    replicationFactor = 1,
  ): Promise<void> {
    const formattedTopic = this.formatTopic(topic);

    logger.debug('Creating Kafka topic', {
      topic: formattedTopic,
      numPartitions,
      replicationFactor,
    });

    await this.connection.createTopic(
      formattedTopic,
      numPartitions,
      replicationFactor,
    );
  }

  /**
   * List all topics
   * @returns List of topics
   */
  public async listTopics(): Promise<string[]> {
    logger.debug('Listing Kafka topics');

    return this.connection.listTopics();
  }
}
