{"name": "@c-cam/logger", "version": "1.0.0", "type": "module", "main": "dist/index.js", "types": "dist/index.d.ts", "files": ["dist/**"], "exports": {".": "./src/index.ts"}, "scripts": {"build": "tsc --project tsconfig.app.json", "clean": "<PERSON><PERSON><PERSON> dist"}, "dependencies": {"hono": "^4.7.10", "winston": "^3.17.0", "winston-daily-rotate-file": "^5.0.0"}, "devDependencies": {"@c-cam/eslint": "workspace:*", "@c-cam/tsconfig": "workspace:*", "@types/node": "^22.15.21", "eslint": "^9.27.0"}}