import { Context, NextFunction } from '@c-cam/core';
import { HttpError, NotFoundError } from '@c-cam/core';
import { logger } from '@c-cam/logger'

// Re-export HttpError from core for backward compatibility
export { HttpError } from '@c-cam/core';

/**
 * Error handling middleware options
 * Tùy chọn cho middleware xử lý lỗi
 */
export interface ErrorHandlingOptions {
  /**
   * Whether to include stack trace in error responses
   * Có bao gồm stack trace trong response lỗi không
   */
  includeStackTrace?: boolean;

  /**
   * Whether to log errors
   * Có ghi log lỗi không
   */
  logErrors?: boolean;

  /**
   * Environment mode (affects error details exposure)
   * Chế độ môi trường (ảnh hưởng đến việc hiển thị chi tiết lỗi)
   */
  environment?: 'development' | 'production' | 'test';

  /**
   * Custom error message for production
   * Thông báo lỗi tùy chỉnh cho production
   */
  productionErrorMessage?: string;
}

/**
 * Not found middleware for Hono
 * Middleware xử lý 404 cho Hono
 * @param c The Hono context object
 * @param next The next function
 */
export const notFound = async (
  c: Context,
  next: NextFunction,
): Promise<void> => {
  await next();

  // If no response was set, it means no route matched
  if (c.res.status === 404 || !c.finalized) {
    const error = new NotFoundError(`Not Found - ${c.req.path}`);
    throw error;
  }
};

/**
 * Create error handler middleware with options
 * Tạo middleware xử lý lỗi với tùy chọn
 * @param options Error handling options
 * @returns Hono middleware function
 */
export function createErrorHandler(options: ErrorHandlingOptions = {}) {
  const {
    includeStackTrace = process.env.NODE_ENV !== 'production',
    logErrors = true,
    environment = (process.env.NODE_ENV as any) || 'production',
    productionErrorMessage = 'An unexpected error occurred',
  } = options;

  return async (c: Context, next: NextFunction): Promise<Response | void> => {
    try {
      await next();
    } catch (error: any) {
      const httpError =
        error instanceof HttpError
          ? error
          : new HttpError(
              error.message || 'Internal Server Error',
              500,
              'INTERNAL_SERVER_ERROR',
            );

      const statusCode = httpError.statusCode || 500;
      const message =
        environment === 'production' && statusCode >= 500
          ? productionErrorMessage
          : httpError.message || 'Internal Server Error';

      // Log error if enabled
      if (logErrors) {
        logger.error(`Error: ${httpError.message}`, {
          url: c.req.path,
          method: c.req.method,
          statusCode,
          stack: httpError.stack,
          userAgent: c.req.header('user-agent'),
          ip:
            c.req.header('x-forwarded-for') ||
            c.req.header('x-real-ip') ||
            'unknown',
          timestamp: new Date().toISOString(),
        });
      }

      // Return error response
      return c.json(
        {
          success: false,
          message,
          code: httpError.code || 'INTERNAL_SERVER_ERROR',
          ...(includeStackTrace && { stack: httpError.stack }),
        },
        statusCode as any,
      );
    }
  };
}

/**
 * Default error handler middleware for Hono
 * Middleware xử lý lỗi mặc định cho Hono
 * @param c The Hono context object
 * @param next The next function
 */
export const errorHandler = createErrorHandler();

/**
 * Create development error handler with detailed error information
 * Tạo error handler cho development với thông tin lỗi chi tiết
 * @returns Hono middleware function for development
 */
export function createDevelopmentErrorHandler() {
  return createErrorHandler({
    includeStackTrace: true,
    logErrors: true,
    environment: 'development',
  });
}

/**
 * Create production error handler with minimal error exposure
 * Tạo error handler cho production với thông tin lỗi tối thiểu
 * @returns Hono middleware function for production
 */
export function createProductionErrorHandler() {
  return createErrorHandler({
    includeStackTrace: false,
    logErrors: true,
    environment: 'production',
    productionErrorMessage: 'An unexpected error occurred',
  });
}

/**
 * Create test error handler with minimal logging
 * Tạo error handler cho test với logging tối thiểu
 * @returns Hono middleware function for testing
 */
export function createTestErrorHandler() {
  return createErrorHandler({
    includeStackTrace: true,
    logErrors: false,
    environment: 'test',
  });
}
