import axios from 'axios'
import { toast } from 'sonner'
import type {
  AxiosError,
  AxiosInstance,
  AxiosRequestConfig,
  AxiosResponse,
} from 'axios'
import type { APIResponse } from '@c-cam/types'
import {
  clearAuthData,
  getAccessToken,
  getDeviceInfo,
  setAccessToken,
} from '@/utils/auth'

// Get API URL from environment (Vite uses import.meta.env)
const API_URL = 'http://localhost:5000'

// Import global refresh state from global provider
// This will be available after the provider is initialized
const getGlobalRefreshState = () => {
  try {
    // Try to import the global refresh state
    const { globalRefreshState } = require('@/providers/global-provider')
    return globalRefreshState
  } catch {
    // Fallback to local state if global provider not available
    return {
      isRefreshing: false,
      refreshPromise: null,
      isOnAuthPage: false,
    }
  }
}

// Local fallback state
let localIsRefreshing = false
let failedQueue: Array<{
  resolve: (value: any) => void
  reject: (error: any) => void
}> = []

// Promise to track ongoing refresh
let refreshPromise: Promise<string> | null = null

const processQueue = (error: any, token: string | null = null) => {
  failedQueue.forEach(({ resolve, reject }) => {
    if (error) {
      reject(error)
    } else {
      resolve(token)
    }
  })

  failedQueue = []
}

/**
 * Create a configured axios instance for API requests
 */
export const createAxiosInstance = (
  config?: AxiosRequestConfig,
): AxiosInstance => {
  const axiosInstance = axios.create({
    baseURL: API_URL,
    timeout: 30000, // 30 seconds
    withCredentials: true, // Include HttpOnly cookies
    headers: {
      'Content-Type': 'application/json',
    },
    ...config,
  })

  // Request interceptor
  axiosInstance.interceptors.request.use(
    (value) => {
      // Get token from utils if available
      if (typeof window !== 'undefined') {
        const token = getAccessToken()
        if (token) {
          value.headers.Authorization = `Bearer ${token}`
        }
      }
      return value
    },
    (error) => {
      return Promise.reject(error)
    },
  )

  // Response interceptor
  axiosInstance.interceptors.response.use(
    (response: AxiosResponse) => {
      return response
    },
    async (error: AxiosError<APIResponse>) => {
      const originalRequest = error.config as any

      // Handle API errors
      const errorResponse = error.response?.data

      // Handle authentication errors
      if (error.response?.status === 401 && !originalRequest._retry) {
        const globalState = getGlobalRefreshState()

        // Don't refresh token on auth pages
        if (globalState.isOnAuthPage) {
          console.debug('Axios interceptor: Skipping token refresh on auth page')
          return Promise.reject(error)
        }

        // Check if global refresh is in progress
        if (globalState.isRefreshing && globalState.refreshPromise) {
          console.debug('Axios interceptor: Global refresh in progress, waiting...')
          try {
            const token = await globalState.refreshPromise
            if (token && originalRequest.headers) {
              originalRequest.headers.Authorization = `Bearer ${token}`
              return axiosInstance(originalRequest)
            }
          } catch (refreshError) {
            return Promise.reject(refreshError)
          }
        }

        // Check if local refresh is in progress
        if (localIsRefreshing) {
          // If already refreshing locally, queue this request
          return new Promise((resolve, reject) => {
            failedQueue.push({ resolve, reject })
          })
            .then((token) => {
              if (originalRequest.headers) {
                originalRequest.headers.Authorization = `Bearer ${token}`
              }
              return axiosInstance(originalRequest)
            })
            .catch((err) => {
              return Promise.reject(err)
            })
        }

        originalRequest._retry = true

        // Try to use global refresh function if available
        const globalRefreshFn = (window as any).__globalRefreshToken
        if (globalRefreshFn && typeof globalRefreshFn === 'function') {
          console.debug('Axios interceptor: Using global refresh function')
          try {
            const token = await globalRefreshFn()
            if (token && originalRequest.headers) {
              originalRequest.headers.Authorization = `Bearer ${token}`
              return axiosInstance(originalRequest)
            } else {
              return Promise.reject(new Error('Token refresh failed'))
            }
          } catch (refreshError) {
            console.error('Axios interceptor: Global refresh failed', refreshError)
            return Promise.reject(refreshError)
          }
        }

        // Fallback to local refresh logic
        localIsRefreshing = true

        // Create refresh promise if not exists
        if (!refreshPromise) {
          refreshPromise = (async () => {
            try {
              console.debug('Axios interceptor: Starting local token refresh...')

              // Call refresh token endpoint (refresh token is sent via HttpOnly cookie)
              const deviceInfo = getDeviceInfo()
              const refreshResponse = await axios.post(
                `${API_URL}/api/identity/refresh-token`,
                {
                  deviceInfo,
                },
                {
                  withCredentials: true, // Include HttpOnly cookies
                },
              )

              const { access_token } = refreshResponse.data

              // Store new access token in memory
              setAccessToken(access_token)

              console.debug('Axios interceptor: Local token refreshed successfully')
              return access_token
            } catch (refreshError) {
              console.error('Axios interceptor: Local token refresh failed', refreshError)

              // Clear auth data and redirect
              clearAuthData()

              // Redirect to login page if not already there
              if (
                typeof window !== 'undefined' &&
                !window.location.pathname.includes('/auth/login')
              ) {
                window.location.href = '/auth/login'
              }

              throw refreshError
            } finally {
              refreshPromise = null
            }
          })()
        }

        try {
          const access_token = await refreshPromise

          // Update authorization header for the original request
          if (originalRequest.headers) {
            originalRequest.headers.Authorization = `Bearer ${access_token}`
          }

          // Process queued requests
          processQueue(null, access_token)

          // Retry the original request
          return axiosInstance(originalRequest)
        } catch (refreshError) {
          // Process queued requests with error
          processQueue(refreshError, null)
          return Promise.reject(refreshError)
        } finally {
          localIsRefreshing = false
        }
      }

      // Show toast notification for other errors
      if (errorResponse?.message) {
        toast.error(errorResponse.message)
      } else if (error.message) {
        toast.error(`Request failed: ${error.message}`)
      }

      return Promise.reject(error)
    },
  )

  return axiosInstance
}

// Create default axios instance
export const axiosClient = createAxiosInstance()

// Export default instance
export default axiosClient

/**
 * Generic API request function with type safety
 */
export async function apiRequest<TData = any, TParams = any>(
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH',
  url: string,
  data?: TParams,
  config?: AxiosRequestConfig,
): Promise<TData> {
  let response: AxiosResponse<APIResponse<TData>>

  switch (method) {
    case 'GET':
      response = await axiosClient.get<APIResponse<TData>>(url, {
        params: data,
        ...config,
      })
      break
    case 'POST':
      response = await axiosClient.post<APIResponse<TData>>(url, data, config)
      break
    case 'PUT':
      response = await axiosClient.put<APIResponse<TData>>(url, data, config)
      break
    case 'PATCH':
      response = await axiosClient.patch<APIResponse<TData>>(url, data, config)
      break
    case 'DELETE':
      response = await axiosClient.delete<APIResponse<TData>>(url, {
        data,
        ...config,
      })
      break
    default:
      throw new Error(`Unsupported method: ${method}`)
  }

  // Return the data property from the API response
  return response.data.data as TData
}

/**
 * Convenience methods for common API operations
 */
export const api = {
  get: <TData = any, TParams = any>(
    url: string,
    params?: TParams,
    config?: AxiosRequestConfig,
  ) => apiRequest<TData, TParams>('GET', url, params, config),

  post: <TData = any, TParams = any>(
    url: string,
    data?: TParams,
    config?: AxiosRequestConfig,
  ) => apiRequest<TData, TParams>('POST', url, data, config),

  put: <TData = any, TParams = any>(
    url: string,
    data?: TParams,
    config?: AxiosRequestConfig,
  ) => apiRequest<TData, TParams>('PUT', url, data, config),

  patch: <TData = any, TParams = any>(
    url: string,
    data?: TParams,
    config?: AxiosRequestConfig,
  ) => apiRequest<TData, TParams>('PATCH', url, data, config),

  delete: <TData = any, TParams = any>(
    url: string,
    data?: TParams,
    config?: AxiosRequestConfig,
  ) => apiRequest<TData, TParams>('DELETE', url, data, config),
}
