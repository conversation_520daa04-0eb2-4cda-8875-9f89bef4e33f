{"name": "@c-cam/api", "type": "module", "scripts": {"dev": "npx nodemon", "build": "rimraf dist && tsc --project tsconfig.app.json", "start": "node dist/main.js", "seed": "tsx src/scripts/seed-database.ts", "seed:force": "tsx src/scripts/seed-database.ts --force", "seed:help": "tsx src/scripts/seed-database.ts --help", "lint": "eslint src --ext .ts,.js", "lint:fix": "eslint src --ext .ts,.js --fix", "format": "prettier --write 'src/**/*.{ts,js}'", "check-types": "tsc --noEmit"}, "dependencies": {"@c-cam/core": "workspace:*", "@c-cam/logger": "workspace:*", "@c-cam/shared": "workspace:*", "@c-cam/types": "workspace:*", "@hono/eslint-config": "^2.0.1", "@hono/node-server": "^1.14.2", "@tailwindcss/vite": "^4.0.6", "@types/bcrypt": "^5.0.2", "axios": "^1.9.0", "bcrypt": "^6.0.0", "dotenv": "^16.5.0", "form-data": "^4.0.2", "hono": "^4.7.10", "jsonwebtoken": "^9.0.2", "kill-port": "^2.0.1", "lodash": "^4.17.21", "mongoose": "^8.15.0", "rimraf": "^4.4.1", "tailwindcss": "^4.0.6"}, "devDependencies": {"@c-cam/eslint": "workspace:*", "@c-cam/tsconfig": "workspace:*", "@types/jsonwebtoken": "^9.0.9", "@types/lodash": "^4.17.17", "@types/mongoose": "^5.11.97", "@types/node": "^20.17.50", "eslint": "^9.27.0", "nodemon": "^3.1.10", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.8.3"}}