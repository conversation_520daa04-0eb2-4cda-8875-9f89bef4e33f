import React from 'react'
import { useGlobalContext, useAuth } from '@/providers/global-provider'

/**
 * Test component to verify global provider functionality
 */
export const TestGlobalProvider: React.FC = () => {
  const globalContext = useGlobalContext()
  const auth = useAuth()

  return (
    <div className="p-4 space-y-4">
      <h2 className="text-xl font-bold">Global Provider Test</h2>
      
      <div className="space-y-2">
        <h3 className="text-lg font-semibold">Global State</h3>
        <p>Is Initialized: {globalContext.isInitialized ? 'Yes' : 'No'}</p>
        <p>Current Route: {globalContext.currentRoute}</p>
        <p>Is Refreshing Token: {globalContext.isRefreshingToken ? 'Yes' : 'No'}</p>
      </div>

      <div className="space-y-2">
        <h3 className="text-lg font-semibold">Auth State</h3>
        <p>Is Authenticated: {auth.isAuthenticated ? 'Yes' : 'No'}</p>
        <p>Is Loading: {auth.isLoading ? 'Yes' : 'No'}</p>
        <p>User: {auth.user ? auth.user.username : 'None'}</p>
        <p>Error: {auth.error || 'None'}</p>
      </div>

      <div className="space-y-2">
        <h3 className="text-lg font-semibold">Actions</h3>
        <button 
          onClick={() => auth.refreshAccessToken()}
          className="px-4 py-2 bg-blue-500 text-white rounded mr-2"
          disabled={auth.isLoading}
        >
          Test Refresh Token
        </button>
        <button 
          onClick={() => auth.logout()}
          className="px-4 py-2 bg-red-500 text-white rounded"
          disabled={auth.isLoading}
        >
          Test Logout
        </button>
      </div>
    </div>
  )
}

export default TestGlobalProvider
