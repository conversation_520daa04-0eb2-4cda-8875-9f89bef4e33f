import { Context, Next } from 'hono';
import { Env, MiddlewareHandler } from 'hono/types';
import { IServiceProvider } from '../di/type';

/**
 * Enhanced HTTP context with dependency injection support
 */
export interface IHttpContext {
  context: Context;
  next?: Next;
  serviceProvider: IServiceProvider;
}

/**
 * Environment variables for Hono application
 */
export interface AppEnv extends Env {
  Variables: {
    serviceProvider: IServiceProvider;
    [key: string]: any;
  };
}

/**
 * Type alias for middleware handlers with our custom environment
 */
export type AppMiddlewareHandler = MiddlewareHandler<AppEnv>;

/**
 * Interface for classes that need to properly dispose of resources
 * Classes implementing this interface must release all resources in the dispose method
 */
export interface IDisposable {
  /**
   * Release all resources held by this instance
   * This might include:
   * - Clearing timers (setTimeout, setInterval)
   * - Closing connections (database, Redis, Kafka, etc.)
   * - Unregistering event listeners
   * - Closing file handles
   * - Releasing any other system resources
   *
   * @returns A promise that resolves when all resources have been released
   */
  dispose(): Promise<void>;
}

export type ServiceResponse<T = undefined> = {
  success: boolean;
  message?: string;
  data?: T;
};
