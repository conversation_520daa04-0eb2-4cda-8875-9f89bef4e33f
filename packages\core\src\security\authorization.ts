import { Context, Next } from 'hono';
import { MiddlewareHandler } from 'hono/types';
import { IMiddleware } from '../decorators/middleware.decorator.js';
import {
  handleAuthorization,
  AuthorizationOptions,
} from '../decorators/authorized.decorator.js';
import { PolicyRegistry, IPolicy } from './policy.js';

/**
 * Middleware class for handling authorization based on the @Authorized and @Policy decorators
 */
export class AuthorizationMiddleware implements IMiddleware {
  constructor(private readonly options: AuthorizationOptions) {}

  /**
   * Middleware implementation that delegates to the handleAuthorization function
   */
  async use(c: Context, next: Next): Promise<void> {
    await handleAuthorization(c, next, this.options);
  }
}

/**
 * Factory function to create an authorization middleware instance
 * @param options Authorization options
 * @returns An instance of AuthorizationMiddleware
 */
export function CreateAuthorizationMiddleware(
  options: AuthorizationOptions,
): AuthorizationMiddleware {
  return new AuthorizationMiddleware(options);
}

/**
 * Hono middleware function for global authorization
 * This can be directly used with app.use()
 * @param options Authorization options
 * @returns Hono middleware function
 */
export const GlobalAuthorizedMiddleware: MiddlewareHandler = async (
  c,
  next,
) => {
  // Create middleware with required: false to not block requests without authorization
  const middleware = new AuthorizationMiddleware({ required: false });
  await middleware.use(c, next);
};

/**
 * Register a policy in the policy registry
 * @param policy Policy to register
 */
export function registerPolicy(policy: IPolicy): void {
  const registry = PolicyRegistry.getInstance();
  registry.registerPolicy(policy);
}

/**
 * Get a policy from the policy registry
 * @param name Name of the policy
 * @returns The policy or undefined if not found
 */
export function getPolicy(name: string): IPolicy | undefined {
  const registry = PolicyRegistry.getInstance();
  return registry.getPolicy(name);
}

/**
 * Create a middleware that requires a specific policy
 * @param policyNames Names of the policies to check
 * @param resource Optional resource identifier
 * @param action Optional action being performed
 * @returns A middleware that checks the specified policy
 */
export function requirePolicy(
  policyNames: string[],
  resource?: string,
  action?: string,
): MiddlewareHandler {
  return async (c, next) => {
    const middleware = new AuthorizationMiddleware({
      policies: policyNames,
      resource,
      action,
      required: true,
    });
    await middleware.use(c, next);
  };
}
