# Signed Cookies Implementation

## Overview

This document describes the implementation of signed cookies in the C-Cam API using <PERSON><PERSON>'s cookie helpers. Signed cookies provide enhanced security by preventing tampering and ensuring data integrity.

## What are Signed Cookies?

Signed cookies are regular cookies that include an HMAC SHA-256 signature. This signature:
- Prevents tampering with cookie values
- Ensures data integrity
- Detects if cookies have been modified by clients
- Provides better security than regular cookies

## Implementation Details

### 1. Cookie Helper Functions

The application uses <PERSON><PERSON>'s built-in cookie helpers:

```typescript
import {
  getSignedCookie,
  setSignedCookie,
  deleteCookie,
} from 'hono/cookie';
```

### 2. Secret Management

The cookie signing secret is derived from the JWT secret:

```typescript
private readonly cookieSecret: string = environment.jwt.secret;
```

### 3. Setting Signed Cookies

```typescript
await setSignedCookie(
  context,
  'refresh_token',
  tokenValue,
  this.cookieSecret,
  {
    httpOnly: true,
    secure: true,
    sameSite: 'Strict',
    path: '/api/identity',
    maxAge: 7 * 24 * 60 * 60, // 7 days
  }
);
```

### 4. Reading Signed Cookies

```typescript
const refreshToken = await getSignedCookie(
  context,
  this.cookieSecret,
  'refresh_token'
);

// Returns:
// - string: valid cookie value
// - false: invalid signature (tampered)
// - undefined: cookie not found
```

### 5. Deleting Cookies

```typescript
deleteCookie(context, 'refresh_token', {
  path: '/api/identity',
});
```

## Security Benefits

### Before (Manual Cookie Handling)
```typescript
// Vulnerable to tampering
const refreshTokenCookie = `refresh_token=${tokenDoc.refresh_token}; HttpOnly; Secure; SameSite=Strict; Path=/api/identity; Max-Age=${7 * 24 * 60 * 60}`;
```

### After (Signed Cookies)
```typescript
// Protected by HMAC signature
await setSignedCookie(context, 'refresh_token', tokenDoc.refresh_token, this.cookieSecret, options);
```

## Cookie Options

All signed cookies use these security options:

- `httpOnly: true` - Prevents JavaScript access
- `secure: true` - HTTPS only
- `sameSite: 'Strict'` - CSRF protection
- `path: '/api/identity'` - Scope limitation
- `maxAge: 7 * 24 * 60 * 60` - 7 days expiration

## Error Handling

The implementation handles three cookie states:

1. **Valid Cookie**: Returns the actual value
2. **Invalid Signature**: Returns `false` (triggers re-authentication)
3. **Missing Cookie**: Returns `undefined` (triggers re-authentication)

## Best Practices

1. **Use Strong Secrets**: The signing secret should be cryptographically strong
2. **Consistent Options**: Use the same options when setting and deleting cookies
3. **Path Scoping**: Limit cookie scope to specific paths
4. **Secure Flags**: Always use `httpOnly`, `secure`, and `sameSite`
5. **Expiration**: Set appropriate `maxAge` values

## Migration Notes

### Changes Made

1. **IdentityController**: Updated to use signed cookies for refresh tokens
2. **ControllerBase**: Added helper methods for cookie operations
3. **Security**: Enhanced protection against cookie tampering

### Backward Compatibility

The implementation maintains API compatibility while enhancing security. Existing clients will continue to work without changes.

## Testing

Use the test file `apps/api/src/tests/signed-cookies.test.ts` to verify signed cookie functionality.

## Environment Configuration

Ensure your `.env` file has a strong JWT secret:

```env
DEVELOPMENT_JWT_SECRET=your-super-secret-jwt-key-here-minimum-32-characters
```

## Troubleshooting

### Common Issues

1. **Invalid Signature**: Check if the secret matches between set/get operations
2. **Cookie Not Found**: Verify path and domain settings
3. **HTTPS Required**: Ensure `secure: true` is only used with HTTPS

### Debug Tips

1. Check browser developer tools for cookie values
2. Verify cookie options match between set/delete operations
3. Ensure the signing secret is consistent across requests
