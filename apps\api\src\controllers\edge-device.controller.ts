import { Context } from 'hono';
import {
  Controller,
  ControllerBase,
  HttpContext,
  HttpGet,
  HttpPost,
  HttpPut,
  HttpDelete,
  Inject,
  Param,
  Query,
  UnauthorizedError,
} from '@c-cam/core';
import EdgeDeviceService from '../services/EdgeDeviceService';

@Controller('/api/edge-devices')
export class EdgeDeviceController extends ControllerBase {
  constructor(
    @Inject(EdgeDeviceService) private edgeDeviceService: EdgeDeviceService,
  ) {
    super();
  }

  /**
   * Helper method to get authenticated user ID
   */
  private getAuthenticatedUserId(c: Context): string {
    const userId = c.get('userId');
    if (!userId) {
      throw new UnauthorizedError('You must be authenticated to access this resource');
    }
    return userId;
  }

  /**
   * Get all edge devices
   */
  @HttpGet('/')
  async getEdgeDevices(
    @HttpContext() c: Context,
    @Query('limit') limit?: string,
    @Query('skip') skip?: string,
    @Query('sortBy') sortBy?: string,
    @Query('sortDirection') sortDirection?: string,
  ): Promise<Response> {
    this.getAuthenticatedUserId(c);

    const devices = await this.edgeDeviceService.find({
      limit: limit ? parseInt(limit) : undefined,
      skip: skip ? parseInt(skip) : undefined,
      sortBy,
      sortDirection,
    });

    return this.success(c, { devices });
  }

  /**
   * Find attendance edge devices
   */
  @HttpGet('/attendance')
  async getAttendanceDevices(@HttpContext() c: Context): Promise<Response> {
    this.getAuthenticatedUserId(c);

    const devices = await this.edgeDeviceService.findAttendanceDevices();
    return this.success(c, { devices });
  }

  /**
   * Find edge devices by camera ID
   */
  @HttpGet('/camera/:cameraId')
  async getEdgeDevicesByCameraId(
    @HttpContext() c: Context,
    @Param('cameraId') cameraId: string,
  ): Promise<Response> {
    this.getAuthenticatedUserId(c);
    this.validateIf(!cameraId, 'Camera ID is required');

    const devices = await this.edgeDeviceService.findByCameraId(cameraId);
    return this.success(c, { devices });
  }

  /**
   * Find edge devices by type
   */
  @HttpGet('/type/:type')
  async getEdgeDevicesByType(
    @HttpContext() c: Context,
    @Param('type') type: string,
  ): Promise<Response> {
    this.getAuthenticatedUserId(c);
    this.validateIf(!type, 'Device type is required');

    const devices = await this.edgeDeviceService.findByType(type);
    return this.success(c, { devices });
  }

  /**
   * Find edge devices by status
   */
  @HttpGet('/status/:status')
  async getEdgeDevicesByStatus(
    @HttpContext() c: Context,
    @Param('status') status: string,
  ): Promise<Response> {
    this.getAuthenticatedUserId(c);
    this.validateIf(!status, 'Device status is required');

    const devices = await this.edgeDeviceService.findByStatus(status);
    return this.success(c, { devices });
  }

  /**
   * Find edge device by IP address
   */
  @HttpGet('/ip/:ipAddress')
  async getEdgeDeviceByIpAddress(
    @HttpContext() c: Context,
    @Param('ipAddress') ipAddress: string,
  ): Promise<Response> {
    this.getAuthenticatedUserId(c);
    this.validateIf(!ipAddress, 'IP address is required');

    const device = await this.edgeDeviceService.findByIpAddress(ipAddress);
    this.notFoundIf(!device, 'Edge device not found');

    return this.success(c, { device });
  }

  /**
   * Find edge device by MAC address
   */
  @HttpGet('/mac/:macAddress')
  async getEdgeDeviceByMacAddress(
    @HttpContext() c: Context,
    @Param('macAddress') macAddress: string,
  ): Promise<Response> {
    this.getAuthenticatedUserId(c);
    this.validateIf(!macAddress, 'MAC address is required');

    const device = await this.edgeDeviceService.findByMacAddress(macAddress);
    this.notFoundIf(!device, 'Edge device not found');

    return this.success(c, { device });
  }

  /**
   * Find edge devices by firmware version
   */
  @HttpGet('/firmware/:version')
  async getEdgeDevicesByFirmwareVersion(
    @HttpContext() c: Context,
    @Param('version') version: string,
  ): Promise<Response> {
    this.getAuthenticatedUserId(c);
    this.validateIf(!version, 'Firmware version is required');

    const devices = await this.edgeDeviceService.findByFirmwareVersion(version);
    return this.success(c, { devices });
  }

  /**
   * Get an edge device by ID
   */
  @HttpGet('/:id')
  async getEdgeDeviceById(
    @HttpContext() c: Context,
    @Param('id') id: string,
  ): Promise<Response> {
    this.getAuthenticatedUserId(c);
    this.validateIf(!id, 'Device ID is required');

    const device = await this.edgeDeviceService.findById(id);
    this.notFoundIf(!device, 'Edge device not found');

    return this.success(c, { device });
  }

  /**
   * Create a new edge device
   */
  @HttpPost('/')
  async createEdgeDevice(@HttpContext() c: Context): Promise<Response> {
    const userId = this.getAuthenticatedUserId(c);
    const deviceData = await c.req.json();

    // Sanitize the data
    const sanitizedData = this.sanitizeData(deviceData);

    // Add the creator ID
    sanitizedData.created_by = userId;

    const device = await this.edgeDeviceService.createEdgeDevice(sanitizedData);
    return this.created(c, { device }, 'Edge device created successfully');
  }

  /**
   * Update an edge device
   */
  @HttpPut('/:id')
  async updateEdgeDevice(
    @HttpContext() c: Context,
    @Param('id') id: string,
  ): Promise<Response> {
    this.getAuthenticatedUserId(c);
    this.validateIf(!id, 'Device ID is required');

    const deviceData = await c.req.json();

    // Sanitize the data
    const sanitizedData = this.sanitizeData(deviceData);

    const success = await this.edgeDeviceService.updateEdgeDevice(id, sanitizedData);
    this.validateIf(!success, 'Failed to update edge device');

    return this.success(c, { success: true }, 'Edge device updated successfully');
  }

  /**
   * Delete an edge device
   */
  @HttpDelete('/:id')
  async deleteEdgeDevice(
    @HttpContext() c: Context,
    @Param('id') id: string,
  ): Promise<Response> {
    this.getAuthenticatedUserId(c);
    this.validateIf(!id, 'Device ID is required');

    const success = await this.edgeDeviceService.delete(id);
    this.validateIf(!success, 'Failed to delete edge device');

    return this.success(c, { success: true }, 'Edge device deleted successfully');
  }
}
