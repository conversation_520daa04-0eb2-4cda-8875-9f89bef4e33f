import EdgeDeviceLogsModel, {
  EdgeDeviceLogsDocument,
} from '@/database/entities/EdgeDeviceLogsModel';
import { Repository, Injectable } from '@c-cam/core';

/**
 * Repository for managing edge device logs
 * Extends the BaseRepository with EdgeDeviceLogsDocument type
 */
@Injectable()
class EdgeDeviceLogsRepository extends Repository<EdgeDeviceLogsDocument> {
  constructor() {
    super(EdgeDeviceLogsModel);
  }

  /**
   * Find logs by edge device ID
   * @param edgeDeviceId The edge device ID to search for
   * @returns A promise that resolves to an array of edge device logs
   */
  async findByEdgeDeviceId(
    edgeDeviceId: string,
  ): Promise<EdgeDeviceLogsDocument[]> {
    return this.find({ edge_device_id: edgeDeviceId });
  }

  /**
   * Find logs by log level
   * @param logLevel The log level to search for
   * @returns A promise that resolves to an array of edge device logs
   */
  async findByLogLevel(logLevel: string): Promise<EdgeDeviceLogsDocument[]> {
    return this.find({ log_level: logLevel });
  }

  /**
   * Find logs by date range
   * @param startDate The start date of the range
   * @param endDate The end date of the range
   * @returns A promise that resolves to an array of edge device logs
   */
  async findByDateRange(
    startDate: Date,
    endDate: Date,
  ): Promise<EdgeDeviceLogsDocument[]> {
    return this.find({
      created_at: { $gte: startDate, $lte: endDate },
    });
  }

  /**
   * Find logs by edge device ID and date range
   * @param edgeDeviceId The edge device ID to search for
   * @param startDate The start date of the range
   * @param endDate The end date of the range
   * @returns A promise that resolves to an array of edge device logs
   */
  async findByEdgeDeviceIdAndDateRange(
    edgeDeviceId: string,
    startDate: Date,
    endDate: Date,
  ): Promise<EdgeDeviceLogsDocument[]> {
    return this.find({
      edge_device_id: edgeDeviceId,
      created_at: { $gte: startDate, $lte: endDate },
    });
  }

  /**
   * Find logs by edge device ID and log level
   * @param edgeDeviceId The edge device ID to search for
   * @param logLevel The log level to search for
   * @returns A promise that resolves to an array of edge device logs
   */
  async findByEdgeDeviceIdAndLogLevel(
    edgeDeviceId: string,
    logLevel: string,
  ): Promise<EdgeDeviceLogsDocument[]> {
    return this.find({
      edge_device_id: edgeDeviceId,
      log_level: logLevel,
    });
  }

  /**
   * Delete logs by edge device ID
   * @param edgeDeviceId The edge device ID to delete logs for
   * @returns A promise that resolves to true if any logs were deleted, false otherwise
   */
  async deleteByEdgeDeviceId(edgeDeviceId: string): Promise<boolean> {
    const result = await this.getModel()
      .deleteMany({ edge_device_id: edgeDeviceId })
      .exec();
    return result.deletedCount > 0;
  }
}

export default EdgeDeviceLogsRepository;
