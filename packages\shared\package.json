{"name": "@c-cam/shared", "version": "1.0.0", "main": "dist/index.js", "types": "dist/index.d.ts", "type": "module", "files": ["dist/**"], "exports": {".": "./src/index.ts"}, "scripts": {"build": "tsc --project tsconfig.app.json", "clean": "<PERSON><PERSON><PERSON> dist"}, "dependencies": {"hono": "^4.7.10", "reflect-metadata": "^0.2.2"}, "devDependencies": {"@c-cam/eslint": "workspace:*", "@c-cam/tsconfig": "workspace:*", "eslint": "^9.27.0"}}