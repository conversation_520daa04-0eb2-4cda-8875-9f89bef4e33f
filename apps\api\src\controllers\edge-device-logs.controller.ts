import { Context } from 'hono';
import {
  Controller,
  ControllerBase,
  HttpContext,
  HttpGet,
  HttpPost,
  HttpDelete,
  Inject,
  ValidationError,
  NotFoundError,
  UnauthorizedError,
} from '@c-cam/core';
import EdgeDeviceLogsService from '../services/EdgeDeviceLogsService';

@Controller('/api/edge-device-logs')
export class EdgeDeviceLogsController extends ControllerBase {
  constructor(
    @Inject(EdgeDeviceLogsService) private edgeDeviceLogsService: EdgeDeviceLogsService,
  ) {
    super();
  }

  /**
   * Get all edge device logs
   */
  @HttpGet('/')
  async getEdgeDeviceLogs(@HttpContext() c: Context): Promise<Response> {
    try {
      const userId = c.get('userId');
      if (!userId) {
        throw new UnauthorizedError('You must be authenticated to access this resource');
      }

      const { limit, skip, sortBy, sortDirection } = c.req.query();

      const logs = await this.edgeDeviceLogsService.find({
        limit: limit ? parseInt(limit) : undefined,
        skip: skip ? parseInt(skip) : undefined,
        sortBy,
        sortDirection,
      });

      return this.success(c, { logs });
    } catch (error: any) {
      throw error;
    }
  }

  /**
   * Get an edge device log by ID
   */
  @HttpGet('/:id')
  async getEdgeDeviceLogById(@HttpContext() c: Context): Promise<Response> {
    try {
      const userId = c.get('userId');
      if (!userId) {
        throw new UnauthorizedError('You must be authenticated to access this resource');
      }

      const { id } = c.req.param();
      this.validateIf(!id, 'Edge device log ID is required');
      
      const log = await this.edgeDeviceLogsService.findById(id!);

      if (!log) {
        throw new NotFoundError('Edge device log not found');
      }

      return this.success(c, { log });
    } catch (error: any) {
      throw error;
    }
  }

  /**
   * Create a new edge device log
   */
  @HttpPost('/')
  async createEdgeDeviceLog(@HttpContext() c: Context): Promise<Response> {
    try {
      const userId = c.get('userId');
      if (!userId) {
        throw new UnauthorizedError('You must be authenticated to access this resource');
      }

      const logData = await c.req.json();
      
      // Validate required fields
      this.validateRequiredFields(logData, ['edge_device_id', 'log_level', 'message']);
      
      // Sanitize the data
      const sanitizedData = this.sanitizeData(logData);

      // Add the creator ID
      sanitizedData.created_by = userId;

      const log = await this.edgeDeviceLogsService.createLog(sanitizedData);
      return this.created(c, { log }, 'Edge device log created successfully');
    } catch (error: any) {
      throw error;
    }
  }

  /**
   * Delete an edge device log
   */
  @HttpDelete('/:id')
  async deleteEdgeDeviceLog(@HttpContext() c: Context): Promise<Response> {
    try {
      const userId = c.get('userId');
      if (!userId) {
        throw new UnauthorizedError('You must be authenticated to access this resource');
      }

      const { id } = c.req.param();
      this.validateIf(!id, 'Edge device log ID is required');
      
      const success = await this.edgeDeviceLogsService.delete(id!);

      if (!success) {
        throw new ValidationError('Failed to delete edge device log');
      }

      return this.success(c, { success: true }, 'Edge device log deleted successfully');
    } catch (error: any) {
      throw error;
    }
  }

  /**
   * Find logs by edge device ID
   */
  @HttpGet('/device/:edgeDeviceId')
  async getLogsByEdgeDeviceId(@HttpContext() c: Context): Promise<Response> {
    try {
      const userId = c.get('userId');
      if (!userId) {
        throw new UnauthorizedError('You must be authenticated to access this resource');
      }

      const { edgeDeviceId } = c.req.param();
      this.validateIf(!edgeDeviceId, 'Edge device ID is required');
      
      const logs = await this.edgeDeviceLogsService.findByEdgeDeviceId(edgeDeviceId!);

      return this.success(c, { logs });
    } catch (error: any) {
      throw error;
    }
  }

  /**
   * Find logs by log level
   */
  @HttpGet('/level/:logLevel')
  async getLogsByLogLevel(@HttpContext() c: Context): Promise<Response> {
    try {
      const userId = c.get('userId');
      if (!userId) {
        throw new UnauthorizedError('You must be authenticated to access this resource');
      }

      const { logLevel } = c.req.param();
      this.validateIf(!logLevel, 'Log level is required');
      
      const logs = await this.edgeDeviceLogsService.findByLogLevel(logLevel!);

      return this.success(c, { logs });
    } catch (error: any) {
      throw error;
    }
  }

  /**
   * Find logs by date range
   */
  @HttpGet('/date-range/:startDate/:endDate')
  async getLogsByDateRange(@HttpContext() c: Context): Promise<Response> {
    try {
      const userId = c.get('userId');
      if (!userId) {
        throw new UnauthorizedError('You must be authenticated to access this resource');
      }

      const { startDate, endDate } = c.req.param();
      this.validateIf(!startDate || !endDate, 'Start date and end date are required');
      
      const start = new Date(startDate!);
      const end = new Date(endDate!);

      if (isNaN(start.getTime()) || isNaN(end.getTime())) {
        throw new ValidationError('Invalid date format');
      }

      const logs = await this.edgeDeviceLogsService.findByDateRange(start, end);

      return this.success(c, { logs });
    } catch (error: any) {
      throw error;
    }
  }

  /**
   * Find logs by edge device ID and date range
   */
  @HttpGet('/device/:edgeDeviceId/date-range/:startDate/:endDate')
  async getLogsByEdgeDeviceIdAndDateRange(@HttpContext() c: Context): Promise<Response> {
    try {
      const userId = c.get('userId');
      if (!userId) {
        throw new UnauthorizedError('You must be authenticated to access this resource');
      }

      const { edgeDeviceId, startDate, endDate } = c.req.param();
      this.validateIf(!edgeDeviceId || !startDate || !endDate, 'Edge device ID, start date, and end date are required');
      
      const start = new Date(startDate!);
      const end = new Date(endDate!);

      if (isNaN(start.getTime()) || isNaN(end.getTime())) {
        throw new ValidationError('Invalid date format');
      }

      const logs = await this.edgeDeviceLogsService.findByEdgeDeviceIdAndDateRange(
        edgeDeviceId!,
        start,
        end
      );

      return this.success(c, { logs });
    } catch (error: any) {
      throw error;
    }
  }

  /**
   * Find logs by edge device ID and log level
   */
  @HttpGet('/device/:edgeDeviceId/level/:logLevel')
  async getLogsByEdgeDeviceIdAndLogLevel(@HttpContext() c: Context): Promise<Response> {
    try {
      const userId = c.get('userId');
      if (!userId) {
        throw new UnauthorizedError('You must be authenticated to access this resource');
      }

      const { edgeDeviceId, logLevel } = c.req.param();
      this.validateIf(!edgeDeviceId || !logLevel, 'Edge device ID and log level are required');
      
      const logs = await this.edgeDeviceLogsService.findByEdgeDeviceIdAndLogLevel(
        edgeDeviceId!,
        logLevel!
      );

      return this.success(c, { logs });
    } catch (error: any) {
      throw error;
    }
  }

  /**
   * Delete logs by edge device ID
   */
  @HttpDelete('/device/:edgeDeviceId')
  async deleteLogsByEdgeDeviceId(@HttpContext() c: Context): Promise<Response> {
    try {
      const userId = c.get('userId');
      if (!userId) {
        throw new UnauthorizedError('You must be authenticated to access this resource');
      }

      const { edgeDeviceId } = c.req.param();
      this.validateIf(!edgeDeviceId, 'Edge device ID is required');
      
      const success = await this.edgeDeviceLogsService.deleteByEdgeDeviceId(edgeDeviceId!);

      if (!success) {
        throw new ValidationError('Failed to delete logs');
      }

      return this.success(c, { success: true }, 'Edge device logs deleted successfully');
    } catch (error: any) {
      throw error;
    }
  }
}
