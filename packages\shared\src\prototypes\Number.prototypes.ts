/**
 * Number prototype extensions for improved number handling
 */

declare global {
  interface Number {
    /**
     * Format number with commas as thousands separators
     */
    toCommaSeparated(): string;

    /**
     * Format number as currency with optional symbol
     * @param symbol Currency symbol to use (default: $)
     */
    toCurrency(symbol?: string): string;

    /**
     * Format number as percentage
     * @param precision Number of decimal places (default: 2)
     */
    toPercentage(precision?: number): string;

    /**
     * Round number to specified precision
     * @param precision Number of decimal places
     */
    round(precision?: number): number;

    /**
     * Check if number is between min and max (inclusive)
     * @param min Minimum value
     * @param max Maximum value
     */
    isBetween(min: number, max: number): boolean;
  }
}

// Implementation
Number.prototype.toCommaSeparated = function(): string {
  return this.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
};

Number.prototype.toCurrency = function(symbol: string = '$'): string {
  // First convert to fixed precision, then apply comma separation manually
  return `${symbol}${this.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ',')}`;
};

Number.prototype.toPercentage = function(precision: number = 2): string {
  return `${(this.valueOf() * 100).toFixed(precision)}%`;
};

Number.prototype.round = function(precision: number = 0): number {
  const factor = Math.pow(10, precision);
  return Math.round(this.valueOf() * factor) / factor;
};

Number.prototype.isBetween = function(min: number, max: number): boolean {
  return this.valueOf() >= min && this.valueOf() <= max;
};

export {}; // This export is needed to make the file a module