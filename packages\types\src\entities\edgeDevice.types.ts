/**
 * Edge Device Attributes Interface
 * Defines the core data structure for edge devices
 */
export interface EdgeDeviceAttributes {
  id: string;
  camera_id: string;
  name: string;
  type: string;
  ip_address: string;
  mac_address: string;
  firmware_version: string;
  is_attendance_device: boolean;
  attendance_mode: string;
  preferred_stream_mode: string;
  status: string;
  created_at: Date;
  created_by: string;
}
