import { Job } from 'bullmq';
import { logger } from '@c-cam/logger';
import { BullMQConnection } from './bullmq.js';
import { BullMQClient, JobData } from './client.js';

/**
 * Queue configuration interface for backward compatibility
 */
export interface IQueueConfig {
  /**
   * Queue name
   */
  name: string;

  /**
   * Redis connection configuration
   */
  connection: {
    host: string;
    port: number;
    password?: string;
    db?: number;
    maxRetriesPerRequest?: number;
  };

  /**
   * Default job options
   */
  defaultJobOptions?: {
    removeOnComplete?: number;
    removeOnFail?: number;
    attempts?: number;
    backoff?: {
      type: 'fixed' | 'exponential';
      delay: number;
    };
  };
}

/**
 * Worker configuration interface for backward compatibility
 */
export interface IWorkerConfig {
  /**
   * Queue name to process
   */
  queueName: string;

  /**
   * Redis connection configuration
   */
  connection: {
    host: string;
    port: number;
    password?: string;
    db?: number;
    maxRetriesPerRequest?: number;
  };

  /**
   * Worker concurrency
   */
  concurrency?: number;
}

/**
 * Job processor function type for backward compatibility
 */
export type JobProcessor<T = any> = (job: Job<T>) => Promise<any>;

/**
 * Queue event handlers interface for backward compatibility
 */
export interface IQueueEventHandlers {
  onCompleted?: (job: Job, result: any) => Promise<void>;
  onFailed?: (job: Job | null, error: Error) => Promise<void>;
  onProgress?: (job: Job, progress: number) => Promise<void>;
  onStalled?: (jobId: string) => void;
  onWaiting?: (jobId: string) => void;
  onActive?: (job: Job) => void;
}

/**
 * Job data interface for backward compatibility
 */
export interface IJobData {
  type: string;
  payload: any;
  id?: string;
}

/**
 * Job options interface for backward compatibility
 */
export interface IJobOptions {
  priority?: number;
  delay?: number;
  jobId?: string;
  attempts?: number;
  backoff?: {
    type: 'fixed' | 'exponential';
    delay: number;
  };
}

/**
 * Queue health status interface
 */
export interface IQueueHealthStatus {
  status: 'healthy' | 'unhealthy';
  details: {
    queueInitialized: boolean;
    workerRunning: boolean;
    redisConnected: boolean;
    stats: {
      waiting: number;
      active: number;
      completed: number;
      failed: number;
      delayed: number;
      paused: number;
    } | null;
    issues: string[];
  };
}

/**
 * Queue service for backward compatibility with existing video processing service
 * This service wraps the new BullMQ integration to maintain API compatibility
 */
export class QueueService {
  private connection: BullMQConnection;
  private client: BullMQClient;
  private isInitialized = false;

  constructor() {
    this.connection = BullMQConnection.getInstance();
    this.client = new BullMQClient();
  }

  /**
   * Create a queue
   * @param config Queue configuration
   */
  public async createQueue(config: IQueueConfig): Promise<void> {
    if (!this.isInitialized) {
      // Initialize the connection if not already done
      await this.connection.initialize({
        connection: {
          host: config.connection.host,
          port: config.connection.port,
          password: config.connection.password,
          db: config.connection.db,
        },
        defaultQueueOptions: {
          defaultJobOptions: config.defaultJobOptions,
        },
        healthCheck: {
          enabled: true,
          interval: 30000,
        },
      });
      this.isInitialized = true;
    }

    // Create the queue
    await this.client.createQueue(config.name, {
      defaultJobOptions: config.defaultJobOptions,
    });

    logger.info(`Queue '${config.name}' created via QueueService`, {
      queueName: config.name,
    });
  }

  /**
   * Create a worker
   * @param config Worker configuration
   * @param processor Job processor function
   * @param eventHandlers Event handlers
   */
  public async createWorker<T = any>(
    config: IWorkerConfig,
    processor: JobProcessor<T>,
    eventHandlers?: IQueueEventHandlers,
  ): Promise<void> {
    // Convert event handlers to the new format
    const bullmqEventHandlers = eventHandlers
      ? {
          onCompleted: eventHandlers.onCompleted,
          onFailed: eventHandlers.onFailed,
          onProgress: eventHandlers.onProgress
            ? async (job: Job, progress: any) => {
                if (typeof progress === 'number' && eventHandlers.onProgress) {
                  await eventHandlers.onProgress(job, progress);
                }
              }
            : undefined,
          onStalled: eventHandlers.onStalled,
          onWaiting: eventHandlers.onWaiting,
          onActive: eventHandlers.onActive,
        }
      : undefined;

    // Create the worker
    await this.client.createWorker(
      config.queueName,
      processor,
      {
        concurrency: config.concurrency || 1,
      },
      bullmqEventHandlers,
    );

    logger.info(
      `Worker created for queue '${config.queueName}' via QueueService`,
      {
        queueName: config.queueName,
        concurrency: config.concurrency || 1,
      },
    );
  }

  /**
   * Add a job to a queue
   * @param queueName Queue name
   * @param jobData Job data
   * @param options Job options
   */
  public async addJob(
    queueName: string,
    jobData: IJobData,
    options?: IJobOptions,
  ): Promise<Job> {
    const bullmqJobData: JobData = {
      type: jobData.type,
      payload: jobData.payload,
      id: jobData.id,
    };

    const bullmqOptions = options
      ? {
          priority: options.priority,
          delay: options.delay,
          jobId: options.jobId,
          attempts: options.attempts,
          backoff: options.backoff,
        }
      : undefined;

    return this.client.addJob(queueName, bullmqJobData, bullmqOptions);
  }

  /**
   * Remove a job from a queue
   * @param queueName Queue name
   * @param jobId Job ID
   */
  public async removeJob(queueName: string, jobId: string): Promise<boolean> {
    try {
      const job = await this.client.getJob(queueName, jobId);
      if (job) {
        await job.remove();
        return true;
      }
      return false;
    } catch (error) {
      logger.error(`Failed to remove job ${jobId} from queue ${queueName}`, {
        error: error instanceof Error ? error.message : String(error),
        queueName,
        jobId,
      });
      return false;
    }
  }

  /**
   * Pause a queue
   * @param queueName Queue name
   */
  public async pauseQueue(queueName: string): Promise<void> {
    await this.client.pauseQueue(queueName);
  }

  /**
   * Resume a queue
   * @param queueName Queue name
   */
  public async resumeQueue(queueName: string): Promise<void> {
    await this.client.resumeQueue(queueName);
  }

  /**
   * Clean completed jobs
   * @param queueName Queue name
   * @param olderThanMs Age threshold in milliseconds
   */
  public async cleanCompleted(
    queueName: string,
    olderThanMs: number,
  ): Promise<number> {
    const cleanedJobs = await this.client.cleanQueue(
      queueName,
      olderThanMs,
      'completed',
    );
    return cleanedJobs.length;
  }

  /**
   * Clean failed jobs
   * @param queueName Queue name
   * @param olderThanMs Age threshold in milliseconds
   */
  public async cleanFailed(
    queueName: string,
    olderThanMs: number,
  ): Promise<number> {
    const cleanedJobs = await this.client.cleanQueue(
      queueName,
      olderThanMs,
      'failed',
    );
    return cleanedJobs.length;
  }

  /**
   * Get queue health status
   * @param queueName Queue name
   */
  public async getQueueHealth(queueName: string): Promise<IQueueHealthStatus> {
    try {
      const healthStatus = this.connection.getHealthStatus();
      const stats = await this.client.getQueueStats(queueName);

      return {
        status: healthStatus.isHealthy ? 'healthy' : 'unhealthy',
        details: {
          queueInitialized: this.isInitialized,
          workerRunning: healthStatus.activeWorkers > 0,
          redisConnected: healthStatus.isConnected,
          stats,
          issues: healthStatus.lastError ? [healthStatus.lastError] : [],
        },
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        details: {
          queueInitialized: this.isInitialized,
          workerRunning: false,
          redisConnected: false,
          stats: null,
          issues: [error instanceof Error ? error.message : String(error)],
        },
      };
    }
  }

  /**
   * Get a job by ID
   * @param queueName Queue name
   * @param jobId Job ID
   */
  public async getJob(queueName: string, jobId: string): Promise<Job | undefined> {
    return this.client.getJob(queueName, jobId);
  }

  /**
   * Get queue statistics
   * @param queueName Queue name
   */
  public async getQueueStats(queueName: string): Promise<{
    waiting: number;
    active: number;
    completed: number;
    failed: number;
    delayed: number;
    paused: number;
  }> {
    return this.client.getQueueStats(queueName);
  }
}
