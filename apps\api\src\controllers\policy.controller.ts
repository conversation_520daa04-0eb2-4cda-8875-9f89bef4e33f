import { Context } from 'hono';
import {
  Controller,
  ControllerBase,
  HttpContext,
  HttpGet,
  HttpPost,
  <PERSON>ttpPut,
  <PERSON>ttpDelete,
  Inject,
  Authorized,
  Param,
  Body,
  Query,
  UnauthorizedError,
  NotFoundError,
  ValidationError,
} from '@c-cam/core';
import PolicyService from '../services/PolicyService';

@Controller('/api/policies')
@Authorized({ roles: ['admin'] })
export class PolicyController extends ControllerBase {
  constructor(@Inject(PolicyService) private policyService: PolicyService) {
    super();
  }

  /**
   * Helper method to get authenticated user ID
   */
  private getAuthenticatedUserId(c: Context): string {
    const userId = c.get('userId');
    if (!userId) {
      throw new UnauthorizedError('You must be authenticated to access this resource');
    }
    return userId;
  }

  /**
   * Get all policies
   */
  @HttpGet('/')
  async getPolicies(
    @HttpContext() c: Context,
    @Query('limit') limit?: string,
    @Query('skip') skip?: string,
    @Query('sortBy') sortBy?: string,
    @Query('sortDirection') sortDirection?: string,
  ): Promise<Response> {
    const userId = this.getAuthenticatedUserId(c);

    const policies = await this.policyService.find({
      limit: limit ? parseInt(limit) : undefined,
      skip: skip ? parseInt(skip) : undefined,
      sortBy,
      sortDirection,
    });

    return this.success(c, { policies });
  }

  /**
   * Get a policy by ID
   */
  @HttpGet('/:id')
  async getPolicyById(
    @HttpContext() c: Context,
    @Param('id') id: string,
  ): Promise<Response> {
    this.getAuthenticatedUserId(c);
    this.validateIf(!id, 'Policy ID is required');

    const policy = await this.policyService.findById(id);
    this.notFoundIf(!policy, 'Policy not found');

    return this.success(c, { policy });
  }

  /**
   * Create a new policy
   */
  @HttpPost('/')
  async createPolicy(
    @HttpContext() c: Context,
    @Body() policyData: any,
  ): Promise<Response> {
    const userId = this.getAuthenticatedUserId(c);

    // Validate required fields
    this.validateRequiredFields(policyData, ['name', 'type', 'conditions']);

    // Sanitize the data
    const sanitizedData = this.sanitizeData(policyData);

    // Add the creator ID
    sanitizedData.createdBy = userId;

    const policy = await this.policyService.createPolicy(sanitizedData);
    return this.created(c, { policy }, 'Policy created successfully');
  }

  /**
   * Update a policy
   */
  @HttpPut('/:id')
  async updatePolicy(
    @HttpContext() c: Context,
    @Param('id') id: string,
    @Body() updateData: any,
  ): Promise<Response> {
    const userId = this.getAuthenticatedUserId(c);
    this.validateIf(!id, 'Policy ID is required');

    // Sanitize the data
    const sanitizedData = this.sanitizeData(updateData);

    // Add the updater ID
    sanitizedData.updatedBy = userId;

    const policy = await this.policyService.update(id, sanitizedData);
    this.notFoundIf(!policy, 'Policy not found or update failed');

    return this.success(c, { policy }, 'Policy updated successfully');
  }

  /**
   * Delete a policy
   */
  @HttpDelete('/:id')
  async deletePolicy(
    @HttpContext() c: Context,
    @Param('id') id: string,
  ): Promise<Response> {
    this.getAuthenticatedUserId(c);
    this.validateIf(!id, 'Policy ID is required');

    const success = await this.policyService.delete(id);
    this.validateIf(!success, 'Failed to delete policy');

    return this.success(c, { success: true }, 'Policy deleted successfully');
  }

  /**
   * Find policies by type
   */
  @HttpGet('/type/:type')
  async getPoliciesByType(
    @HttpContext() c: Context,
    @Param('type') type: string,
  ): Promise<Response> {
    this.getAuthenticatedUserId(c);
    this.validateIf(!type, 'Policy type is required');

    const policies = await this.policyService.findByType(type);
    return this.success(c, { policies });
  }

  /**
   * Find policies by resource
   */
  @HttpGet('/resource/:resource')
  async getPoliciesByResource(
    @HttpContext() c: Context,
    @Param('resource') resource: string,
  ): Promise<Response> {
    this.getAuthenticatedUserId(c);
    this.validateIf(!resource, 'Resource is required');

    const policies = await this.policyService.findByResource(resource);
    return this.success(c, { policies });
  }

  /**
   * Find policies for a user
   * Used for token generation and authorization
   */
  @HttpGet('/user/:userId')
  async getPoliciesForUser(
    @HttpContext() c: Context,
    @Param('userId') userId: string,
    @Query('roles') roles?: string,
    @Query('attributes') attributes?: string,
  ): Promise<Response> {
    this.getAuthenticatedUserId(c);
    this.validateIf(!userId, 'User ID is required');

    let userRoles: string[] = [];
    let userAttributes: Record<string, any> = {};

    try {
      if (roles) {
        userRoles = JSON.parse(roles);
      }
      if (attributes) {
        userAttributes = JSON.parse(attributes);
      }
    } catch (e) {
      throw new ValidationError('Invalid roles or attributes format');
    }

    const policies = await this.policyService.findPoliciesForUser(
      userId,
      userRoles,
      userAttributes,
    );

    return this.success(c, { policies });
  }

  /**
   * Get policy statements for inclusion in access token
   */
  @HttpGet('/token-statements/:userId')
  async getPolicyStatementsForToken(
    @HttpContext() c: Context,
    @Param('userId') userId: string,
    @Query('roles') roles?: string,
    @Query('attributes') attributes?: string,
  ): Promise<Response> {
    this.getAuthenticatedUserId(c);
    this.validateIf(!userId, 'User ID is required');

    let userRoles: string[] = [];
    let userAttributes: Record<string, any> = {};

    try {
      if (roles) {
        userRoles = JSON.parse(roles);
      }
      if (attributes) {
        userAttributes = JSON.parse(attributes);
      }
    } catch (e) {
      throw new ValidationError('Invalid roles or attributes format');
    }

    const policyStatements =
      await this.policyService.getPolicyStatementsForToken(
        userId,
        userRoles,
        userAttributes,
      );

    return this.success(c, { policyStatements });
  }
}
