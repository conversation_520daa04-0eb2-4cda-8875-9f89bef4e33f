import { Document } from 'mongoose';
import { createSchema, createModel } from '@c-cam/core';
import { FaceRecognitionLogsAttributes } from '@c-cam/types';

/**
 * Face Recognition Logs Document Interface
 * Extends the FaceRecognitionLogsAttributes (excluding id) and Document
 */
export interface FaceRecognitionLogsDocument
  extends Omit<FaceRecognitionLogsAttributes, 'id'>,
    Document {}

/**
 * Face Recognition Logs Schema
 * Defines the MongoDB schema for face recognition logs
 */
const FaceRecognitionLogsSchema = createSchema({
  edge_device_id: {
    type: String,
    ref: 'edge_device',
    required: true,
  },
  user_id: {
    type: String,
    ref: 'users',
    required: true,
  },
  device_id: { type: String, required: true },
  camera_id: {
    type: String,
    ref: 'camera',
    required: true,
  },
  recognized_at: { type: Date, required: true },
  image_url: { type: String, required: false },
  similarity_percent: { type: Number, required: true },
  status: {
    type: String,
    required: true,
    enum: ['success', 'failure', 'pending'],
  },
  created_by: { type: String, required: true },
});

// Add indexes
FaceRecognitionLogsSchema.index({ user_id: 1, recognized_at: -1 });
FaceRecognitionLogsSchema.index({ edge_device_id: 1, recognized_at: -1 });

// Create and export the model
const FaceRecognitionLogsModel = createModel<FaceRecognitionLogsDocument>(
  'face_recognition_logs',
  FaceRecognitionLogsSchema,
);

export default FaceRecognitionLogsModel;
