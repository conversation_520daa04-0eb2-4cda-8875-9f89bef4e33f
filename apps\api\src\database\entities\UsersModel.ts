import { Document } from 'mongoose';
import { createSchema, createModel } from '@c-cam/core';
import { UsersAttributes } from '@c-cam/types';

/**
 * Users Document Interface
 * Extends the UsersAttributes (excluding id) and Document
 */
export interface UsersDocument extends Omit<UsersAttributes, 'id'>, Document {
  checkPassword(password: string): Promise<boolean>;
}

/**
 * Users Schema
 * Defines the MongoDB schema for users
 */
const UsersSchema = createSchema({
  unit_id: {
    type: String,
    ref: 'unit',
    required: true,
  },
  face_id: {
    type: String,
    required: false,
  },
  member_role_id: {
    type: String,
    ref: 'member_role',
    required: false,
  },
  code: { type: String, required: true, unique: true },
  name: { type: String, required: true },
  email: { type: String, required: false, unique: true, sparse: true },
  phone: { type: String, required: false },
  dob: { type: Date, required: false },
  gender: { type: String, enum: ['male', 'female', 'other'] },
  username: { type: String, required: true, unique: true },
  password: { type: String, required: true, select: false },
  created_by: { type: String, required: true },
});

// Add method to check password
UsersSchema.methods.checkPassword = async function (password: string): Promise<boolean> {
  // In a real implementation, you would use bcrypt.compare or similar
  // For now, we'll just do a simple comparison
  const bcrypt = await import('bcrypt');
  const isValid = await bcrypt.compare(password, this.password);
  return isValid;
};

// Create and export the model
const UsersModel = createModel<UsersDocument>('users', UsersSchema);

export default UsersModel;
