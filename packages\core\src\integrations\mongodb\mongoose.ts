import mongoose, { Connection, ConnectOptions } from 'mongoose';
import { logger } from '@c-cam/logger';

/**
 * Mongoose connection options
 */
export interface MongooseOptions {
  /**
   * MongoDB connection URI
   */
  uri: string;

  /**
   * Connection options
   */
  options?: ConnectOptions;
}

/**
 * Mongoose connection manager
 */
export class MongooseConnection {
  private static _instance: MongooseConnection;
  private _defaultConnection: Connection | null = null;
  private _options: MongooseOptions | null = null;

  /**
   * Get the singleton instance
   */
  public static getInstance(): MongooseConnection {
    if (!MongooseConnection._instance) {
      MongooseConnection._instance = new MongooseConnection();
    }
    return MongooseConnection._instance;
  }

  /**
   * Get the current connection options
   * @returns The connection options or null if not initialized
   */
  public getOptions(): MongooseOptions | null {
    return this._options;
  }

  /**
   * Initialize the Mongoose connection
   * @param options Connection options
   * @returns Promise resolving to the established connection
   * @throws Error if connection fails or if already initialized
   */
  public async initialize(options: MongooseOptions): Promise<Connection> {
    if (this._defaultConnection && this._defaultConnection.readyState === 1) {
      logger.warn('Mongoose connection already initialized and connected');
      return this._defaultConnection;
    }

    this._options = options;

    try {
      // Set mongoose options for better performance and compatibility
      mongoose.set('strictQuery', false);
      mongoose.set('bufferCommands', false);

      // Extract database name from URI for logging purposes
      const dbName = this._extractDatabaseName(options.uri);

      // Connect to MongoDB using the full URI
      await mongoose.connect(options.uri, {
        maxPoolSize: 10,
        serverSelectionTimeoutMS: 5000,
        socketTimeoutMS: 45000,
        ...options.options,
      });

      this._defaultConnection = mongoose.connection;

      // Set up event listeners
      this._setupConnectionEventListeners(this._defaultConnection, dbName);

      logger.info('Mongoose successfully connected to MongoDB', {
        database: dbName,
        readyState: this._defaultConnection.readyState,
      });

      return this._defaultConnection;
    } catch (error) {
      logger.error('Failed to connect to MongoDB with Mongoose', {
        error: error instanceof Error ? error.message : String(error),
        uri: options.uri.replace(/\/\/([^:]+):([^@]+)@/, '//***:***@'), // Hide credentials in logs
      });

      // Reset connection state on failure
      this._defaultConnection = null;
      this._options = null;

      throw error;
    }
  }

  /**
   * Extract database name from MongoDB URI
   * @param uri MongoDB connection URI
   * @returns Database name or 'unknown' if not found
   */
  private _extractDatabaseName(uri: string): string {
    try {
      // Handle MongoDB URI format: mongodb://[username:password@]host1[:port1][,...hostN[:portN]][/[defaultauthdb][?options]]
      const url = new URL(uri);
      const pathname = url.pathname;

      if (pathname && pathname.length > 1) {
        // Remove leading slash and get database name (before any query parameters)
        const dbName = pathname.substring(1).split('?')[0];
        return dbName || 'unknown';
      }

      return 'unknown';
    } catch (error) {
      logger.warn('Failed to extract database name from URI', { error });
      return 'unknown';
    }
  }

  /**
   * Set up event listeners for a connection
   * @param connection The connection to set up listeners for
   * @param dbName The database name
   */
  private _setupConnectionEventListeners(connection: Connection, dbName: string): void {
    // Remove any existing listeners to prevent duplicates
    connection.removeAllListeners('connected');
    connection.removeAllListeners('error');
    connection.removeAllListeners('disconnected');
    connection.removeAllListeners('reconnected');

    connection.on('connected', () => {
      logger.info(`Mongoose connected to MongoDB database: ${dbName}`, {
        readyState: connection.readyState,
        host: connection.host,
        port: connection.port,
      });
    });

    connection.on('error', (err) => {
      logger.error(`Mongoose connection error for database: ${dbName}`, {
        error: err instanceof Error ? err.message : String(err),
        readyState: connection.readyState,
      });
    });

    connection.on('disconnected', () => {
      logger.warn(`Mongoose disconnected from MongoDB database: ${dbName}`, {
        readyState: connection.readyState,
      });
    });

    connection.on('reconnected', () => {
      logger.info(`Mongoose reconnected to MongoDB database: ${dbName}`, {
        readyState: connection.readyState,
      });
    });
  }

  /**
   * Get the default Mongoose connection
   * @returns The active connection
   * @throws Error if connection is not initialized or not connected
   */
  public getConnection(): Connection {
    if (!this._defaultConnection) {
      throw new Error('Mongoose connection not initialized. Call initialize() first.');
    }

    if (this._defaultConnection.readyState !== 1) {
      throw new Error(
        `Mongoose connection is not ready. Current state: ${this._defaultConnection.readyState}`,
      );
    }

    return this._defaultConnection;
  }

  /**
   * Check if the connection is active and ready
   * @returns True if connection is active, false otherwise
   */
  public isConnected(): boolean {
    return this._defaultConnection?.readyState === 1;
  }

  /**
   * Get connection status information
   * @returns Connection status details
   */
  public getConnectionStatus(): {
    isConnected: boolean;
    readyState: number;
    host?: string;
    port?: number;
    name?: string;
  } {
    if (!this._defaultConnection) {
      return {
        isConnected: false,
        readyState: 0,
      };
    }

    return {
      isConnected: this._defaultConnection.readyState === 1,
      readyState: this._defaultConnection.readyState,
      host: this._defaultConnection.host,
      port: this._defaultConnection.port,
      name: this._defaultConnection.name,
    };
  }

  /**
   * Close all Mongoose connections gracefully
   * @param force Whether to force close the connection
   */
  public async close(force: boolean = false): Promise<void> {
    try {
      if (this._defaultConnection && mongoose.connection.readyState !== 0) {
        const dbName = this._defaultConnection.name || 'unknown';

        logger.info(`Closing Mongoose connection to database: ${dbName}`, {
          force,
          readyState: this._defaultConnection.readyState,
        });

        if (force) {
          await mongoose.connection.close(true);
        } else {
          await mongoose.disconnect();
        }

        this._defaultConnection = null;
        this._options = null;

        logger.info('Successfully closed Mongoose connection');
      } else {
        logger.info('No active Mongoose connection to close');
      }
    } catch (error) {
      logger.error('Error while closing Mongoose connection', {
        error: error instanceof Error ? error.message : String(error),
      });
      throw error;
    }
  }
}

/**
 * Query options for repository operations
 */
export interface QueryOptions {
  /** Limit the number of results */
  limit?: number;
  /** Skip a number of results */
  skip?: number;
  /** Sort order */
  sort?: Record<string, 1 | -1>;
  /** Fields to select */
  select?: string | Record<string, 0 | 1>;
  /** Whether to populate referenced documents */
  populate?: string | string[];
}

/**
 * Base repository interface for Mongoose
 */
export interface IRepository<T extends mongoose.Document> {
  /**
   * Find a document by ID
   * @param id Document ID
   * @param options Query options
   * @returns Promise resolving to the document or null if not found
   */
  findById(id: string | mongoose.Types.ObjectId, options?: QueryOptions): Promise<T | null>;

  /**
   * Find documents by a filter
   * @param filter Query filter
   * @param options Query options
   * @returns Promise resolving to an array of documents
   */
  find(filter?: Record<string, any>, options?: QueryOptions): Promise<T[]>;

  /**
   * Find a single document by a filter
   * @param filter Query filter
   * @param options Query options
   * @returns Promise resolving to the document or null if not found
   */
  findOne(filter: Record<string, any>, options?: QueryOptions): Promise<T | null>;

  /**
   * Create a new document
   * @param data Document data
   * @returns Promise resolving to the created document
   */
  create(data: Partial<T>): Promise<T>;

  /**
   * Update a document by ID
   * @param id Document ID
   * @param data Update data
   * @returns Promise resolving to true if document was modified
   */
  update(id: string | mongoose.Types.ObjectId, data: Partial<T>): Promise<boolean>;

  /**
   * Delete a document by ID
   * @param id Document ID
   * @returns Promise resolving to true if document was deleted
   */
  delete(id: string | mongoose.Types.ObjectId): Promise<boolean>;

  /**
   * Check if a document exists
   * @param filter Query filter
   * @returns Promise resolving to true if document exists
   */
  exists(filter: Record<string, any>): Promise<boolean>;
}

/**
 * Base repository implementation for Mongoose
 */
export abstract class Repository<T extends mongoose.Document> implements IRepository<T> {
  protected readonly model: mongoose.Model<T>;

  /**
   * Create a new repository
   * @param model The Mongoose model
   */
  constructor(model: mongoose.Model<T>) {
    this.model = model;
  }

  /**
   * Get the Mongoose model
   * @returns The Mongoose model instance
   */
  protected getModel(): mongoose.Model<T> {
    return this.model;
  }

  /**
   * Apply query options to a Mongoose query
   * @param query The Mongoose query
   * @param options Query options to apply
   * @returns The modified query
   */
  protected applyQueryOptions<Q>(query: Q, options?: QueryOptions): Q {
    if (!options) return query;

    const mongooseQuery = query as any;

    if (options.limit) {
      mongooseQuery.limit(options.limit);
    }

    if (options.skip) {
      mongooseQuery.skip(options.skip);
    }

    if (options.sort) {
      mongooseQuery.sort(options.sort);
    }

    if (options.select) {
      mongooseQuery.select(options.select);
    }

    if (options.populate) {
      if (Array.isArray(options.populate)) {
        options.populate.forEach((path) => mongooseQuery.populate(path));
      } else {
        mongooseQuery.populate(options.populate);
      }
    }

    return query;
  }

  /**
   * Find a document by ID
   * @param id Document ID
   * @param options Query options
   * @returns Promise resolving to the document or null if not found
   */
  public async findById(
    id: string | mongoose.Types.ObjectId,
    options?: QueryOptions,
  ): Promise<T | null> {
    try {
      const query = this.getModel().findById(id);
      return this.applyQueryOptions(query, options).exec();
    } catch (error) {
      logger.error('Error finding document by ID', {
        id: id.toString(),
        model: this.model.modelName,
        error: error instanceof Error ? error.message : String(error),
      });
      throw error;
    }
  }

  /**
   * Find documents by a filter
   * @param filter Query filter
   * @param options Query options
   * @returns Promise resolving to an array of documents
   */
  public async find(filter: Record<string, any> = {}, options?: QueryOptions): Promise<T[]> {
    try {
      const query = this.getModel().find(filter);
      return this.applyQueryOptions(query, options).exec();
    } catch (error) {
      logger.error('Error finding documents', {
        filter,
        model: this.model.modelName,
        error: error instanceof Error ? error.message : String(error),
      });
      throw error;
    }
  }

  /**
   * Find a single document by a filter
   * @param filter Query filter
   * @param options Query options
   * @returns Promise resolving to the document or null if not found
   */
  public async findOne(filter: Record<string, any>, options?: QueryOptions): Promise<T | null> {
    try {
      const query = this.getModel().findOne(filter);
      return this.applyQueryOptions(query, options).exec();
    } catch (error) {
      logger.error('Error finding single document', {
        filter,
        model: this.model.modelName,
        error: error instanceof Error ? error.message : String(error),
      });
      throw error;
    }
  }

  /**
   * Create a new document
   * @param data Document data
   * @returns Promise resolving to the created document
   */
  public async create(data: Partial<T>): Promise<T> {
    try {
      const model = this.getModel();
      const document = new model(data);
      return await document.save();
    } catch (error) {
      logger.error('Error creating document', {
        model: this.model.modelName,
        error: error instanceof Error ? error.message : String(error),
      });
      throw error;
    }
  }

  /**
   * Update a document by ID
   * @param id Document ID
   * @param data Update data
   * @returns Promise resolving to true if document was modified
   */
  public async update(id: string | mongoose.Types.ObjectId, data: Partial<T>): Promise<boolean> {
    try {
      const result = await this.getModel().updateOne({ _id: id }, { $set: data }).exec();
      return result.modifiedCount > 0;
    } catch (error) {
      logger.error('Error updating document by ID', {
        id: id.toString(),
        model: this.model.modelName,
        error: error instanceof Error ? error.message : String(error),
      });
      throw error;
    }
  }

  /**
   * Update many documents by a filter
   * @param filter Query filter
   * @param data Update data
   * @returns Promise resolving to true if any documents were modified
   */
  public async updateMany(filter: Record<string, any>, data: Partial<T>): Promise<boolean> {
    try {
      const result = await this.getModel().updateMany(filter, { $set: data }).exec();
      return result.modifiedCount > 0;
    } catch (error) {
      logger.error('Error updating many documents', {
        filter,
        model: this.model.modelName,
        error: error instanceof Error ? error.message : String(error),
      });
      throw error;
    }
  }

  /**
   * Delete a document by ID
   * @param id Document ID
   * @returns Promise resolving to true if document was deleted
   */
  public async delete(id: string | mongoose.Types.ObjectId): Promise<boolean> {
    try {
      const result = await this.getModel().deleteOne({ _id: id }).exec();
      return result.deletedCount > 0;
    } catch (error) {
      logger.error('Error deleting document by ID', {
        id: id.toString(),
        model: this.model.modelName,
        error: error instanceof Error ? error.message : String(error),
      });
      throw error;
    }
  }

  /**
   * Delete many documents by a filter
   * @param filter Query filter
   * @returns Promise resolving to true if any documents were deleted
   */
  public async deleteMany(filter: Record<string, any>): Promise<boolean> {
    try {
      const result = await this.getModel().deleteMany(filter).exec();
      return result.deletedCount > 0;
    } catch (error) {
      logger.error('Error deleting many documents', {
        filter,
        model: this.model.modelName,
        error: error instanceof Error ? error.message : String(error),
      });
      throw error;
    }
  }

  /**
   * Check if a document exists by filter
   * @param filter Query filter
   * @returns Promise resolving to true if document exists
   */
  public async exists(filter: Record<string, any>): Promise<boolean> {
    try {
      const result = await this.getModel().countDocuments(filter).exec();
      return result > 0;
    } catch (error) {
      logger.error('Error checking document existence', {
        filter,
        model: this.model.modelName,
        error: error instanceof Error ? error.message : String(error),
      });
      throw error;
    }
  }

  /**
   * Count documents by filter
   * @param filter Query filter
   * @returns Promise resolving to the count of documents
   */
  public async count(filter: Record<string, any> = {}): Promise<number> {
    try {
      return await this.getModel().countDocuments(filter).exec();
    } catch (error) {
      logger.error('Error counting documents', {
        filter,
        model: this.model.modelName,
        error: error instanceof Error ? error.message : String(error),
      });
      throw error;
    }
  }
}
