import { BaseModel, Inject, Injectable } from '@c-cam/core';
import { CameraDocument } from '@/database/entities/CameraModel';
import CameraRepository from '@/repositories/CameraRepository';

/**
 * Service for managing cameras
 * Extends the BaseModel with CameraDocument type
 */
@Injectable()
class CameraService extends BaseModel<CameraDocument> {
  /**
   * Create a new CameraService
   * @param tenantId Optional tenant ID for multi-tenant operations
   */
  constructor(@Inject(CameraRepository) repository: CameraRepository) {
    super(repository);
  }

  /**
   * Create a new camera
   * @param cameraData The camera data
   * @returns The newly created camera
   */
  async createCamera(cameraData: {
    name: string;
    type: string;
    ip_address: string;
    location: string;
    status: string;
    created_by: string;
  }): Promise<CameraDocument> {
    // Validate IP address format
    if (!this.isValidIpAddress(cameraData.ip_address)) {
      throw new Error('Invalid IP address format');
    }

    // Check if a camera with the same IP address already exists
    const existingCamera = await (
      this.repository as CameraRepository
    ).findByIpAddress(cameraData.ip_address);

    if (existingCamera) {
      throw new Error(
        `Camera with IP address '${cameraData.ip_address}' already exists`,
      );
    }

    // Create the new camera
    return this.create(cameraData);
  }

  /**
   * Update a camera
   * @param id The camera ID
   * @param cameraData The data to update
   * @returns True if the camera was updated, false otherwise
   */
  async updateCamera(
    id: string,
    cameraData: Partial<{
      name: string;
      type: string;
      ip_address: string;
      location: string;
      status: string;
    }>,
  ): Promise<boolean> {
    // Check if the camera exists
    const camera = await this.findById(id);
    if (!camera) {
      throw new Error(`Camera with ID '${id}' not found`);
    }

    // Validate IP address format if provided
    if (
      cameraData.ip_address &&
      !this.isValidIpAddress(cameraData.ip_address)
    ) {
      throw new Error('Invalid IP address format');
    }

    // Check for unique constraints
    if (cameraData.ip_address && cameraData.ip_address !== camera.ip_address) {
      const existingCamera = await (
        this.repository as CameraRepository
      ).findByIpAddress(cameraData.ip_address);

      if (existingCamera && existingCamera.id !== id) {
        throw new Error(
          `Camera with IP address '${cameraData.ip_address}' already exists`,
        );
      }
    }

    // Update the camera
    return this.update(id, cameraData);
  }

  /**
   * Find cameras by name
   * @param name The camera name
   * @returns An array of cameras
   */
  async findByName(name: string): Promise<CameraDocument[]> {
    return (this.repository as CameraRepository).findByName(name);
  }

  /**
   * Find a camera by IP address
   * @param ipAddress The IP address
   * @returns The camera or null if not found
   */
  async findByIpAddress(ipAddress: string): Promise<CameraDocument | null> {
    return (this.repository as CameraRepository).findByIpAddress(ipAddress);
  }

  /**
   * Find cameras by type
   * @param type The camera type
   * @returns An array of cameras
   */
  async findByType(type: string): Promise<CameraDocument[]> {
    return (this.repository as CameraRepository).findByType(type);
  }

  /**
   * Find cameras by status
   * @param status The camera status
   * @returns An array of cameras
   */
  async findByStatus(status: string): Promise<CameraDocument[]> {
    return (this.repository as CameraRepository).findByStatus(status);
  }

  /**
   * Find cameras by location
   * @param location The camera location
   * @returns An array of cameras
   */
  async findByLocation(location: string): Promise<CameraDocument[]> {
    return (this.repository as CameraRepository).findByLocation(location);
  }

  /**
   * Find cameras by created by
   * @param createdBy The creator ID
   * @returns An array of cameras
   */
  async findByCreatedBy(createdBy: string): Promise<CameraDocument[]> {
    return (this.repository as CameraRepository).findByCreatedBy(createdBy);
  }

  /**
   * Validate IP address format
   * @param ipAddress The IP address to validate
   * @returns True if the IP address format is valid, false otherwise
   */
  private isValidIpAddress(ipAddress: string): boolean {
    const ipRegex = /^(\d{1,3})\.(\d{1,3})\.(\d{1,3})\.(\d{1,3})$/;
    if (!ipRegex.test(ipAddress)) {
      return false;
    }

    const parts = ipAddress.split('.').map((part) => parseInt(part, 10));
    return parts.every((part) => part >= 0 && part <= 255);
  }
}

export default CameraService;
