import { Context, Next } from 'hono';
import { cors } from 'hono/cors';
import { MiddlewareHandler } from 'hono/types';
import { IMiddleware } from '../decorators/middleware.decorator.js';

/**
 * CORS configuration options
 */
export interface CorsOptions {
  /**
   * Configures the Access-Control-Allow-Origin CORS header
   * Can be a string, RegExp, or an array of strings/RegExp
   * Default: '*'
   */
  origin?: string | RegExp | (string | RegExp)[] | boolean;

  /**
   * Configures the Access-Control-Allow-Methods CORS header
   * Default: 'GET,HEAD,PUT,PATCH,POST,DELETE'
   */
  methods?: string | string[];

  /**
   * Configures the Access-Control-Allow-Headers CORS header
   * Default: 'Content-Type, Authorization'
   */
  allowedHeaders?: string | string[];

  /**
   * Configures the Access-Control-Expose-Headers CORS header
   * Default: none
   */
  exposedHeaders?: string | string[];

  /**
   * Configures the Access-Control-Allow-Credentials CORS header
   * Default: false
   */
  credentials?: boolean;

  /**
   * Configures the Access-Control-Max-Age CORS header
   * Default: 86400 (24 hours)
   */
  maxAge?: number;

  /**
   * Whether to enable CORS preflight requests
   * Default: true
   */
  preflightContinue?: boolean;

  /**
   * Pass the CORS preflight response to the next handler
   * Default: false
   */
  optionsSuccessStatus?: number;
}

/**
 * Default CORS options
 */
const defaultCorsOptions: CorsOptions = {
  origin: '*',
  methods: 'GET,HEAD,PUT,PATCH,POST,DELETE',
  allowedHeaders: 'Content-Type, Authorization',
  credentials: false,
  maxAge: 86400, // 24 hours
  preflightContinue: false,
  optionsSuccessStatus: 204,
};

/**
 * CORS middleware implementation
 */
export class CorsMiddleware implements IMiddleware {
  private readonly options: CorsOptions;

  constructor(options: CorsOptions = {}) {
    this.options = { ...defaultCorsOptions, ...options };
  }

  /**
   * Handle CORS request
   */
  public async use(c: Context, next: Next): Promise<void> {
    // Convert our options to Hono CORS options format
    const honoOptions = {
      origin: typeof this.options.origin === 'boolean' || this.options.origin instanceof RegExp || Array.isArray(this.options.origin)
        ? this.options.origin === true ? '*' : this.options.origin === false ? '' : String(this.options.origin)
        : this.options.origin || '*',
      allowMethods: Array.isArray(this.options.methods) 
        ? this.options.methods 
        : this.options.methods?.split(',') || ['GET', 'HEAD', 'PUT', 'PATCH', 'POST', 'DELETE'],
      allowHeaders: Array.isArray(this.options.allowedHeaders) 
        ? this.options.allowedHeaders 
        : this.options.allowedHeaders?.split(',') || ['Content-Type', 'Authorization'],
      exposeHeaders: Array.isArray(this.options.exposedHeaders)
        ? this.options.exposedHeaders 
        : this.options.exposedHeaders?.split(',') || [],
      credentials: !!this.options.credentials,
      maxAge: this.options.maxAge ? Number(this.options.maxAge) : undefined,
    };
    
    // Apply the Hono CORS middleware
    const corsMiddleware = cors(honoOptions);
    await corsMiddleware(c, next);
  }
}

/**
 * Create CORS middleware with the specified options
 * @param options CORS options
 * @returns Hono middleware function
 */
export function createCorsMiddleware(
  options?: CorsOptions,
): MiddlewareHandler {
  const middleware = new CorsMiddleware(options);
  return (c, next) => middleware.use(c, next);
}
