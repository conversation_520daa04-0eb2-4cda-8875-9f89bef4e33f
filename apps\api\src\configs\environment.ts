import '@c-cam/shared';
import { MongooseOptions, RedisConnectionOptions } from '@c-cam/core';
import dotenv from 'dotenv';
import _ from 'lodash';

interface IServer {
  port: number;
}

interface IJWT {
  secret: string;
  expiresIn: string;
}

interface IBullMQConfig {
  defaultJobOptions: {
    removeOnComplete: number;
    removeOnFail: number;
    attempts: number;
    backoff: {
      type: string;
      delay: number;
    };
  };
}

interface IAdmin {
  email: string;
  password: string;
}

interface ILogging {
  level: string;
  file: string;
}

interface ICors {
  origin: string;
  methods: string[];
}

interface IRateLimit {
  windowMs: number;
  maxRequests: number;
}

interface IMonitoring {
  healthCheckInterval: number;
  metricsEnabled: boolean;
}

type EnvironmentName = 'DEVELOPMENT' | 'PRODUCTION' | 'TEST';

interface IEnvironment {
  dotenv: dotenv.DotenvConfigOutput;
  name: EnvironmentName;
  server: IServer;
  jwt: IJWT;
  mongo: MongooseOptions;
  redis: RedisConnectionOptions;
  bullmq: IBullMQConfig;
  admin: IAdmin;
  logging: ILogging;
  cors: ICors;
  rateLimit: IRateLimit;
  monitoring: IMonitoring;
}

export const environment: IEnvironment = {
  dotenv: dotenv.config(),
  name: (process.env.NODE_ENV as EnvironmentName) || 'DEVELOPMENT',
  server: {
    port: Number(get('SERVER_PORT')),
  },
  jwt: {
    secret: get('JWT_SECRET'),
    expiresIn: get('JWT_EXPIRES_IN'),
  },
  mongo: {
    uri: get('MONGO_CONNECTION_TEMPLATE').format({
      protocol: get('MONGO_PROTOCOL'),
      username: get('MONGO_USERNAME'),
      password: get('MONGO_PASSWORD'),
      host: get('MONGO_HOST'),
      port: get('MONGO_PORT'),
      database: get('MONGO_DATABASE_NAME'),
      retryWrites: get('MONGO_RETRY_WRITES').toBoolean(),
    }),
    options: {
      autoIndex: true,
      serverSelectionTimeoutMS: 5000,
    },
  },
  redis: {
    mode: 'single',
    singleOptions: {
      uri: get('REDIS_URL') || undefined,
      host: get('REDIS_HOST') || undefined,
      port: Number(get('REDIS_PORT')) || undefined,
      password: get('REDIS_PASSWORD') || undefined,
      db: Number(get('REDIS_DB')) || undefined,
      maxRetriesPerRequest: get('REDIS_MAX_RETRIES_PER_REQUEST')
        ? Number(get('REDIS_MAX_RETRIES_PER_REQUEST'))
        : undefined,
    },
  },
  bullmq: {
    defaultJobOptions: {
      removeOnComplete:
        Number(get('BULLMQ_DEFAULT_JOB_OPTIONS_REMOVE_ON_COMPLETE')) || 10,
      removeOnFail:
        Number(get('BULLMQ_DEFAULT_JOB_OPTIONS_REMOVE_ON_FAIL')) || 50,
      attempts: Number(get('BULLMQ_DEFAULT_JOB_OPTIONS_ATTEMPTS')) || 3,
      backoff: {
        type: get('BULLMQ_DEFAULT_JOB_OPTIONS_BACKOFF_TYPE') || 'exponential',
        delay: Number(get('BULLMQ_DEFAULT_JOB_OPTIONS_BACKOFF_DELAY')) || 2000,
      },
    },
  },
  admin: {
    email: get('ADMIN_EMAIL') || '<EMAIL>',
    password: get('ADMIN_PASSWORD') || 'admin123',
  },
  logging: {
    level: get('LOG_LEVEL') || 'info',
    file: get('LOG_FILE') || './logs/app.log',
  },
  cors: {
    origin: get('CORS_ORIGIN') || 'http://localhost:3000,http://localhost:5173',
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  },
  rateLimit: {
    windowMs: Number(get('RATE_LIMIT_WINDOW_MS')) || 900000,
    maxRequests: Number(get('RATE_LIMIT_MAX_REQUESTS')) || 100,
  },
  monitoring: {
    healthCheckInterval: Number(get('HEALTH_CHECK_INTERVAL')) || 30000,
    metricsEnabled: get('METRICS_ENABLED') === 'true',
  },
};

function get(key: string) {
  if (!process.env.NODE_ENV) {
    throw new Error('NODE_ENV is not defined');
  }
  const name = [process.env.NODE_ENV, key].join('_');
  return _.get(process.env, name, '');
}
