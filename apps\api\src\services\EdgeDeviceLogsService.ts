import { BaseModel, Inject, Injectable } from '@c-cam/core';
import { EdgeDeviceLogsDocument } from '@/database/entities/EdgeDeviceLogsModel';
import EdgeDeviceLogsRepository from '@/repositories/EdgeDeviceLogsRepository';

/**
 * Service for managing edge device logs
 * Extends the BaseModel with EdgeDeviceLogsDocument type
 */
@Injectable()
class EdgeDeviceLogsService extends BaseModel<EdgeDeviceLogsDocument> {
  constructor(
    @Inject(EdgeDeviceLogsRepository)
    repository: EdgeDeviceLogsRepository,
  ) {
    super(repository);
  }

  /**
   * Find logs by edge device ID
   * @param edgeDeviceId The edge device ID to search for
   * @returns A promise that resolves to an array of edge device logs
   */
  async findByEdgeDeviceId(
    edgeDeviceId: string,
  ): Promise<EdgeDeviceLogsDocument[]> {
    return (this.repository as EdgeDeviceLogsRepository).findByEdgeDeviceId(
      edgeDeviceId,
    );
  }

  /**
   * Find logs by log level
   * @param logLevel The log level to search for
   * @returns A promise that resolves to an array of edge device logs
   */
  async findByLogLevel(logLevel: string): Promise<EdgeDeviceLogsDocument[]> {
    return (this.repository as EdgeDeviceLogsRepository).findByLogLevel(
      logLevel,
    );
  }

  /**
   * Find logs by date range
   * @param startDate The start date of the range
   * @param endDate The end date of the range
   * @returns A promise that resolves to an array of edge device logs
   */
  async findByDateRange(
    startDate: Date,
    endDate: Date,
  ): Promise<EdgeDeviceLogsDocument[]> {
    return (this.repository as EdgeDeviceLogsRepository).findByDateRange(
      startDate,
      endDate,
    );
  }

  /**
   * Find logs by edge device ID and date range
   * @param edgeDeviceId The edge device ID to search for
   * @param startDate The start date of the range
   * @param endDate The end date of the range
   * @returns A promise that resolves to an array of edge device logs
   */
  async findByEdgeDeviceIdAndDateRange(
    edgeDeviceId: string,
    startDate: Date,
    endDate: Date,
  ): Promise<EdgeDeviceLogsDocument[]> {
    return (
      this.repository as EdgeDeviceLogsRepository
    ).findByEdgeDeviceIdAndDateRange(edgeDeviceId, startDate, endDate);
  }

  /**
   * Find logs by edge device ID and log level
   * @param edgeDeviceId The edge device ID to search for
   * @param logLevel The log level to search for
   * @returns A promise that resolves to an array of edge device logs
   */
  async findByEdgeDeviceIdAndLogLevel(
    edgeDeviceId: string,
    logLevel: string,
  ): Promise<EdgeDeviceLogsDocument[]> {
    return (
      this.repository as EdgeDeviceLogsRepository
    ).findByEdgeDeviceIdAndLogLevel(edgeDeviceId, logLevel);
  }

  /**
   * Delete logs by edge device ID
   * @param edgeDeviceId The edge device ID to delete logs for
   * @returns A promise that resolves to true if any logs were deleted, false otherwise
   */
  async deleteByEdgeDeviceId(edgeDeviceId: string): Promise<boolean> {
    return (this.repository as EdgeDeviceLogsRepository).deleteByEdgeDeviceId(
      edgeDeviceId,
    );
  }

  /**
   * Create a new edge device log
   * @param logData The log data
   * @returns The newly created log
   */
  async createLog(logData: {
    edge_device_id: string;
    log_level: string;
    message: string;
    component?: string;
    details?: Record<string, unknown>;
    created_by: string;
  }): Promise<EdgeDeviceLogsDocument> {
    // Validate required fields
    if (!logData.edge_device_id) {
      throw new Error('Edge device ID is required');
    }

    if (!logData.log_level) {
      throw new Error('Log level is required');
    }

    if (!logData.message) {
      throw new Error('Message is required');
    }

    // Create the new log
    return this.create(logData);
  }
}

export default EdgeDeviceLogsService;
