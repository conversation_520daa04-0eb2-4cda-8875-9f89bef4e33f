/**
 * Shift Detail Attributes Interface
 * Defines the core data structure for shift details
 */
export interface ShiftDetailAttributes {
  id: string;
  shift_id: string;
  is_overnight: boolean;
  check_in_start_time: string;
  late_threshold_time: string;
  half_day_missed_start_time: string;
  break_time: string;
  check_out_start_time: string;
  early_leave_threshold_time: string;
  half_day_missed_end_time: string;
  total_working_hours: number;
  check_in_required: boolean;
  flex_late_threshold_time: string;
  flex_half_day_missed_time: string;
}
