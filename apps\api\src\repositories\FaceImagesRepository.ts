import FaceImagesModel, {
  FaceImagesDocument,
} from '@/database/entities/FaceImagesModel';
import { Repository, Injectable } from '@c-cam/core';

/**
 * Repository for managing face images
 * Extends the BaseRepository with FaceImagesDocument type
 */
@Injectable()
class FaceImagesRepository extends Repository<FaceImagesDocument> {
  constructor() {
    super(FaceImagesModel);
  }

  /**
   * Find face images by user ID
   * @param userId The user ID to search for
   * @returns A promise that resolves to an array of face images
   */
  async findByUserId(userId: string): Promise<FaceImagesDocument[]> {
    return this.find({ user_id: userId });
  }

  /**
   * Find face images by image type
   * @param imageType The image type to search for
   * @returns A promise that resolves to an array of face images
   */
  async findByImageType(imageType: string): Promise<FaceImagesDocument[]> {
    return this.find({ image_type: imageType });
  }

  /**
   * Find face images by date range
   * @param startDate The start date of the range
   * @param endDate The end date of the range
   * @returns A promise that resolves to an array of face images
   */
  async findByDateRange(
    startDate: Date,
    endDate: Date,
  ): Promise<FaceImagesDocument[]> {
    return this.find({
      created_at: { $gte: startDate, $lte: endDate },
    });
  }

  /**
   * Find face images by user ID and image type
   * @param userId The user ID to search for
   * @param imageType The image type to search for
   * @returns A promise that resolves to an array of face images
   */
  async findByUserIdAndImageType(
    userId: string,
    imageType: string,
  ): Promise<FaceImagesDocument[]> {
    return this.find({
      user_id: userId,
      image_type: imageType,
    });
  }

  /**
   * Delete face images by user ID
   * @param userId The user ID to delete face images for
   * @returns A promise that resolves to true if any face images were deleted, false otherwise
   */
  async deleteByUserId(userId: string): Promise<boolean> {
    const result = await this.getModel().deleteMany({ user_id: userId }).exec();
    return result.deletedCount > 0;
  }
}

export default FaceImagesRepository;
