import * as mqtt from 'mqtt';
import { logger } from '@c-cam/logger';

/**
 * MQTT connection health status
 */
export interface MqttHealthStatus {
  isConnected: boolean;
  status:
    | 'connected'
    | 'connecting'
    | 'reconnecting'
    | 'disconnected'
    | 'offline'
    | 'error';
  uptime?: number;
  lastError?: string;
  defaultClient: {
    connected: boolean;
    clientId?: string;
    error?: string;
  };
  multiTenant?: {
    enabled: boolean;
    tenants: Array<{
      id: string;
      connected: boolean;
      clientId?: string;
      error?: string;
    }>;
  };
}

/**
 * MQTT connection options
 */
export interface MqttConnectionOptions {
  /**
   * MQTT broker URL (e.g., mqtt://localhost:1883)
   */
  url: string;

  /**
   * Client ID for the MQTT connection
   * If not provided, a random client ID will be generated
   */
  clientId?: string;

  /**
   * Username for authentication
   */
  username?: string;

  /**
   * Password for authentication
   */
  password?: string;

  /**
   * Whether to use a clean session
   * @default true
   */
  clean?: boolean;

  /**
   * Keep alive interval in seconds
   * @default 60
   */
  keepalive?: number;

  /**
   * Whether to automatically reconnect
   * @default true
   */
  reconnectPeriod?: number;

  /**
   * Connect timeout in milliseconds
   * @default 30000
   */
  connectTimeout?: number;

  /**
   * Whether to use TLS
   * @default false
   */
  rejectUnauthorized?: boolean;

  /**
   * QoS level for subscriptions
   * @default 0
   */
  qos?: 0 | 1 | 2;

  /**
   * Whether to retain messages
   * @default false
   */
  retain?: boolean;

  /**
   * Will message configuration
   */
  will?: {
    /**
     * Topic for the will message
     */
    topic: string;

    /**
     * Payload for the will message
     */
    payload: string;

    /**
     * QoS level for the will message
     * @default 0
     */
    qos?: 0 | 1 | 2;

    /**
     * Whether to retain the will message
     * @default false
     */
    retain?: boolean;
  };

  /**
   * Health check configuration
   */
  healthCheck?: {
    /**
     * Enable periodic health checks
     */
    enabled?: boolean;
    /**
     * Health check interval in milliseconds
     */
    interval?: number;
    /**
     * Timeout for health check operations in milliseconds
     */
    timeout?: number;
  };

  /**
   * Error handling configuration
   */
  errorHandling?: {
    /**
     * Whether to throw errors or log and continue
     */
    throwOnError?: boolean;
    /**
     * Maximum retry attempts for operations
     */
    maxRetries?: number;
    /**
     * Retry delay in milliseconds
     */
    retryDelay?: number;
  };
}

/**
 * MQTT message handler function
 */
export type MqttMessageHandler = (
  topic: string,
  message: Buffer,
  packet: mqtt.IPublishPacket,
) => void;

/**
 * MQTT connection manager
 */
export class MqttConnection {
  private static _instance: MqttConnection;
  private _defaultClient: mqtt.MqttClient | null = null;
  private _options: MqttConnectionOptions | null = null;
  private _messageHandlers: Map<string, Set<MqttMessageHandler>> = new Map();
  private _connectionStartTime: number | null = null;
  private _lastError: string | null = null;
  private _clientErrors: Map<string, string> = new Map();

  /**
   * Get the singleton instance
   */
  public static getInstance(): MqttConnection {
    if (!MqttConnection._instance) {
      MqttConnection._instance = new MqttConnection();
    }
    return MqttConnection._instance;
  }

  /**
   * Get the current connection options
   * @returns The connection options or null if not initialized
   */
  public getOptions(): MqttConnectionOptions | null {
    return this._options;
  }

  /**
   * Check if MQTT is connected and ready
   * @returns True if connected and ready, false otherwise
   */
  public isConnected(): boolean {
    return !!(this._defaultClient && this._defaultClient.connected);
  }

  /**
   * Get detailed health status of the MQTT connection
   * @returns Health status information
   */
  public getHealthStatus(): MqttHealthStatus {
    if (!this._defaultClient || !this._options) {
      return {
        isConnected: false,
        status: 'disconnected',
        defaultClient: {
          connected: false,
        },
      };
    }

    const uptime = this._connectionStartTime
      ? Date.now() - this._connectionStartTime
      : undefined;

    const baseStatus: MqttHealthStatus = {
      isConnected: this.isConnected(),
      status: this._getConnectionStatus(),
      uptime,
      lastError: this._lastError || undefined,
      defaultClient: {
        connected: this._defaultClient.connected,
        clientId: this._defaultClient.options.clientId,
        error: this._clientErrors.get('default'),
      },
    };

    return baseStatus;
  }

  /**
   * Get the current connection status
   * @private
   */
  private _getConnectionStatus(): MqttHealthStatus['status'] {
    if (!this._defaultClient) {
      return 'disconnected';
    }

    if (this._defaultClient.connected) {
      return 'connected';
    }

    if (this._defaultClient.reconnecting) {
      return 'reconnecting';
    }

    return 'disconnected';
  }

  /**
   * Perform a health check on the MQTT connection
   * @returns Promise resolving to true if healthy, false otherwise
   */
  public async healthCheck(): Promise<boolean> {
    try {
      if (!this._defaultClient || !this._defaultClient.connected) {
        return false;
      }

      // MQTT doesn't have a built-in ping, so we check the connection state
      // and optionally publish to a health check topic
      return this._defaultClient.connected;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      this._lastError = errorMessage;
      this._clientErrors.set('default', errorMessage);
      logger.warn('MQTT health check failed', { error: errorMessage });
      return false;
    }
  }

  /**
   * Initialize the MQTT connection(s)
   * @param options Connection options
   */
  public async initialize(
    options: MqttConnectionOptions,
  ): Promise<mqtt.MqttClient> {
    if (this._defaultClient) {
      logger.warn('MQTT connection already initialized');
      return this._defaultClient;
    }

    this._options = options;

    try {
      // Create the default client
      const clientId =
        options.clientId ||
        `mqtt-client-${Math.random().toString(16).substring(2, 10)}`;
      this._defaultClient = mqtt.connect(options.url, {
        ...options,
        clientId,
      });

      // Set up event listeners
      this._setupClientEventListeners(this._defaultClient, 'default');

      logger.info('MQTT connected');

      return this._defaultClient;
    } catch (error) {
      logger.error('Failed to connect to MQTT broker', { error });
      throw error;
    }
  }

  /**
   * Set up event listeners for a client
   * @param client The client to set up listeners for
   * @param clientName The client name
   * @param tenantId Optional tenant ID
   */
  private _setupClientEventListeners(
    client: mqtt.MqttClient,
    clientName: string,
    tenantId?: string,
  ): void {
    const clientInfo = tenantId
      ? `tenant ${tenantId} (${clientName})`
      : clientName;

    client.on('connect', () => {
      logger.info(`MQTT connected: ${clientInfo}`);
    });

    client.on('reconnect', () => {
      logger.info(`MQTT reconnecting: ${clientInfo}`);
    });

    client.on('close', () => {
      logger.info(`MQTT connection closed: ${clientInfo}`);
    });

    client.on('disconnect', () => {
      logger.info(`MQTT disconnected: ${clientInfo}`);
    });

    client.on('offline', () => {
      logger.info(`MQTT offline: ${clientInfo}`);
    });

    client.on('error', (error) => {
      logger.error(`MQTT error for ${clientInfo}`, { error });
    });

    client.on('message', (topic, message, packet) => {
      this._handleMessage(topic, message, packet);
    });
  }

  /**
   * Handle a message for the default client
   * @param topic The message topic
   * @param message The message payload
   * @param packet The MQTT packet
   */
  private _handleMessage(
    topic: string,
    message: Buffer,
    packet: mqtt.IPublishPacket,
  ): void {
    const handlers = this._messageHandlers.get(topic);
    if (handlers) {
      for (const handler of handlers) {
        try {
          handler(topic, message, packet);
        } catch (error) {
          logger.error('Error in MQTT message handler', { topic, error });
        }
      }
    }

    // Also check for wildcard handlers
    for (const [handlerTopic, handlers] of this._messageHandlers.entries()) {
      if (
        handlerTopic !== topic &&
        this._topicMatchesWildcard(topic, handlerTopic)
      ) {
        for (const handler of handlers) {
          try {
            handler(topic, message, packet);
          } catch (error) {
            logger.error('Error in MQTT wildcard message handler', {
              topic,
              handlerTopic,
              error,
            });
          }
        }
      }
    }
  }

  /**
   * Check if a topic matches a wildcard pattern
   * @param topic The topic to check
   * @param wildcardPattern The wildcard pattern
   * @returns True if the topic matches the wildcard pattern
   */
  private _topicMatchesWildcard(
    topic: string,
    wildcardPattern: string,
  ): boolean {
    // Convert MQTT wildcards to regex
    const regex = new RegExp(
      '^' +
        wildcardPattern
          .replace(/\+/g, '[^/]+') // + matches a single level
          .replace(/#/g, '.*') + // # matches multiple levels
        '$',
    );
    return regex.test(topic);
  }

  /**
   * Get the default MQTT client
   */
  public getClient(): mqtt.MqttClient {
    if (!this._defaultClient) {
      throw new Error(
        'MQTT connection not initialized. Call initialize() first.',
      );
    }
    return this._defaultClient;
  }

  /**
   * Subscribe to a topic
   * @param topic The topic to subscribe to
   * @param options Subscription options
   */
  public async subscribe(
    topic: string,
    options?: mqtt.IClientSubscribeOptions,
  ): Promise<void> {
    const client = this.getClient();

    // Ensure QoS is a valid value (0, 1, or 2)
    const qos = this._options?.qos ?? 0;
    const subscribeOptions = options || { qos };

    return new Promise((resolve, reject) => {
      client.subscribe(topic, subscribeOptions, (err) => {
        if (err) {
          logger.error('Error subscribing to MQTT topic', {
            topic,
            error: err,
          });
          reject(err);
        } else {
          logger.info(`Subscribed to MQTT topic: ${topic}`);
          resolve();
        }
      });
    });
  }

  /**
   * Unsubscribe from a topic
   * @param topic The topic to unsubscribe from
   */
  public async unsubscribe(topic: string): Promise<void> {
    const client = this.getClient();
    return new Promise((resolve, reject) => {
      client.unsubscribe(topic, (err) => {
        if (err) {
          logger.error('Error unsubscribing from MQTT topic', {
            topic,
            error: err,
          });
          reject(err);
        } else {
          logger.info(`Unsubscribed from MQTT topic: ${topic}`);
          resolve();
        }
      });
    });
  }

  /**
   * Register a message handler for a topic
   * @param topic The topic to handle messages for
   * @param handler The message handler function
   */
  public onMessage(topic: string, handler: MqttMessageHandler): void {
    let handlers = this._messageHandlers.get(topic);
    if (!handlers) {
      handlers = new Set();
      this._messageHandlers.set(topic, handlers);
    }
    handlers.add(handler);
  }

  /**
   * Remove a message handler for a topic
   * @param topic The topic to remove the handler for
   * @param handler The message handler function to remove
   */
  public offMessage(topic: string, handler: MqttMessageHandler): void {
    const handlers = this._messageHandlers.get(topic);
    if (handlers) {
      handlers.delete(handler);
      if (handlers.size === 0) {
        this._messageHandlers.delete(topic);
      }
    }
  }

  /**
   * Publish a message to a topic
   * @param topic The topic to publish to
   * @param message The message to publish
   * @param options Publish options
   */
  public async publish(
    topic: string,
    message: string | Buffer,
    options?: mqtt.IClientPublishOptions,
  ): Promise<void> {
    const client = this.getClient();

    // Ensure QoS is a valid value (0, 1, or 2) and retain is a boolean
    const qos = this._options?.qos ?? 0;
    const retain = this._options?.retain ?? false;
    const publishOptions = options || { qos, retain };

    return new Promise((resolve, reject) => {
      client.publish(topic, message, publishOptions, (err) => {
        if (err) {
          logger.error('Error publishing MQTT message', { topic, error: err });
          reject(err);
        } else {
          logger.debug(`Published MQTT message to topic: ${topic}`);
          resolve();
        }
      });
    });
  }

  /**
   * Close all MQTT connections
   */
  public async close(): Promise<void> {
    // Close the default client
    if (this._defaultClient) {
      await new Promise<void>((resolve) => {
        this._defaultClient?.end(true, {}, () => {
          logger.info('Closed default MQTT connection');
          resolve();
        });
      });
      this._defaultClient = null;
    }
    this._messageHandlers.clear();

    logger.info('All MQTT connections closed');
  }
}
