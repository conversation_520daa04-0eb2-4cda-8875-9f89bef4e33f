{"name": "c-cam", "private": true, "scripts": {"build": "turbo run build", "build:api": "turbo run build --filter @c-cam/api", "build:web": "turbo run build --filter @c-cam/web", "build:core": "turbo run build --filter @c-cam/core", "build:types": "turbo run build --filter @c-cam/types", "dev": "turbo run dev", "dev:api": "turbo run dev --filter @c-cam/api", "dev:web": "turbo run dev --filter @c-cam/web", "lint": "turbo run lint", "format": "prettier --write \"**/*.{ts,tsx,md}\"", "check-types": "turbo run check-types", "clean": "rimraf dist node_modules pnpm-lock.yaml"}, "devDependencies": {"prettier": "^3.5.3", "rimraf": "^6.0.1", "turbo": "^2.5.3", "typescript": "5.8.2"}, "packageManager": "pnpm@9.0.0", "engines": {"node": ">=18"}, "dependencies": {"logger": "^0.0.1"}}