import { useApiQuery } from '@/shared/hooks/use-api-query'

// Types for role data
export interface Role {
  id: string
  name: string
  description?: string
  member_role_id?: string
  permission_id?: string
  created_at?: string
  updated_at?: string
  created_by?: string
}

export interface MemberRole {
  id: string
  user_id: string
  role_id: string
  assigned_by?: string
  assigned_at?: string
}

export interface RoleQueryParams {
  limit?: number
  skip?: number
  sortBy?: string
  sortDirection?: 'asc' | 'desc'
}

export interface RoleListResponse {
  roles: Array<Role>
}

export interface RoleResponse {
  role: Role
}

export interface MemberRoleListResponse {
  memberRoles: Array<MemberRole>
}

/**
 * Hook to fetch all roles with optional pagination and sorting
 */
export const useRolesQuery = (params?: RoleQueryParams) => {
  return useApiQuery<RoleListResponse>(
    ['roles', ...(params ? [JSON.stringify(params)] : [])],
    '/api/roles',
    params
  )
}

/**
 * Hook to fetch a single role by ID
 */
export const useRoleQuery = (id: string, enabled = true) => {
  return useApiQuery<RoleResponse>(
    ['roles', id],
    `/api/roles/${id}`,
    undefined,
    { enabled: enabled && !!id }
  )
}

/**
 * Hook to fetch role by name
 */
export const useRoleByNameQuery = (name: string, enabled = true) => {
  return useApiQuery<RoleResponse>(
    ['roles', 'name', name],
    `/api/roles/name/${name}`,
    undefined,
    { enabled: enabled && !!name }
  )
}

/**
 * Hook to fetch roles by member role ID
 */
export const useRolesByMemberRoleQuery = (memberRoleId: string, enabled = true) => {
  return useApiQuery<RoleListResponse>(
    ['roles', 'member-role', memberRoleId],
    `/api/roles/member-role/${memberRoleId}`,
    undefined,
    { enabled: enabled && !!memberRoleId }
  )
}

/**
 * Hook to fetch roles by permission ID
 */
export const useRolesByPermissionQuery = (permissionId: string, enabled = true) => {
  return useApiQuery<RoleListResponse>(
    ['roles', 'permission', permissionId],
    `/api/roles/permission/${permissionId}`,
    undefined,
    { enabled: enabled && !!permissionId }
  )
}

/**
 * Hook to fetch roles created by a specific user
 */
export const useRolesByCreatorQuery = (createdBy: string, enabled = true) => {
  return useApiQuery<RoleListResponse>(
    ['roles', 'created-by', createdBy],
    `/api/roles/created-by/${createdBy}`,
    undefined,
    { enabled: enabled && !!createdBy }
  )
}

// Member Role related queries

/**
 * Hook to fetch all member roles with optional pagination and sorting
 */
export const useMemberRolesQuery = (params?: RoleQueryParams) => {
  return useApiQuery<MemberRoleListResponse>(
    ['member-roles', ...(params ? [JSON.stringify(params)] : [])],
    '/api/member-roles',
    params
  )
}

/**
 * Hook to fetch roles for a specific user
 */
export const useUserRolesQuery = (userId: string, enabled = true) => {
  return useApiQuery<MemberRoleListResponse>(
    ['member-roles', 'user', userId],
    `/api/member-roles/user/${userId}`,
    undefined,
    { enabled: enabled && !!userId }
  )
}

/**
 * Hook to fetch users with a specific role
 */
export const useUsersWithRoleQuery = (roleId: string, enabled = true) => {
  return useApiQuery<MemberRoleListResponse>(
    ['member-roles', 'role', roleId],
    `/api/member-roles/role/${roleId}`,
    undefined,
    { enabled: enabled && !!roleId }
  )
}
