import { Context } from 'hono';
import {
  Controller,
  ControllerBase,
  HttpContext,
  HttpGet,
  HttpPost,
  HttpDelete,
  Inject,
  Param,
  Query,
  UnauthorizedError,
} from '@c-cam/core';
import PermissionService from '../services/PermissionService';

@Controller('/api/permissions')
export class PermissionController extends ControllerBase {
  constructor(
    @Inject(PermissionService) private permissionService: PermissionService,
  ) {
    super();
  }

  /**
   * Helper method to get authenticated user ID
   */
  private getAuthenticatedUserId(c: Context): string {
    const userId = c.get('userId');
    if (!userId) {
      throw new UnauthorizedError('You must be authenticated to access this resource');
    }
    return userId;
  }

  /**
   * Get all permissions
   */
  @HttpGet('/')
  async getPermissions(
    @HttpContext() c: Context,
    @Query('limit') limit?: string,
    @Query('skip') skip?: string,
    @Query('sortBy') sortBy?: string,
    @Query('sortDirection') sortDirection?: string,
  ): Promise<Response> {
    this.getAuthenticatedUserId(c);

    const permissions = await this.permissionService.find({
      limit: limit ? parseInt(limit) : undefined,
      skip: skip ? parseInt(skip) : undefined,
      sortBy,
      sortDirection,
    });

    return this.success(c, { permissions });
  }

  /**
   * Get a permission by ID
   */
  @HttpGet('/:id')
  async getPermissionById(
    @HttpContext() c: Context,
    @Param('id') id: string,
  ): Promise<Response> {
    this.getAuthenticatedUserId(c);
    this.validateIf(!id, 'Permission ID is required');

    const permission = await this.permissionService.findById(id);
    this.notFoundIf(!permission, 'Permission not found');

    return this.success(c, { permission });
  }

  /**
   * Create a new permission
   */
  @HttpPost('/')
  async createPermission(@HttpContext() c: Context): Promise<Response> {
    const userId = this.getAuthenticatedUserId(c);
    const { roleId, module, feature, action } = await c.req.json();

    // Validate required fields
    this.validateRequiredFields(
      { roleId, module, feature, action },
      ['roleId', 'module', 'feature', 'action']
    );

    const permission = await this.permissionService.createPermission(
      roleId,
      module,
      feature,
      action,
      userId
    );

    return this.created(c, { permission }, 'Permission created successfully');
  }

  /**
   * Delete a permission
   */
  @HttpDelete('/:id')
  async deletePermission(
    @HttpContext() c: Context,
    @Param('id') id: string,
  ): Promise<Response> {
    this.getAuthenticatedUserId(c);
    this.validateIf(!id, 'Permission ID is required');

    const success = await this.permissionService.delete(id);
    this.validateIf(!success, 'Failed to delete permission');

    return this.success(c, { success: true }, 'Permission deleted successfully');
  }

  /**
   * Check if a role has a specific permission
   */
  @HttpGet('/check/:roleId/:module/:feature/:action')
  async checkPermission(
    @HttpContext() c: Context,
    @Param('roleId') roleId: string,
    @Param('module') module: string,
    @Param('feature') feature: string,
    @Param('action') action: string,
  ): Promise<Response> {
    this.getAuthenticatedUserId(c);

    // Validate all required parameters
    this.validateIf(!roleId || !module || !feature || !action, 'All permission check parameters are required');

    const hasPermission = await this.permissionService.hasPermission(
      roleId,
      module,
      feature,
      action
    );

    return this.success(c, { hasPermission });
  }

  /**
   * Get all permissions for a role
   */
  @HttpGet('/role/:roleId')
  async getPermissionsForRole(
    @HttpContext() c: Context,
    @Param('roleId') roleId: string,
  ): Promise<Response> {
    this.getAuthenticatedUserId(c);
    this.validateIf(!roleId, 'Role ID is required');

    const permissions = await this.permissionService.getPermissionsForRole(roleId);
    return this.success(c, { permissions });
  }

  /**
   * Get all permissions for a module
   */
  @HttpGet('/module/:module')
  async getPermissionsForModule(
    @HttpContext() c: Context,
    @Param('module') module: string,
  ): Promise<Response> {
    this.getAuthenticatedUserId(c);
    this.validateIf(!module, 'Module name is required');

    const permissions = await this.permissionService.getPermissionsForModule(module);
    return this.success(c, { permissions });
  }

  /**
   * Get all permissions for a feature
   */
  @HttpGet('/feature/:feature')
  async getPermissionsForFeature(
    @HttpContext() c: Context,
    @Param('feature') feature: string,
  ): Promise<Response> {
    this.getAuthenticatedUserId(c);
    this.validateIf(!feature, 'Feature name is required');

    const permissions = await this.permissionService.getPermissionsForFeature(feature);
    return this.success(c, { permissions });
  }

  /**
   * Get all permissions for an action
   */
  @HttpGet('/action/:action')
  async getPermissionsForAction(
    @HttpContext() c: Context,
    @Param('action') action: string,
  ): Promise<Response> {
    this.getAuthenticatedUserId(c);
    this.validateIf(!action, 'Action name is required');

    const permissions = await this.permissionService.getPermissionsForAction(action);
    return this.success(c, { permissions });
  }

  /**
   * Remove all permissions for a role
   */
  @HttpDelete('/role/:roleId')
  async removeAllPermissionsForRole(
    @HttpContext() c: Context,
    @Param('roleId') roleId: string,
  ): Promise<Response> {
    this.getAuthenticatedUserId(c);
    this.validateIf(!roleId, 'Role ID is required');

    const success = await this.permissionService.removeAllPermissionsForRole(roleId);
    this.validateIf(!success, 'Failed to remove permissions');

    return this.success(c, { success: true }, 'All permissions removed for role successfully');
  }
}
