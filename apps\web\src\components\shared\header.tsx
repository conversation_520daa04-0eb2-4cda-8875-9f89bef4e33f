import { GripIcon } from 'lucide-react'
import { Separator } from '../ui/separator'
import { Combobox } from '../override/combobox'
import { Notification } from '../override/notification'
import { UserBox } from '../override/userbox'
import { Logo } from './logo'

export function HeaderLayout() {
  return (
    <>
      <div className="flex justify-between items-center w-full h-[60px] pr-6 bg-white border-t-0 border-r-0 border-b-[1.5px] border-l-0 border-[#e9eaf2]">
        <div className="flex justify-start items-center self-stretch flex-grow-0 flex-shrink-0 relative gap-4 px-6">
          <GripIcon />
          <div className="flex justify-start items-center flex-grow-0 flex-shrink-0 relative gap-2">
            <Logo />
          </div>
          <Separator orientation="vertical" className="h-0.5" />
          <Combobox />
        </div>
        <div className="flex justify-end items-center flex-grow-0 flex-shrink-0 gap-4">
          <Notification />
          <UserBox />
        </div>
      </div>
    </>
  )
}

export default HeaderLayout
