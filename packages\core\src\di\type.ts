import { Context, Next } from 'hono';
import { IHttpContext } from '../hosting/interfaces';
import { Middleware } from '../shared/types';

// Core interfaces for dependency injection
export interface IServiceCollection {
  AddSingleton(serviceType: any, implementationType?: any): IServiceCollection;
  AddScoped(serviceType: any, implementationType?: any): IServiceCollection;
  AddTransient(serviceType: any, implementationType?: any): IServiceCollection;
  BuildServiceProvider(): IServiceProvider;
}

export interface IServiceProvider {
  GetService<T>(serviceType: any): T;
  CreateScope(): IServiceScope;
}

export interface IServiceScope {
  serviceProvider: IServiceProvider;
  dispose(): void;
}

// Service lifetime types
export enum ServiceLifetime {
  Singleton,
  Scoped,
  Transient,
}

// Type definitions for dependency injection
export type ServiceDescriptor = {
  serviceType: any;
  implementationType: any;
  lifetime: ServiceLifetime;
};

export type Constructor<T = any> = new (...args: any[]) => T;

export type RequestDelegate = (context: IHttpContext) => Promise<void>;

// Use the shared interface for middleware functions
export type MiddlewareDIFunction = Middleware.DIFunction;

/**
 * Get the service provider from the context
 */
export function GetServiceProvider(c: Context): IServiceProvider {
  return c.get('serviceProvider');
}

/**
 * Create a new HTTP context
 */
export function CreateHttpContext(c: Context, next?: Next): IHttpContext {
  return {
    context: c,
    next,
    serviceProvider: GetServiceProvider(c),
  };
}
