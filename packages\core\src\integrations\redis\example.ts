import {
  RedisConnection,
  RedisConnectionOptions,
  RedisCache,
  ICache,
  RedisHealthStatus,
} from './index.js';

/**
 * Example of how to use the Redis distributed cache with enhanced features
 */
async function redisExample() {
  // 1. Configure and initialize the Redis connection with health monitoring
  const redisOptions: RedisConnectionOptions = {
    mode: 'single',
    singleOptions: {
      uri: 'redis://localhost:6379/0',
      // Or use individual connection parameters:
      // host: 'localhost',
      // port: 6379,
      // password: 'your-password',
      // db: 0,
    },
    // Enable health check monitoring
    healthCheck: {
      enabled: true,
      interval: 30000, // 30 seconds
      timeout: 5000, // 5 seconds
    },
  };

  // Get the Redis connection manager
  const redisConnection = RedisConnection.getInstance();

  // Initialize the connection
  await redisConnection.initialize(redisOptions);

  // Check connection health
  const healthStatus: RedisHealthStatus = redisConnection.getHealthStatus();
  console.log('Redis Health Status:', healthStatus);

  // Perform a health check
  const isHealthy = await redisConnection.healthCheck();
  console.log('Redis is healthy:', isHealthy);

  // 2. Create a cache instance with enhanced options
  const cache: ICache = new RedisCache({
    defaultTtl: 3600, // 1 hour
    keyPrefix: 'app:',
    // Custom serialization (optional)
    serialization: {
      serialize: JSON.stringify,
      deserialize: JSON.parse,
    },
    // Error handling configuration
    errorHandling: {
      throwOnError: false, // Return null/false instead of throwing
      logErrors: true, // Log errors for debugging
    },
  });

  // 3. Basic cache operations

  // Set a value in the cache
  await cache.set('user:123', { id: 123, name: 'John Doe' });

  // Get a value from the cache
  const user = await cache.get('user:123');
  console.log('User:', user);

  // Check if a key exists
  const exists = await cache.exists('user:123');
  console.log('User exists:', exists);

  // Get TTL of a key
  const ttl = await cache.ttl('user:123');
  console.log('User TTL:', ttl);

  // Set TTL for an existing key
  await cache.expire('user:123', 1800); // 30 minutes

  // Delete a value
  await cache.delete('user:123');

  // 4. Advanced cache operations

  // Set multiple values with TTL
  await cache.mset(
    {
      'product:1': { id: 1, name: 'Product 1', price: 10 },
      'product:2': { id: 2, name: 'Product 2', price: 20 },
      'product:3': { id: 3, name: 'Product 3', price: 30 },
    },
    7200,
  ); // 2 hours

  // Get multiple values
  const products = await cache.mget(['product:1', 'product:2', 'product:3']);
  console.log('Products:', products);

  // Delete multiple keys
  const deletedCount = await cache.deleteMany(['product:1', 'product:2']);
  console.log('Deleted keys:', deletedCount);

  // 5. Numeric operations
  await cache.set('counter', 0);
  await cache.increment('counter');
  await cache.increment('counter', 5);
  await cache.decrement('counter', 2);
  const counter = await cache.get('counter');
  console.log('Counter:', counter);

  // 6. Pattern matching and statistics

  // Set some test data
  await cache.mset({
    'session:user1': { userId: 1, loginTime: Date.now() },
    'session:user2': { userId: 2, loginTime: Date.now() },
    'config:theme': 'dark',
    'config:language': 'en',
  });

  // Get all session keys
  const sessionKeys = await cache.keys('session:*');
  console.log('Session keys:', sessionKeys);

  // Get all config keys
  const configKeys = await cache.keys('config:*');
  console.log('Config keys:', configKeys);

  // Get cache statistics
  const stats = await cache.getStats();
  console.log('Cache statistics:', stats);

  // 7. Connection management

  // Check if still connected
  console.log('Is connected:', redisConnection.isConnected());

  // Get detailed health status
  const finalHealthStatus = redisConnection.getHealthStatus();
  console.log('Final health status:', finalHealthStatus);

  // Clear cache (only keys with prefix)
  await cache.clear();

  // Close the connection gracefully when done
  await redisConnection.close();
}

// This is just an example and won't be executed
// redisExample().catch(console.error);

// Export the example function for documentation purposes
export { redisExample };
