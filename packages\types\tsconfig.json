{"compilerOptions": {"target": "ES2022", "module": "ESNext", "moduleResolution": "bundler", "experimentalDecorators": true, "emitDecoratorMetadata": true, "jsx": "react-jsx", "jsxImportSource": "hono/jsx", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "resolveJsonModule": true, "strict": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "noUnusedLocals": true, "noUnusedParameters": true, "noFallthroughCasesInSwitch": true, "noUncheckedSideEffectImports": true, "baseUrl": ".", "paths": {"@/*": ["./src/*"]}, "noEmit": true}, "ts-node": {"require": ["tsconfig-paths/register"]}, "exclude": ["node_modules"]}