import MemberRoleModel, {
  MemberRoleDocument,
} from '@/database/entities/MemberRoleModel';
import { Repository, Injectable } from '@c-cam/core';

/**
 * Repository for managing member roles
 * Extends the BaseRepository with MemberRoleDocument type
 */
@Injectable()
class MemberRoleRepository extends Repository<MemberRoleDocument> {
  constructor() {
    super(MemberRoleModel);
  }

  /**
   * Find member roles by user ID
   * @param userId The user ID to search for
   * @returns A promise that resolves to an array of member roles
   */
  async findByUserId(userId: string): Promise<MemberRoleDocument[]> {
    return this.find({ user_id: userId });
  }

  /**
   * Find member roles by role ID
   * @param roleId The role ID to search for
   * @returns A promise that resolves to an array of member roles
   */
  async findByRoleId(roleId: string): Promise<MemberRoleDocument[]> {
    return this.find({ role_id: roleId });
  }

  /**
   * Find a member role by user ID and role ID
   * @param userId The user ID to search for
   * @param roleId The role ID to search for
   * @returns A promise that resolves to a member role or null if not found
   */
  async findByUserIdAndRoleId(
    userId: string,
    roleId: string,
  ): Promise<MemberRoleDocument | null> {
    return this.findOne({ user_id: userId, role_id: roleId });
  }

  /**
   * Delete member roles by user ID
   * @param userId The user ID to delete roles for
   * @returns A promise that resolves to true if any roles were deleted, false otherwise
   */
  async deleteByUserId(userId: string): Promise<boolean> {
    const result = await this.getModel().deleteMany({ user_id: userId }).exec();
    return result.deletedCount > 0;
  }

  /**
   * Delete member roles by role ID
   * @param roleId The role ID to delete roles for
   * @returns A promise that resolves to true if any roles were deleted, false otherwise
   */
  async deleteByRoleId(roleId: string): Promise<boolean> {
    const result = await this.getModel().deleteMany({ role_id: roleId }).exec();
    return result.deletedCount > 0;
  }
}

export default MemberRoleRepository;
