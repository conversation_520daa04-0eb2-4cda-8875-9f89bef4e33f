import { BaseModel, Inject, Injectable } from '@c-cam/core';
import { EdgeDeviceDocument } from '@/database/entities/EdgeDeviceModel';
import EdgeDeviceRepository from '@/repositories/EdgeDeviceRepository';

/**
 * Service for managing edge devices
 * Extends the BaseModel with EdgeDeviceDocument type
 */
@Injectable()
class EdgeDeviceService extends BaseModel<EdgeDeviceDocument> {
  constructor(
    @Inject(EdgeDeviceRepository)
    repository: EdgeDeviceRepository,
  ) {
    super(repository);
  }

  /**
   * Create a new edge device
   * @param deviceData The edge device data
   * @returns The newly created edge device
   */
  async createEdgeDevice(deviceData: {
    camera_id: string;
    name: string;
    type: string;
    ip_address: string;
    mac_address: string;
    firmware_version: string;
    is_attendance_device: boolean;
    attendance_mode: string;
    preferred_stream_mode: string;
    status: string;
    created_by: string;
  }): Promise<EdgeDeviceDocument> {
    // Validate IP address format
    if (!this.isValidIpAddress(deviceData.ip_address)) {
      throw new Error('Invalid IP address format');
    }

    // Validate MAC address format
    if (!this.isValidMacAddress(deviceData.mac_address)) {
      throw new Error('Invalid MAC address format');
    }

    // Check if a device with the same IP address already exists
    const existingIpDevice = await (
      this.repository as EdgeDeviceRepository
    ).findByIpAddress(deviceData.ip_address);

    if (existingIpDevice) {
      throw new Error(
        `Edge device with IP address '${deviceData.ip_address}' already exists`,
      );
    }

    // Check if a device with the same MAC address already exists
    const existingMacDevice = await (
      this.repository as EdgeDeviceRepository
    ).findByMacAddress(deviceData.mac_address);

    if (existingMacDevice) {
      throw new Error(
        `Edge device with MAC address '${deviceData.mac_address}' already exists`,
      );
    }

    // Create the new edge device
    return this.create(deviceData);
  }

  /**
   * Update an edge device
   * @param id The edge device ID
   * @param deviceData The data to update
   * @returns True if the edge device was updated, false otherwise
   */
  async updateEdgeDevice(
    id: string,
    deviceData: Partial<{
      camera_id: string;
      name: string;
      type: string;
      ip_address: string;
      mac_address: string;
      firmware_version: string;
      is_attendance_device: boolean;
      attendance_mode: string;
      preferred_stream_mode: string;
      status: string;
    }>,
  ): Promise<boolean> {
    // Check if the edge device exists
    const device = await this.findById(id);
    if (!device) {
      throw new Error(`Edge device with ID '${id}' not found`);
    }

    // Validate IP address format if provided
    if (
      deviceData.ip_address &&
      !this.isValidIpAddress(deviceData.ip_address)
    ) {
      throw new Error('Invalid IP address format');
    }

    // Validate MAC address format if provided
    if (
      deviceData.mac_address &&
      !this.isValidMacAddress(deviceData.mac_address)
    ) {
      throw new Error('Invalid MAC address format');
    }

    // Check for unique constraints
    if (deviceData.ip_address && deviceData.ip_address !== device.ip_address) {
      const existingIpDevice = await (
        this.repository as EdgeDeviceRepository
      ).findByIpAddress(deviceData.ip_address);

      if (existingIpDevice && existingIpDevice.id !== id) {
        throw new Error(
          `Edge device with IP address '${deviceData.ip_address}' already exists`,
        );
      }
    }

    if (
      deviceData.mac_address &&
      deviceData.mac_address !== device.mac_address
    ) {
      const existingMacDevice = await (
        this.repository as EdgeDeviceRepository
      ).findByMacAddress(deviceData.mac_address);

      if (existingMacDevice && existingMacDevice.id !== id) {
        throw new Error(
          `Edge device with MAC address '${deviceData.mac_address}' already exists`,
        );
      }
    }

    // Update the edge device
    return this.update(id, deviceData);
  }

  /**
   * Find edge devices by camera ID
   * @param cameraId The camera ID
   * @returns An array of edge devices
   */
  async findByCameraId(cameraId: string): Promise<EdgeDeviceDocument[]> {
    return (this.repository as EdgeDeviceRepository).findByCameraId(cameraId);
  }

  /**
   * Find edge devices by name
   * @param name The device name
   * @returns An array of edge devices
   */
  async findByName(name: string): Promise<EdgeDeviceDocument[]> {
    return (this.repository as EdgeDeviceRepository).findByName(name);
  }

  /**
   * Find edge devices by type
   * @param type The device type
   * @returns An array of edge devices
   */
  async findByType(type: string): Promise<EdgeDeviceDocument[]> {
    return (this.repository as EdgeDeviceRepository).findByType(type);
  }

  /**
   * Find an edge device by IP address
   * @param ipAddress The IP address
   * @returns The edge device or null if not found
   */
  async findByIpAddress(ipAddress: string): Promise<EdgeDeviceDocument | null> {
    return (this.repository as EdgeDeviceRepository).findByIpAddress(ipAddress);
  }

  /**
   * Find an edge device by MAC address
   * @param macAddress The MAC address
   * @returns The edge device or null if not found
   */
  async findByMacAddress(
    macAddress: string,
  ): Promise<EdgeDeviceDocument | null> {
    return (this.repository as EdgeDeviceRepository).findByMacAddress(
      macAddress,
    );
  }

  /**
   * Find edge devices by firmware version
   * @param firmwareVersion The firmware version
   * @returns An array of edge devices
   */
  async findByFirmwareVersion(
    firmwareVersion: string,
  ): Promise<EdgeDeviceDocument[]> {
    return (this.repository as EdgeDeviceRepository).findByFirmwareVersion(
      firmwareVersion,
    );
  }

  /**
   * Find attendance edge devices
   * @returns An array of attendance edge devices
   */
  async findAttendanceDevices(): Promise<EdgeDeviceDocument[]> {
    return (this.repository as EdgeDeviceRepository).findAttendanceDevices();
  }

  /**
   * Find edge devices by status
   * @param status The device status
   * @returns An array of edge devices
   */
  async findByStatus(status: string): Promise<EdgeDeviceDocument[]> {
    return (this.repository as EdgeDeviceRepository).findByStatus(status);
  }

  /**
   * Validate IP address format
   * @param ipAddress The IP address to validate
   * @returns True if the IP address format is valid, false otherwise
   */
  private isValidIpAddress(ipAddress: string): boolean {
    const ipRegex = /^(\d{1,3})\.(\d{1,3})\.(\d{1,3})\.(\d{1,3})$/;
    if (!ipRegex.test(ipAddress)) {
      return false;
    }

    const parts = ipAddress.split('.').map((part) => parseInt(part, 10));
    return parts.every((part) => part >= 0 && part <= 255);
  }

  /**
   * Validate MAC address format
   * @param macAddress The MAC address to validate
   * @returns True if the MAC address format is valid, false otherwise
   */
  private isValidMacAddress(macAddress: string): boolean {
    const macRegex = /^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$/;
    return macRegex.test(macAddress);
  }
}

export default EdgeDeviceService;
