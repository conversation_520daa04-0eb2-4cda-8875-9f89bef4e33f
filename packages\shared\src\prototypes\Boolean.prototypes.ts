/**
 * Boolean prototype extensions for improved functionality
 */

declare global {
  interface Boolean {
    /**
     * Converts the boolean to a string 'Yes' or 'No'
     */
    toYesNo(): string;

    /**
     * Converts the boolean to a string 'True' or 'False'
     */
    toTrueFalse(): string;

    /**
     * Converts the boolean to a string '1' or '0'
     */
    toBinary(): string;

    /**
     * Converts the boolean to a string 'Enabled' or 'Disabled'
     */
    toEnabledDisabled(): string;
  }
}

// Implementation
Boolean.prototype.toYesNo = function(): string {
  return this.valueOf() ? 'Yes' : 'No';
};

Boolean.prototype.toTrueFalse = function(): string {
  return this.valueOf() ? 'True' : 'False';
};

Boolean.prototype.toBinary = function(): string {
  return this.valueOf() ? '1' : '0';
};

Boolean.prototype.toEnabledDisabled = function(): string {
  return this.valueOf() ? 'Enabled' : 'Disabled';
};

export {}; // This export is needed to make the file a module