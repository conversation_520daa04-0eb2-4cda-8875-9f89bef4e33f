import DailyAttendanceSummariesModel, {
  DailyAttendanceSummariesDocument,
} from '@/database/entities/DailyAttendanceSummariesModel';
import { Repository, Injectable } from '@c-cam/core';

/**
 * Repository for managing daily attendance summaries
 * Extends the BaseRepository with DailyAttendanceSummariesDocument type
 */
@Injectable()
class DailyAttendanceSummariesRepository extends Repository<DailyAttendanceSummariesDocument> {
  constructor() {
    super(DailyAttendanceSummariesModel);
  }

  /**
   * Find attendance summaries by user ID
   * @param userId The user ID to search for
   * @returns A promise that resolves to an array of attendance summaries
   */
  async findByUserId(
    userId: string,
  ): Promise<DailyAttendanceSummariesDocument[]> {
    return this.find({ user_id: userId });
  }

  /**
   * Find attendance summaries by shift ID
   * @param shiftId The shift ID to search for
   * @returns A promise that resolves to an array of attendance summaries
   */
  async findByShiftId(
    shiftId: string,
  ): Promise<DailyAttendanceSummariesDocument[]> {
    return this.find({ shift_id: shiftId });
  }

  /**
   * Find attendance summaries by holiday ID
   * @param holidayId The holiday ID to search for
   * @returns A promise that resolves to an array of attendance summaries
   */
  async findByHolidayId(
    holidayId: string,
  ): Promise<DailyAttendanceSummariesDocument[]> {
    return this.find({ holiday_id: holidayId });
  }

  /**
   * Find attendance summaries by work date
   * @param workDate The work date to search for
   * @returns A promise that resolves to an array of attendance summaries
   */
  async findByWorkDate(
    workDate: Date,
  ): Promise<DailyAttendanceSummariesDocument[]> {
    // Create start and end dates for the given day
    const startDate = new Date(workDate);
    startDate.setHours(0, 0, 0, 0);

    const endDate = new Date(workDate);
    endDate.setHours(23, 59, 59, 999);

    return this.find({ work_date: { $gte: startDate, $lte: endDate } });
  }

  /**
   * Find attendance summaries by date range
   * @param startDate The start date of the range
   * @param endDate The end date of the range
   * @returns A promise that resolves to an array of attendance summaries
   */
  async findByDateRange(
    startDate: Date,
    endDate: Date,
  ): Promise<DailyAttendanceSummariesDocument[]> {
    return this.find({ work_date: { $gte: startDate, $lte: endDate } });
  }

  /**
   * Find attendance summaries by user ID and date range
   * @param userId The user ID to search for
   * @param startDate The start date of the range
   * @param endDate The end date of the range
   * @returns A promise that resolves to an array of attendance summaries
   */
  async findByUserIdAndDateRange(
    userId: string,
    startDate: Date,
    endDate: Date,
  ): Promise<DailyAttendanceSummariesDocument[]> {
    return this.find({
      user_id: userId,
      work_date: { $gte: startDate, $lte: endDate },
    });
  }

  /**
   * Find late attendance summaries
   * @returns A promise that resolves to an array of late attendance summaries
   */
  async findLateAttendances(): Promise<DailyAttendanceSummariesDocument[]> {
    return this.find({ is_late: true });
  }

  /**
   * Find early leave attendance summaries
   * @returns A promise that resolves to an array of early leave attendance summaries
   */
  async findEarlyLeaveAttendances(): Promise<
    DailyAttendanceSummariesDocument[]
  > {
    return this.find({ is_early_leave: true });
  }
}

export default DailyAttendanceSummariesRepository;
