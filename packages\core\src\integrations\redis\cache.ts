import { Redis, Cluster } from 'ioredis';
import { RedisConnection } from './redis.js';
import { logger } from '@c-cam/logger';

/**
 * Cache options
 */
export interface CacheOptions {
  /**
   * Default TTL in seconds
   */
  defaultTtl?: number;

  /**
   * Key prefix
   */
  keyPrefix?: string;

  /**
   * Serialization options
   */
  serialization?: {
    /**
     * Custom serializer function
     */
    serialize?: (value: any) => string;
    /**
     * Custom deserializer function
     */
    deserialize?: (value: string) => any;
  };

  /**
   * Error handling options
   */
  errorHandling?: {
    /**
     * Whether to throw errors or return null/false on failures
     */
    throwOnError?: boolean;
    /**
     * Whether to log errors
     */
    logErrors?: boolean;
  };
}

/**
 * Cache interface
 */
export interface ICache {
  /**
   * Get a value from the cache
   * @param key The cache key
   * @returns The cached value or null if not found
   */
  get<T>(key: string): Promise<T | null>;

  /**
   * Set a value in the cache
   * @param key The cache key
   * @param value The value to cache
   * @param ttl Optional TTL in seconds
   * @returns True if the value was set, false otherwise
   */
  set<T>(key: string, value: T, ttl?: number): Promise<boolean>;

  /**
   * Delete a value from the cache
   * @param key The cache key
   * @returns True if the value was deleted, false otherwise
   */
  delete(key: string): Promise<boolean>;

  /**
   * Delete multiple values from the cache
   * @param keys The cache keys to delete
   * @returns Number of keys that were deleted
   */
  deleteMany(keys: string[]): Promise<number>;

  /**
   * Check if a key exists in the cache
   * @param key The cache key
   * @returns True if the key exists, false otherwise
   */
  exists(key: string): Promise<boolean>;

  /**
   * Get the TTL (time to live) of a key
   * @param key The cache key
   * @returns TTL in seconds, -1 if key exists but has no TTL, -2 if key doesn't exist
   */
  ttl(key: string): Promise<number>;

  /**
   * Set TTL for an existing key
   * @param key The cache key
   * @param ttl TTL in seconds
   * @returns True if TTL was set, false otherwise
   */
  expire(key: string, ttl: number): Promise<boolean>;

  /**
   * Clear all values from the cache
   * @returns True if the cache was cleared, false otherwise
   */
  clear(): Promise<boolean>;

  /**
   * Get multiple values from the cache
   * @param keys The cache keys
   * @returns An array of cached values (null for keys not found)
   */
  mget<T>(keys: string[]): Promise<(T | null)[]>;

  /**
   * Set multiple values in the cache
   * @param items Key-value pairs to cache
   * @param ttl Optional TTL in seconds
   * @returns True if all values were set, false otherwise
   */
  mset<T>(items: Record<string, T>, ttl?: number): Promise<boolean>;

  /**
   * Increment a numeric value in the cache
   * @param key The cache key
   * @param value The value to increment by (default: 1)
   * @returns The new value
   */
  increment(key: string, value?: number): Promise<number>;

  /**
   * Decrement a numeric value in the cache
   * @param key The cache key
   * @param value The value to decrement by (default: 1)
   * @returns The new value
   */
  decrement(key: string, value?: number): Promise<number>;

  /**
   * Get all keys matching a pattern
   * @param pattern The pattern to match (supports wildcards)
   * @returns Array of matching keys
   */
  keys(pattern: string): Promise<string[]>;

  /**
   * Get cache statistics
   * @returns Cache statistics object
   */
  getStats(): Promise<{
    totalKeys: number;
    memoryUsage?: number;
    hitRate?: number;
  }>;
}

/**
 * Redis-based distributed cache implementation
 */
export class RedisCache implements ICache {
  private readonly client: Redis | Cluster;
  private readonly options: CacheOptions;

  /**
   * Create a new Redis cache
   * @param options Cache options
   */
  constructor(options: CacheOptions = {}) {
    this.options = {
      defaultTtl: 3600, // 1 hour
      keyPrefix: '',
      serialization: {
        serialize: JSON.stringify,
        deserialize: JSON.parse,
      },
      errorHandling: {
        throwOnError: false,
        logErrors: true,
      },
      ...options,
    };

    // Get the Redis connection
    const connectionManager = RedisConnection.getInstance();

    this.client = connectionManager.getConnection();
  }

  /**
   * Format a cache key with the prefix
   * @param key The cache key
   * @returns The formatted key
   */
  private formatKey(key: string): string {
    return this.options.keyPrefix ? `${this.options.keyPrefix}:${key}` : key;
  }

  /**
   * Serialize a value for storage
   * @param value The value to serialize
   * @returns The serialized value
   */
  private serialize(value: any): string {
    try {
      return this.options.serialization!.serialize!(value);
    } catch (error) {
      if (this.options.errorHandling!.logErrors) {
        logger.error('Error serializing cache value', { error, value });
      }
      throw error;
    }
  }

  /**
   * Deserialize a value from storage
   * @param value The serialized value
   * @returns The deserialized value
   */
  private deserialize<T>(value: string): T {
    try {
      return this.options.serialization!.deserialize!(value) as T;
    } catch (error) {
      if (this.options.errorHandling!.logErrors) {
        logger.error('Error deserializing cache value', { error, value });
      }
      throw error;
    }
  }

  /**
   * Handle cache operation errors
   * @param operation The operation name
   * @param error The error that occurred
   * @param key Optional key involved in the operation
   * @param defaultValue Default value to return on error
   */
  private handleError<T>(
    operation: string,
    error: any,
    key?: string,
    defaultValue?: T,
  ): T {
    const errorMessage = error instanceof Error ? error.message : String(error);

    if (this.options.errorHandling!.logErrors) {
      logger.error(`Error in cache ${operation}`, {
        error: errorMessage,
        key,
      });
    }

    if (this.options.errorHandling!.throwOnError) {
      throw error;
    }

    return defaultValue as T;
  }

  /**
   * Get a value from the cache
   * @param key The cache key
   * @returns The cached value or null if not found
   */
  public async get<T>(key: string): Promise<T | null> {
    try {
      const formattedKey = this.formatKey(key);
      const value = await this.client.get(formattedKey);

      if (!value) {
        return null;
      }

      return this.deserialize<T>(value);
    } catch (error) {
      return this.handleError('get', error, key, null);
    }
  }

  /**
   * Set a value in the cache
   * @param key The cache key
   * @param value The value to cache
   * @param ttl Optional TTL in seconds
   * @returns True if the value was set, false otherwise
   */
  public async set<T>(key: string, value: T, ttl?: number): Promise<boolean> {
    try {
      const formattedKey = this.formatKey(key);
      const serializedValue = this.serialize(value);
      const effectiveTtl = ttl ?? this.options.defaultTtl;

      if (effectiveTtl && effectiveTtl > 0) {
        await this.client.set(
          formattedKey,
          serializedValue,
          'EX',
          effectiveTtl,
        );
      } else {
        await this.client.set(formattedKey, serializedValue);
      }

      return true;
    } catch (error) {
      return this.handleError('set', error, key, false);
    }
  }

  /**
   * Delete a value from the cache
   * @param key The cache key
   * @returns True if the value was deleted, false otherwise
   */
  public async delete(key: string): Promise<boolean> {
    try {
      const formattedKey = this.formatKey(key);
      const result = await this.client.del(formattedKey);
      return result > 0;
    } catch (error) {
      return this.handleError('delete', error, key, false);
    }
  }

  /**
   * Delete multiple values from the cache
   * @param keys The cache keys to delete
   * @returns Number of keys that were deleted
   */
  public async deleteMany(keys: string[]): Promise<number> {
    if (keys.length === 0) {
      return 0;
    }

    try {
      const formattedKeys = keys.map((key) => this.formatKey(key));
      const result = await this.client.del(...formattedKeys);
      return result;
    } catch (error) {
      return this.handleError('deleteMany', error, keys.join(','), 0);
    }
  }

  /**
   * Check if a key exists in the cache
   * @param key The cache key
   * @returns True if the key exists, false otherwise
   */
  public async exists(key: string): Promise<boolean> {
    try {
      const formattedKey = this.formatKey(key);
      const result = await this.client.exists(formattedKey);
      return result === 1;
    } catch (error) {
      return this.handleError('exists', error, key, false);
    }
  }

  /**
   * Get the TTL (time to live) of a key
   * @param key The cache key
   * @returns TTL in seconds, -1 if key exists but has no TTL, -2 if key doesn't exist
   */
  public async ttl(key: string): Promise<number> {
    try {
      const formattedKey = this.formatKey(key);
      return await this.client.ttl(formattedKey);
    } catch (error) {
      return this.handleError('ttl', error, key, -2);
    }
  }

  /**
   * Set TTL for an existing key
   * @param key The cache key
   * @param ttl TTL in seconds
   * @returns True if TTL was set, false otherwise
   */
  public async expire(key: string, ttl: number): Promise<boolean> {
    try {
      const formattedKey = this.formatKey(key);
      const result = await this.client.expire(formattedKey, ttl);
      return result === 1;
    } catch (error) {
      return this.handleError('expire', error, key, false);
    }
  }

  /**
   * Clear all values from the cache
   * @returns True if the cache was cleared, false otherwise
   */
  public async clear(): Promise<boolean> {
    try {
      if (this.options.keyPrefix) {
        // Delete all keys with the prefix using SCAN for better performance
        const pattern = `${this.options.keyPrefix}:*`;
        let cursor = '0';
        let totalDeleted = 0;

        do {
          const result = await this.client.scan(
            cursor,
            'MATCH',
            pattern,
            'COUNT',
            100,
          );
          cursor = result[0];
          const keys = result[1];

          if (keys.length > 0) {
            const deleted = await this.client.del(...keys);
            totalDeleted += deleted;
          }
        } while (cursor !== '0');

        logger.info(
          `Cleared ${totalDeleted} keys from Redis cache with prefix: ${this.options.keyPrefix}`,
        );
      } else {
        // Dangerous operation - only allow if explicitly configured
        await this.client.flushdb();
        logger.info('Cleared entire Redis database');
      }

      return true;
    } catch (error) {
      return this.handleError('clear', error, undefined, false);
    }
  }

  /**
   * Get multiple values from the cache
   * @param keys The cache keys
   * @returns An array of cached values (null for keys not found)
   */
  public async mget<T>(keys: string[]): Promise<(T | null)[]> {
    if (keys.length === 0) {
      return [];
    }

    try {
      const formattedKeys = keys.map((key) => this.formatKey(key));
      const values = await this.client.mget(...formattedKeys);

      return values.map((value: string | null) => {
        if (!value) {
          return null;
        }
        try {
          return this.deserialize<T>(value);
        } catch (error) {
          if (this.options.errorHandling!.logErrors) {
            logger.warn('Error deserializing value in mget', { error, value });
          }
          return null;
        }
      });
    } catch (error) {
      return this.handleError(
        'mget',
        error,
        keys.join(','),
        keys.map(() => null),
      );
    }
  }

  /**
   * Set multiple values in the cache
   * @param items Key-value pairs to cache
   * @param ttl Optional TTL in seconds
   * @returns True if all values were set, false otherwise
   */
  public async mset<T>(
    items: Record<string, T>,
    ttl?: number,
  ): Promise<boolean> {
    if (Object.keys(items).length === 0) {
      return true;
    }

    try {
      const pipeline = this.client.pipeline();
      const effectiveTtl = ttl ?? this.options.defaultTtl;

      for (const [key, value] of Object.entries(items)) {
        const formattedKey = this.formatKey(key);
        const serializedValue = this.serialize(value);

        if (effectiveTtl && effectiveTtl > 0) {
          pipeline.set(formattedKey, serializedValue, 'EX', effectiveTtl);
        } else {
          pipeline.set(formattedKey, serializedValue);
        }
      }

      const results = await pipeline.exec();

      // Check if all operations succeeded
      if (results) {
        const failed = results.some(([error]) => error !== null);
        if (failed && this.options.errorHandling!.logErrors) {
          logger.warn('Some operations failed in mset', { results });
        }
        return !failed;
      }

      return true;
    } catch (error) {
      return this.handleError(
        'mset',
        error,
        Object.keys(items).join(','),
        false,
      );
    }
  }

  /**
   * Increment a numeric value in the cache
   * @param key The cache key
   * @param value The value to increment by (default: 1)
   * @returns The new value
   */
  public async increment(key: string, value = 1): Promise<number> {
    try {
      const formattedKey = this.formatKey(key);
      return await this.client.incrby(formattedKey, value);
    } catch (error) {
      if (this.options.errorHandling!.throwOnError) {
        throw error;
      }
      return this.handleError('increment', error, key, 0);
    }
  }

  /**
   * Decrement a numeric value in the cache
   * @param key The cache key
   * @param value The value to decrement by (default: 1)
   * @returns The new value
   */
  public async decrement(key: string, value = 1): Promise<number> {
    try {
      const formattedKey = this.formatKey(key);
      return await this.client.decrby(formattedKey, value);
    } catch (error) {
      if (this.options.errorHandling!.throwOnError) {
        throw error;
      }
      return this.handleError('decrement', error, key, 0);
    }
  }

  /**
   * Get all keys matching a pattern
   * @param pattern The pattern to match (supports wildcards)
   * @returns Array of matching keys
   */
  public async keys(pattern: string): Promise<string[]> {
    try {
      // Ensure we always have a string, even if keyPrefix is undefined
      const prefix = this.options.keyPrefix || '';
      const formattedPattern = prefix
        ? `${prefix}:${pattern}`
        : pattern;

      const keys: string[] = [];
      let cursor = '0';

      do {
        const result = await this.client.scan(
          cursor,
          'MATCH',
          formattedPattern,
          'COUNT',
          1000,
        );
        cursor = result[0];
        keys.push(...result[1]);
      } while (cursor !== '0');

      // Remove prefix from returned keys if it exists
      if (this.options.keyPrefix) {
        const prefixLength = this.options.keyPrefix.length + 1; // +1 for the colon
        return keys.map((key) => key.substring(prefixLength));
      }

      return keys;
    } catch (error) {
      return this.handleError('keys', error, pattern, []);
    }
  }

  /**
   * Get cache statistics
   * @returns Cache statistics object
   */
  public async getStats(): Promise<{
    totalKeys: number;
    memoryUsage?: number;
    hitRate?: number;
  }> {
    try {
      const info = await this.client.info('memory');
      const keyspace = await this.client.info('keyspace');

      // Parse memory usage
      const memoryMatch = info?.match(/used_memory:(\d+)/) || null;
      const memoryUsage = memoryMatch && memoryMatch[1]
        ? parseInt(memoryMatch[1], 10)
        : undefined;

      // Count keys with prefix if specified
      let totalKeys = 0;
      if (this.options.keyPrefix) {
        // Use the non-null keyPrefix since we've checked it exists
        const keys = await this.keys('*');
        totalKeys = keys.length;
      } else {
        // Parse total keys from keyspace info
        const keyspaceMatch = keyspace?.match(/keys=(\d+)/) || null;
        totalKeys = keyspaceMatch && keyspaceMatch[1] ? parseInt(keyspaceMatch[1], 10) : 0;
      }

      return {
        totalKeys,
        memoryUsage,
        // Hit rate calculation would require additional tracking
        hitRate: undefined,
      };
    } catch (error) {
      return this.handleError('getStats', error, undefined, {
        totalKeys: 0,
        memoryUsage: undefined,
        hitRate: undefined,
      });
    }
  }
}
