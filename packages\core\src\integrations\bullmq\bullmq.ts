import {
  Queue,
  Worker,
  Job,
  QueueOptions,
  WorkerOptions,
  ConnectionOptions,
} from 'bullmq';
import { logger } from '@c-cam/logger';

/**
 * BullMQ connection options
 */
export interface BullMQConnectionOptions {
  /**
   * Redis connection configuration
   */
  connection: ConnectionOptions;

  /**
   * Default queue options
   */
  defaultQueueOptions?: Partial<QueueOptions>;

  /**
   * Default worker options
   */
  defaultWorkerOptions?: Partial<WorkerOptions>;

  /**
   * Health check configuration
   */
  healthCheck?: {
    /**
     * Enable periodic health checks
     */
    enabled?: boolean;
    /**
     * Health check interval in milliseconds
     */
    interval?: number;
    /**
     * Timeout for health check operations in milliseconds
     */
    timeout?: number;
  };

  /**
   * Error handling configuration
   */
  errorHandling?: {
    /**
     * Whether to throw errors or log and continue
     */
    throwOnError?: boolean;
    /**
     * Maximum retry attempts for operations
     */
    maxRetries?: number;
    /**
     * Retry delay in milliseconds
     */
    retryDelay?: number;
  };
}

/**
 * BullMQ health status
 */
export interface BullMQHealthStatus {
  /**
   * Overall health status
   */
  isHealthy: boolean;

  /**
   * Connection status
   */
  isConnected: boolean;

  /**
   * Connection start time
   */
  connectionStartTime: number | null;

  /**
   * Last error message
   */
  lastError: string | null;

  /**
   * Number of active queues
   */
  activeQueues: number;

  /**
   * Number of active workers
   */
  activeWorkers: number;

  /**
   * Connection information
   */
  connectionInfo: {
    host?: string;
    port?: number;
    db?: number;
  };
}

/**
 * Job processor function
 */
export type JobProcessor<T = any> = (job: Job<T>) => Promise<any>;

/**
 * Job event handlers
 */
export interface JobEventHandlers {
  onCompleted?: (job: Job, result: any) => Promise<void>;
  onFailed?: (job: Job, error: Error) => Promise<void>;
  onProgress?: (job: Job, progress: any) => Promise<void>;
  onStalled?: (jobId: string) => void;
  onWaiting?: (jobId: string) => void;
  onActive?: (job: Job) => void;
  onPaused?: () => void;
  onResumed?: () => void;
  onCleaned?: (jobs: string[], type: string) => void;
  onDrained?: () => void;
}

/**
 * BullMQ connection manager
 */
export class BullMQConnection {
  private static _instance: BullMQConnection;
  private _queues: Map<string, Queue> = new Map();
  private _workers: Map<string, Worker> = new Map();
  private _options: BullMQConnectionOptions | null = null;
  private _connectionStartTime: number | null = null;
  private _lastError: string | null = null;
  private _healthCheckInterval: NodeJS.Timeout | null = null;

  private constructor() {}

  /**
   * Get the singleton instance
   */
  public static getInstance(): BullMQConnection {
    if (!BullMQConnection._instance) {
      BullMQConnection._instance = new BullMQConnection();
    }
    return BullMQConnection._instance;
  }

  /**
   * Get the current connection options
   * @returns The connection options or null if not initialized
   */
  public getOptions(): BullMQConnectionOptions | null {
    return this._options;
  }

  /**
   * Get health status
   * @returns Current health status
   */
  public getHealthStatus(): BullMQHealthStatus {
    return {
      isHealthy: this.isConnected() && this._lastError === null,
      isConnected: this.isConnected(),
      connectionStartTime: this._connectionStartTime,
      lastError: this._lastError,
      activeQueues: this._queues.size,
      activeWorkers: this._workers.size,
      connectionInfo: {
        host: (this._options?.connection as any)?.host,
        port: (this._options?.connection as any)?.port,
        db: (this._options?.connection as any)?.db,
      },
    };
  }

  /**
   * Check if BullMQ is connected and ready
   * @returns True if connected, false otherwise
   */
  public isConnected(): boolean {
    return (
      this._options !== null &&
      (this._queues.size > 0 || this._workers.size > 0)
    );
  }

  /**
   * Perform a health check
   * @returns Promise resolving to true if healthy, false otherwise
   */
  public async healthCheck(): Promise<boolean> {
    if (!this._options) {
      return false;
    }

    try {
      // Test connection by creating a temporary queue and checking its status
      const testQueue = new Queue('__health_check__', {
        connection: this._options.connection,
      });

      await testQueue.waitUntilReady();
      await testQueue.close();

      this._lastError = null;
      return true;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      this._lastError = errorMessage;

      logger.error('BullMQ health check failed', {
        error: errorMessage,
      });

      return false;
    }
  }

  /**
   * Initialize the BullMQ connection
   * @param options Connection options
   * @returns Promise resolving when initialization is complete
   * @throws Error if initialization fails
   */
  public async initialize(options: BullMQConnectionOptions): Promise<void> {
    if (this._options && this.isConnected()) {
      logger.warn('BullMQ connection already initialized and connected');
      return;
    }

    // Clean up any existing connections
    if (this._options) {
      await this.close();
    }

    this._options = {
      defaultQueueOptions: {},
      defaultWorkerOptions: {
        concurrency: 1,
      },
      healthCheck: {
        enabled: false,
        interval: 30000,
        timeout: 5000,
      },
      errorHandling: {
        throwOnError: false,
        maxRetries: 3,
        retryDelay: 1000,
      },
      ...options,
    };

    this._lastError = null;
    this._connectionStartTime = Date.now();

    // Start health check if enabled
    this._startHealthCheck();

    logger.info('BullMQ connection manager initialized', {
      connection: this._maskConnectionInfo(this._options.connection),
      healthCheckEnabled: this._options.healthCheck?.enabled,
    });
  }

  /**
   * Create a new queue
   * @param name Queue name
   * @param options Queue options
   * @returns Promise resolving to the created queue
   */
  public async createQueue(
    name: string,
    options?: Partial<QueueOptions>,
  ): Promise<Queue> {
    if (!this._options) {
      throw new Error(
        'BullMQ connection not initialized. Call initialize() first.',
      );
    }

    if (this._queues.has(name)) {
      logger.warn(`Queue '${name}' already exists, returning existing queue`);
      return this._queues.get(name)!;
    }

    try {
      const queueOptions: QueueOptions = {
        connection: this._options.connection,
        ...this._options.defaultQueueOptions,
        ...options,
      };

      const queue = new Queue(name, queueOptions);

      // Set up event listeners
      this._setupQueueEventListeners(queue, name);

      // Wait for queue to be ready
      await queue.waitUntilReady();

      this._queues.set(name, queue);

      logger.info(`Queue '${name}' created successfully`, {
        queueName: name,
      });

      return queue;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      this._lastError = errorMessage;

      logger.error(`Failed to create queue '${name}'`, {
        error: errorMessage,
        queueName: name,
      });

      throw error;
    }
  }

  /**
   * Create a new worker
   * @param queueName Queue name to process
   * @param processor Job processor function
   * @param options Worker options
   * @param eventHandlers Event handlers
   * @returns Promise resolving to the created worker
   */
  public async createWorker<T = any>(
    queueName: string,
    processor: JobProcessor<T>,
    options?: Partial<WorkerOptions>,
    eventHandlers?: JobEventHandlers,
  ): Promise<Worker> {
    if (!this._options) {
      throw new Error(
        'BullMQ connection not initialized. Call initialize() first.',
      );
    }

    const workerKey = `${queueName}-worker`;

    if (this._workers.has(workerKey)) {
      logger.warn(
        `Worker for queue '${queueName}' already exists, returning existing worker`,
      );
      return this._workers.get(workerKey)!;
    }

    try {
      const workerOptions: WorkerOptions = {
        connection: this._options.connection,
        ...this._options.defaultWorkerOptions,
        ...options,
      };

      const worker = new Worker(queueName, processor, workerOptions);

      // Set up event listeners
      this._setupWorkerEventListeners(worker, queueName, eventHandlers);

      // Wait for worker to be ready
      await worker.waitUntilReady();

      this._workers.set(workerKey, worker);

      logger.info(`Worker for queue '${queueName}' created successfully`, {
        queueName,
        concurrency: workerOptions.concurrency,
      });

      return worker;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      this._lastError = errorMessage;

      logger.error(`Failed to create worker for queue '${queueName}'`, {
        error: errorMessage,
        queueName,
      });

      throw error;
    }
  }

  /**
   * Get a queue by name
   * @param name Queue name
   * @returns The queue or undefined if not found
   */
  public getQueue(name: string): Queue | undefined {
    return this._queues.get(name);
  }

  /**
   * Get a worker by queue name
   * @param queueName Queue name
   * @returns The worker or undefined if not found
   */
  public getWorker(queueName: string): Worker | undefined {
    return this._workers.get(`${queueName}-worker`);
  }

  /**
   * Get all queue names
   * @returns Array of queue names
   */
  public getQueueNames(): string[] {
    return Array.from(this._queues.keys());
  }

  /**
   * Get all worker queue names
   * @returns Array of worker queue names
   */
  public getWorkerQueueNames(): string[] {
    return Array.from(this._workers.keys()).map((key) =>
      key.replace('-worker', ''),
    );
  }

  /**
   * Close all connections and clean up resources
   * @returns Promise resolving when cleanup is complete
   */
  public async close(): Promise<void> {
    logger.info('Closing BullMQ connections...');

    // Stop health check
    this._stopHealthCheck();

    // Close all workers
    const workerClosePromises = Array.from(this._workers.values()).map(
      async (worker) => {
        try {
          await worker.close();
        } catch (error) {
          logger.error('Error closing worker', {
            error: error instanceof Error ? error.message : String(error),
          });
        }
      },
    );

    // Close all queues
    const queueClosePromises = Array.from(this._queues.values()).map(
      async (queue) => {
        try {
          await queue.close();
        } catch (error) {
          logger.error('Error closing queue', {
            error: error instanceof Error ? error.message : String(error),
          });
        }
      },
    );

    // Wait for all closures to complete
    await Promise.all([...workerClosePromises, ...queueClosePromises]);

    // Clear collections
    this._workers.clear();
    this._queues.clear();

    // Reset state
    this._options = null;
    this._connectionStartTime = null;
    this._lastError = null;

    logger.info('BullMQ connections closed successfully');
  }

  /**
   * Start health check monitoring
   */
  private _startHealthCheck(): void {
    if (!this._options?.healthCheck?.enabled) {
      return;
    }

    this._stopHealthCheck(); // Stop any existing health check

    const interval = this._options.healthCheck.interval || 30000;

    this._healthCheckInterval = setInterval(async () => {
      try {
        await this.healthCheck();
      } catch (error) {
        logger.error('Health check error', {
          error: error instanceof Error ? error.message : String(error),
        });
      }
    }, interval);

    logger.debug('BullMQ health check monitoring started', {
      interval,
    });
  }

  /**
   * Stop health check monitoring
   */
  private _stopHealthCheck(): void {
    if (this._healthCheckInterval) {
      clearInterval(this._healthCheckInterval);
      this._healthCheckInterval = null;
      logger.debug('BullMQ health check monitoring stopped');
    }
  }

  /**
   * Set up event listeners for a queue
   * @param queue The queue to set up listeners for
   * @param queueName The queue name
   */
  private _setupQueueEventListeners(queue: Queue, queueName: string): void {
    queue.on('error', (error) => {
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      this._lastError = errorMessage;

      logger.error(`Queue '${queueName}' error`, {
        error: errorMessage,
        queueName,
      });
    });

    queue.on('waiting', (jobId) => {
      logger.debug(`Job ${jobId} is waiting in queue '${queueName}'`, {
        jobId,
        queueName,
      });
    });

    queue.on('cleaned', (jobs, type) => {
      logger.info(`Queue '${queueName}' cleaned ${jobs.length} ${type} jobs`, {
        queueName,
        jobCount: jobs.length,
        type,
      });
    });
  }

  /**
   * Set up event listeners for a worker
   * @param worker The worker to set up listeners for
   * @param queueName The queue name
   * @param eventHandlers Optional event handlers
   */
  private _setupWorkerEventListeners(
    worker: Worker,
    queueName: string,
    eventHandlers?: JobEventHandlers,
  ): void {
    worker.on('error', (error) => {
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      this._lastError = errorMessage;

      logger.error(`Worker for queue '${queueName}' error`, {
        error: errorMessage,
        queueName,
      });
    });

    worker.on('completed', async (job, result) => {
      logger.debug(`Job ${job.id} completed in queue '${queueName}'`, {
        jobId: job.id,
        queueName,
      });

      if (eventHandlers?.onCompleted) {
        try {
          await eventHandlers.onCompleted(job, result);
        } catch (error) {
          logger.error('Error in onCompleted handler', {
            error: error instanceof Error ? error.message : String(error),
            jobId: job.id,
            queueName,
          });
        }
      }
    });

    worker.on('failed', async (job, error) => {
      logger.error(
        `Job ${job?.id || 'unknown'} failed in queue '${queueName}'`,
        {
          error: error.message,
          jobId: job?.id,
          queueName,
        },
      );

      if (eventHandlers?.onFailed && job) {
        try {
          await eventHandlers.onFailed(job, error);
        } catch (handlerError) {
          logger.error('Error in onFailed handler', {
            error:
              handlerError instanceof Error
                ? handlerError.message
                : String(handlerError),
            jobId: job.id,
            queueName,
          });
        }
      }
    });

    worker.on('progress', async (job, progress) => {
      logger.debug(
        `Job ${job.id} progress in queue '${queueName}': ${JSON.stringify(progress)}`,
        {
          jobId: job.id,
          queueName,
          progress,
        },
      );

      if (eventHandlers?.onProgress) {
        try {
          await eventHandlers.onProgress(job, progress);
        } catch (error) {
          logger.error('Error in onProgress handler', {
            error: error instanceof Error ? error.message : String(error),
            jobId: job.id,
            queueName,
          });
        }
      }
    });

    worker.on('stalled', (jobId) => {
      logger.warn(`Job ${jobId} stalled in queue '${queueName}'`, {
        jobId,
        queueName,
      });

      if (eventHandlers?.onStalled) {
        eventHandlers.onStalled(jobId);
      }
    });

    worker.on('active', (job) => {
      logger.debug(`Job ${job.id} started processing in queue '${queueName}'`, {
        jobId: job.id,
        queueName,
      });

      if (eventHandlers?.onActive) {
        eventHandlers.onActive(job);
      }
    });

    worker.on('paused', () => {
      logger.info(`Worker for queue '${queueName}' paused`, {
        queueName,
      });

      if (eventHandlers?.onPaused) {
        eventHandlers.onPaused();
      }
    });

    worker.on('resumed', () => {
      logger.info(`Worker for queue '${queueName}' resumed`, {
        queueName,
      });

      if (eventHandlers?.onResumed) {
        eventHandlers.onResumed();
      }
    });

    worker.on('drained', () => {
      logger.debug(`Worker for queue '${queueName}' drained`, {
        queueName,
      });

      if (eventHandlers?.onDrained) {
        eventHandlers.onDrained();
      }
    });
  }

  /**
   * Mask sensitive connection information for logging
   * @param connection Connection options
   * @returns Masked connection info
   */
  private _maskConnectionInfo(connection: ConnectionOptions): any {
    const masked = { ...connection };

    // Mask password if present
    if ((masked as any).password) {
      (masked as any).password = '***';
    }

    return masked;
  }
}
