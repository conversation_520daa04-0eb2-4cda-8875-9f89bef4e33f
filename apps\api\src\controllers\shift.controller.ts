import { Context } from 'hono';
import {
  Controller,
  ControllerBase,
  HttpContext,
  HttpGet,
  HttpPost,
  HttpPut,
  HttpDelete,
  Inject,
  Param,
  Body,
  Query,
  UnauthorizedError,
  ValidationError,
} from '@c-cam/core';
import ShiftService from '../services/ShiftService';

@Controller('/api/shifts')
export class ShiftController extends ControllerBase {
  constructor(
    @Inject(ShiftService) private shiftService: ShiftService,
  ) {
    super();
  }

  /**
   * Helper method to get authenticated user ID
   */
  private getAuthenticatedUserId(c: Context): string {
    const userId = c.get('userId');
    if (!userId) {
      throw new UnauthorizedError('You must be authenticated to access this resource');
    }
    return userId;
  }

  /**
   * Get all shifts
   */
  @HttpGet('/')
  async getShifts(
    @HttpContext() c: Context,
    @Query('limit') limit?: string,
    @Query('skip') skip?: string,
    @Query('sortBy') sortBy?: string,
    @Query('sortDirection') sortDirection?: string,
  ): Promise<Response> {
    this.getAuthenticatedUserId(c);

    const shifts = await this.shiftService.find({
      limit: limit ? parseInt(limit) : undefined,
      skip: skip ? parseInt(skip) : undefined,
      sortBy,
      sortDirection,
    });

    return this.success(c, { shifts });
  }

  /**
   * Get a shift by ID
   */
  @HttpGet('/:id')
  async getShiftById(
    @HttpContext() c: Context,
    @Param('id') id: string,
  ): Promise<Response> {
    this.getAuthenticatedUserId(c);
    this.validateIf(!id, 'Shift ID is required');

    const shift = await this.shiftService.findById(id);
    this.notFoundIf(!shift, 'Shift not found');

    return this.success(c, { shift });
  }

  /**
   * Create a new shift
   */
  @HttpPost('/')
  async createShift(@HttpContext() c: Context): Promise<Response> {
    this.getAuthenticatedUserId(c);
    const shiftData = await c.req.json();

    // Validate required fields
    this.validateRequiredFields(shiftData, ['name', 'shift_type', 'work_coefficient']);

    // Sanitize the data
    const sanitizedData = this.sanitizeData(shiftData);

    const shift = await this.shiftService.createShift(sanitizedData);
    return this.created(c, { shift }, 'Shift created successfully');
  }

  /**
   * Update a shift
   */
  @HttpPut('/:id')
  async updateShift(
    @HttpContext() c: Context,
    @Param('id') id: string,
  ): Promise<Response> {
    this.getAuthenticatedUserId(c);
    this.validateIf(!id, 'Shift ID is required');

    const shiftData = await c.req.json();

    // Sanitize the data
    const sanitizedData = this.sanitizeData(shiftData);

    const success = await this.shiftService.updateShift(id, sanitizedData);
    this.validateIf(!success, 'Failed to update shift');

    return this.success(c, { success: true }, 'Shift updated successfully');
  }

  /**
   * Delete a shift
   */
  @HttpDelete('/:id')
  async deleteShift(
    @HttpContext() c: Context,
    @Param('id') id: string,
  ): Promise<Response> {
    this.getAuthenticatedUserId(c);
    this.validateIf(!id, 'Shift ID is required');

    const success = await this.shiftService.delete(id);
    this.validateIf(!success, 'Failed to delete shift');

    return this.success(c, { success: true }, 'Shift deleted successfully');
  }

  /**
   * Find a shift by name
   */
  @HttpGet('/name/:name')
  async getShiftByName(
    @HttpContext() c: Context,
    @Param('name') name: string,
  ): Promise<Response> {
    this.getAuthenticatedUserId(c);
    this.validateIf(!name, 'Shift name is required');

    const shift = await this.shiftService.findByName(name);
    this.notFoundIf(!shift, 'Shift not found');

    return this.success(c, { shift });
  }

  /**
   * Find shifts by shift type
   */
  @HttpGet('/type/:shiftType')
  async getShiftsByShiftType(
    @HttpContext() c: Context,
    @Param('shiftType') shiftType: string,
  ): Promise<Response> {
    this.getAuthenticatedUserId(c);
    this.validateIf(!shiftType, 'Shift type is required');

    const shifts = await this.shiftService.findByShiftType(shiftType);
    return this.success(c, { shifts });
  }

  /**
   * Find shifts by work coefficient
   */
  @HttpGet('/coefficient/:workCoefficient')
  async getShiftsByWorkCoefficient(
    @HttpContext() c: Context,
    @Param('workCoefficient') workCoefficient: string,
  ): Promise<Response> {
    this.getAuthenticatedUserId(c);
    this.validateIf(!workCoefficient, 'Work coefficient is required');

    const coefficient = parseFloat(workCoefficient);
    this.validateIf(isNaN(coefficient), 'Invalid work coefficient');

    const shifts = await this.shiftService.findByWorkCoefficient(coefficient);
    return this.success(c, { shifts });
  }
}
