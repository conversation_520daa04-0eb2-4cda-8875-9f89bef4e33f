import ShiftModel, { ShiftDocument } from '@/database/entities/ShiftModel';
import { Repository, Injectable } from '@c-cam/core';

/**
 * Repository for managing shifts
 * Extends the BaseRepository with ShiftDocument type
 */
@Injectable()
class ShiftRepository extends Repository<ShiftDocument> {
  constructor() {
    super(ShiftModel);
  }

  /**
   * Find a shift by name
   * @param name The shift name to search for
   * @returns A promise that resolves to a shift or null if not found
   */
  async findByName(name: string): Promise<ShiftDocument | null> {
    return this.findOne({ name });
  }

  /**
   * Find shifts by shift type
   * @param shiftType The shift type to search for
   * @returns A promise that resolves to an array of shifts
   */
  async findByShiftType(shiftType: string): Promise<ShiftDocument[]> {
    return this.find({ shift_type: shiftType });
  }

  /**
   * Find shifts by work coefficient
   * @param workCoefficient The work coefficient to search for
   * @returns A promise that resolves to an array of shifts
   */
  async findByWorkCoefficient(
    workCoefficient: number,
  ): Promise<ShiftDocument[]> {
    return this.find({ work_coefficient: workCoefficient });
  }
}

export default ShiftRepository;
