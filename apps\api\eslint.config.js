import backendConfig from '@c-cam/eslint/backend';
import honoConfig from '@hono/eslint-config';

export default [...honoConfig, backendConfig, {
  rules: {
    'perfectionist/sort-imports': [
      'error',
      {
        tsconfigRootDir: '.',
        order: {
          type: 'asc',
          groups: [
            ['builtin', 'external'],
            'internal',
            ['parent', 'sibling', 'index']
          ]
        }
      },
    ],
    // Override or add specific rules for the API app
    'no-console': ['error'],
    'node/no-process-env': ['error']
  },
}]
