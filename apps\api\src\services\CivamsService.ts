import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import FormData from 'form-data';

// GraphQL Types
interface GraphQLError {
  message: string;
  locations?: { line: number; column: number }[];
  path?: string[];
  extensions?: Record<string, unknown>;
}

interface GraphQLResponse<T> {
  data?: T;
  errors?: GraphQLError[];
}

// Types and Interfaces

/**
 * Gender enum for user data
 */
enum Gender {
  Male = 'Male',
  Female = 'Female',
  Other = 'Other'
}

/**
 * User interface representing user data
 */
interface User {
  id: string;
  fullName: string;
  avatar?: string;
  avatarBase64?: { path: string; data?: string };
  email: string;
  accessToken?: string;
  roles?: { id: string }[];
}

/**
 * Response for user query
 */
interface UserQueryResponse {
  user: User;
}

/**
 * Response for userWeb query
 */
interface UserWebQueryResponse {
  userWeb: User;
}

/**
 * Login credentials for authentication
 */
interface LoginCredentials {
  email: string;
  password: string;
  type?: number; // 0 for AES, 1 for RSA
}

/**
 * User ability/permission definition
 */
interface Ability {
  action: string;
  subject: string;
}

/**
 * User role definition
 */
interface Role {
  id: string;
  name: string;
}

/**
 * Login response data structure
 */
interface LoginResponseData {
  login: {
    accessToken: string;
    refreshToken: string;
    fullName: string;
    avatar?: string;
    roles: Role[];
    role: string;
    ability: Ability[];
    id: string;
  };
}

/**
 * Payload for token refresh
 */
interface RefetchTokenPayload {
  refreshToken: string;
}

/**
 * Response data for token refresh
 */
interface RefetchTokenResponseData {
  refetchAccessToken: {
    accessToken: string;
    refreshToken: string;
  };
}

/**
 * Input for adding a new user
 */
interface AddUserInput {
  userId: string;
  email: string;
  fullName: string;
  birthDay: string; // YYYY-MM-DD format
  phoneNumber: string;
  gender: Gender;
  departmentId: string;
  companyId: string;
  employeeCode: string;
  adName: string;
  title: string;
  isFaceReScanEnable?: boolean;
  isRemoteCheckEnable?: boolean;
}

/**
 * Response data for adding a user
 */
interface AddUserResponseData {
  addUser: {
    id: string;
    fullName: string;
    avatar?: string;
  };
}

/**
 * API-specific types
 */

/**
 * Face result type enum
 */
enum FaceResultType {
  CheckIn = 0,
  CheckOut = 1,
  Undefined = 2,
  All = 3
}

/**
 * Login credentials for API authentication
 */
interface ApiLoginCredentials {
  email: string;
  password: string;
  type?: number; // 0 for AES, 1 for RSA
}

/**
 * API login response type
 */
type ApiLoginResponse = LoginResponseData['login'];

/**
 * Payload for API token refresh
 */
interface ApiRefreshTokenPayload {
  refreshToken: string;
}

/**
 * Response for API token refresh
 */
type ApiRefreshTokenResponse = RefetchTokenResponseData['refetchAccessToken'];

/**
 * Parameters for exporting face recognition results to Excel
 */
interface ExportFaceResultToExcelParams {
  type: FaceResultType;
  deviceIds?: string;
  textSearch?: string;
  departmentIds?: string;
  companyIds?: string;
  order?: string;
  timekeepingOnly?: boolean;
  startTime: string; // YYYY-MM-DD
  endTime: string; // YYYY-MM-DD
}

/**
 * Service for interacting with the Civams Face ID API
 * Provides methods for both GraphQL and REST API endpoints
 */
class CivamsService {
  private axiosInstance: AxiosInstance;
  private accessToken?: string;
  private readonly baseUrl: string;

  /**
   * Creates a new instance of the Civams API service
   * @param baseURL The base URL of the API
   * @param accessToken Optional initial access token
   */
  constructor(baseURL: string, accessToken?: string) {
    this.baseUrl = baseURL;
    this.accessToken = accessToken;
    this.axiosInstance = axios.create({
      baseURL: this.baseUrl,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Add authorization interceptor
    this.setupInterceptors();
  }

  /**
   * Sets up request interceptors for authentication
   * @private
   */
  private setupInterceptors(): void {
    this.axiosInstance.interceptors.request.use(
      (config) => {
        if (this.accessToken && config.headers) {
          config.headers['Authorization'] = `Bearer ${this.accessToken}`;
        }
        return config;
      },
      (error) => Promise.reject(error)
    );
  }

  /**
   * Sets the access token for authentication
   * @param token The access token
   */
  public setAccessToken(token: string): void {
    this.accessToken = token;
  }

  /**
   * Gets the current access token
   * @returns The current access token or undefined
   */
  public getAccessToken(): string | undefined {
    return this.accessToken;
  }

  /**
   * Clears the current access token
   */
  public clearAccessToken(): void {
    this.accessToken = undefined;
  }

  /**
   * Makes a GraphQL request to the API
   * @param query The GraphQL query or mutation string
   * @param variables Optional variables for the query/mutation
   * @returns Promise with the GraphQL response
   */
  private async makeGraphQLRequest<T>(
    query: string,
    variables?: Record<string, unknown>,
  ): Promise<GraphQLResponse<T>> {
    try {
      const response = await this.axiosInstance.post<GraphQLResponse<T>>('/graphql', {
        query,
        variables,
      });

      return response.data;
    } catch (error) {
      if (axios.isAxiosError(error) && error.response) {
        throw new Error(`GraphQL request failed: ${error.response.status} - ${JSON.stringify(error.response.data)}`);
      }
      throw error;
    }
  }

  /**
   * Makes a REST API request
   * @param config The Axios request configuration
   * @returns Promise with the API response
   */
  private async makeApiRequest<T>(
    config: AxiosRequestConfig,
  ): Promise<AxiosResponse<T>> {
    try {
      return await this.axiosInstance<T>(config);
    } catch (error) {
      if (axios.isAxiosError(error) && error.response) {
        throw new Error(
          `API request to ${config.url} failed: ${error.response.status} - ${JSON.stringify(error.response.data)}`
        );
      }
      throw error;
    }
  }

  // --- User GraphQL Methods ---

  /**
   * Fetches information of the currently logged-in user
   * @returns Promise with user information
   */
  async getCurrentUser(): Promise<GraphQLResponse<UserQueryResponse>> {
    const query = `
      query user {
        user {
          id
          fullName
          avatar
          avatarBase64 { path data }
          email
          accessToken
          roles { id }
        }
      }
    `;
    return this.makeGraphQLRequest<UserQueryResponse>(query);
  }

  /**
   * Fetches web-specific information of the currently logged-in user
   * @returns Promise with user web information
   */
  async getCurrentUserWeb(): Promise<GraphQLResponse<UserWebQueryResponse>> {
    const query = `
      query userWeb {
        userWeb {
          id
          fullName
          avatar
          avatarBase64 { path }
          email
          roles { id }
        }
      }
    `;
    return this.makeGraphQLRequest<UserWebQueryResponse>(query);
  }

  /**
   * Authenticates a user and retrieves a token
   * @param credentials User login credentials
   * @returns Promise with login response data
   */
  async login(
    credentials: LoginCredentials,
  ): Promise<GraphQLResponse<LoginResponseData>> {
    const query = `
      query Login($email: String!, $password: String!, $type: Float) {
        login(email: $email, password: $password, type: $type) {
          accessToken
          refreshToken
          fullName
          avatar
          roles { id name }
          role
          ability { action subject }
          id
        }
      }
    `;

    // Convert credentials to Record<string, unknown> to fix type error
    const variables: Record<string, unknown> = {
      email: credentials.email,
      password: credentials.password,
      type: credentials.type
    };

    const response = await this.makeGraphQLRequest<LoginResponseData>(query, variables);

    if (response.data?.login.accessToken) {
      this.setAccessToken(response.data.login.accessToken);
    }

    return response;
  }

  /**
   * Refreshes the access token using a refresh token
   * @param payload Refresh token payload
   * @returns Promise with refreshed token data
   */
  async refetchAccessToken(
    payload: RefetchTokenPayload,
  ): Promise<GraphQLResponse<RefetchTokenResponseData>> {
    const query = `
      mutation refetchAccessToken($refreshToken: String!) {
        refetchAccessToken(refreshToken: $refreshToken) {
          accessToken
          refreshToken
        }
      }
    `;

    // Convert payload to Record<string, unknown> to fix type error
    const variables: Record<string, unknown> = {
      refreshToken: payload.refreshToken
    };

    const response = await this.makeGraphQLRequest<RefetchTokenResponseData>(query, variables);

    if (response.data?.refetchAccessToken.accessToken) {
      this.setAccessToken(response.data.refetchAccessToken.accessToken);
    }

    return response;
  }

  /**
   * Adds a new user to the system
   * @param userInput User data to add
   * @returns Promise with added user data
   */
  async addUser(
    userInput: AddUserInput,
  ): Promise<GraphQLResponse<AddUserResponseData>> {
    const query = `
      mutation addUser(
        $userId: String!, $email: String!, $fullName: String!, $birthDay: Date!,
        $phoneNumber: String!, $gender: Gender!, $departmentId: String!,
        $companyId: String!, $employeeCode: String!, $adName: String!, $title: String!,
        $isFaceReScanEnable: Boolean, $isRemoteCheckEnable: Boolean
      ) {
        addUser(input: {
          userId: $userId, email: $email, fullName: $fullName, birthDay: $birthDay,
          phoneNumber: $phoneNumber, gender: $gender, departmentId: $departmentId,
          companyId: $companyId, employeeCode: $employeeCode, adName: $adName, title: $title,
          isFaceReScanEnable: $isFaceReScanEnable, isRemoteCheckEnable: $isRemoteCheckEnable
        }) {
          id
          fullName
          avatar
        }
      }
    `;

    // Create variables object with proper typing
    const variables: Record<string, unknown> = {
      userId: userInput.userId,
      email: userInput.email,
      fullName: userInput.fullName,
      birthDay: userInput.birthDay,
      phoneNumber: userInput.phoneNumber,
      gender: userInput.gender,
      departmentId: userInput.departmentId,
      companyId: userInput.companyId,
      employeeCode: userInput.employeeCode,
      adName: userInput.adName,
      title: userInput.title,
    };

    // Add optional properties only if they exist
    if (userInput.isFaceReScanEnable !== undefined) {
      variables.isFaceReScanEnable = userInput.isFaceReScanEnable;
    }

    if (userInput.isRemoteCheckEnable !== undefined) {
      variables.isRemoteCheckEnable = userInput.isRemoteCheckEnable;
    }

    return this.makeGraphQLRequest<AddUserResponseData>(query, variables);
  }

  // --- REST API Methods ---

  /**
   * Authenticates a user via REST API
   * @param credentials API login credentials
   * @returns Promise with login response
   */
  async apiLogin(credentials: ApiLoginCredentials): Promise<ApiLoginResponse> {
    const params = new URLSearchParams();
    params.append('email', credentials.email);
    params.append('password', credentials.password);

    if (credentials.type !== undefined) {
      params.append('type', credentials.type.toString());
    }

    const response = await this.makeApiRequest<ApiLoginResponse>({
      method: 'POST',
      url: '/api/user/login',
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
      data: params,
    });

    if (response.data.accessToken) {
      this.setAccessToken(response.data.accessToken);
    }

    return response.data;
  }

  /**
   * Refreshes the access token via REST API
   * @param payload Refresh token payload
   * @returns Promise with refreshed token response
   */
  async apiRefreshToken(
    payload: ApiRefreshTokenPayload,
  ): Promise<ApiRefreshTokenResponse> {
    const formData = new FormData();
    formData.append('refreshToken', payload.refreshToken);

    const response = await this.makeApiRequest<ApiRefreshTokenResponse>({
      method: 'POST',
      url: '/api/user/refresh-token',
      headers: {
        // Use FormData headers when available (Node.js environment)
        ...(formData.getHeaders ? formData.getHeaders() : {}),
      },
      data: formData,
    });

    if (response.data.accessToken) {
      this.setAccessToken(response.data.accessToken);
    }

    return response.data;
  }

  /**
   * Imports user data with files
   * @param formData FormData containing user data and files
   * @returns Promise with import response
   */
  async importUserData(formData: FormData): Promise<AxiosResponse<unknown>> {
    return this.makeApiRequest<unknown>({
      method: 'PUT',
      url: '/api/user/import',
      headers: {
        ...(formData.getHeaders ? formData.getHeaders() : {}),
      },
      data: formData,
    });
  }

  /**
   * Exports face recognition results to Excel
   * @param params Export parameters
   * @returns Promise with Excel data as string
   */
  async exportFaceResultToExcel(
    params: ExportFaceResultToExcelParams,
  ): Promise<AxiosResponse<string>> {
    return this.makeApiRequest<string>({
      method: 'GET',
      url: '/api/timekeeping',
      params,
    });
  }

  /**
   * Creates a new company
   * @param name Company name
   * @returns Promise with created company data
   */
  async createCompany(name: string): Promise<
    GraphQLResponse<{
      createCompany: {
        id: string;
        name: string;
        sortKey: string;
        count: number;
      };
    }>
  > {
    const query = `
      mutation createCompany($name: String!) {
        createCompany(name: $name) {
          id
          name
          sortKey
          count
        }
      }
    `;

    return this.makeGraphQLRequest(query, { name });
  }

  // Additional methods can be added following the same pattern for:
  // - Department management
  // - Device management
  // - Shift User management
  // - Other API endpoints as needed
}

// Export the service class
export { CivamsService, Gender, FaceResultType };
