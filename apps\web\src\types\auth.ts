export interface User {
  id: string
  username: string
  name: string
  email?: string
  phone?: string
  dob?: string
  gender?: string
  unit_id?: string
  member_role_id?: string
  face_id?: string
  roles: Array<string>
  permissions: Array<string>
  created_at?: string
}

export interface LoginRequest {
  username: string
  password: string
  deviceInfo?: {
    deviceName?: string
    deviceType?: string
    userAgent?: string
  }
}

export interface LoginResponse {
  access_token: string
  expires_in: number
  token_type: string
  user: User
}

export interface RefreshTokenRequest {
  deviceInfo?: {
    deviceName?: string
    deviceType?: string
    userAgent?: string
  }
}

export interface RefreshTokenResponse {
  access_token: string
  expires_in: number
  token_type: string
}

export interface VerifyTokenRequest {
  token: string
}

export interface VerifyTokenResponse {
  valid: boolean
  user?: User
}

export interface AuthState {
  user: User | null
  accessToken: string | null
  refreshToken: string | null
  isAuthenticated: boolean
  isLoading: boolean
  error: string | null
}

export interface AuthContextType extends AuthState {
  login: (credentials: LoginRequest) => Promise<void>
  logout: () => Promise<void>
  logoutAll: () => Promise<void>
  refreshAccessToken: () => Promise<void>
  verifyToken: (token?: string) => Promise<boolean>
  clearError: () => void
}

export interface TokenPayload {
  sub: string // user id
  username: string
  role: string
  unit_id?: string
  member_role_id?: string
  iat: number
  exp: number
}
