import { Context } from 'hono';
import {
  Controller,
  ControllerBase,
  HttpContext,
  HttpGet,
  HttpPost,
  HttpPut,
  HttpDelete,
  Inject,
  Param,
  Query,
  UnauthorizedError,
  NotFoundError,
  ValidationError,
} from '@c-cam/core';
import CameraService from '../services/CameraService';

@Controller('/api/cameras')
export class CameraController extends ControllerBase {
  constructor(
    @Inject(CameraService) private cameraService: CameraService,
  ) {
    super();
  }

  /**
   * Helper method to get authenticated user ID
   */
  private getAuthenticatedUserId(c: Context): string {
    const userId = c.get('userId');
    if (!userId) {
      throw new UnauthorizedError('You must be authenticated to access this resource');
    }
    return userId;
  }

  /**
   * Get all cameras
   */
  @HttpGet('/')
  async getCameras(
    @HttpContext() c: Context,
    @Query('limit') limit?: string,
    @Query('skip') skip?: string,
    @Query('sortBy') sortBy?: string,
    @Query('sortDirection') sortDirection?: string,
  ): Promise<Response> {
    this.getAuthenticatedUserId(c);

    const cameras = await this.cameraService.find({
      limit: limit ? parseInt(limit) : undefined,
      skip: skip ? parseInt(skip) : undefined,
      sortBy,
      sortDirection,
    });

    return this.success(c, { cameras });
  }

  /**
   * Find cameras by type
   */
  @HttpGet('/type/:type')
  async getCamerasByType(
    @HttpContext() c: Context,
    @Param('type') type: string,
  ): Promise<Response> {
    this.getAuthenticatedUserId(c);
    this.validateIf(!type, 'Camera type is required');

    const cameras = await this.cameraService.findByType(type);
    return this.success(c, { cameras });
  }

  /**
   * Find cameras by status
   */
  @HttpGet('/status/:status')
  async getCamerasByStatus(
    @HttpContext() c: Context,
    @Param('status') status: string,
  ): Promise<Response> {
    this.getAuthenticatedUserId(c);
    this.validateIf(!status, 'Camera status is required');

    const cameras = await this.cameraService.findByStatus(status);
    return this.success(c, { cameras });
  }

  /**
   * Find cameras by location
   */
  @HttpGet('/location/:location')
  async getCamerasByLocation(
    @HttpContext() c: Context,
    @Param('location') location: string,
  ): Promise<Response> {
    this.getAuthenticatedUserId(c);
    this.validateIf(!location, 'Camera location is required');

    const cameras = await this.cameraService.findByLocation(location);
    return this.success(c, { cameras });
  }

  /**
   * Find a camera by IP address
   */
  @HttpGet('/ip/:ipAddress')
  async getCameraByIpAddress(
    @HttpContext() c: Context,
    @Param('ipAddress') ipAddress: string,
  ): Promise<Response> {
    this.getAuthenticatedUserId(c);
    this.validateIf(!ipAddress, 'IP address is required');

    const camera = await this.cameraService.findByIpAddress(ipAddress);
    this.notFoundIf(!camera, 'Camera not found');

    return this.success(c, { camera });
  }

  /**
   * Get a camera by ID
   */
  @HttpGet('/:id')
  async getCameraById(
    @HttpContext() c: Context,
    @Param('id') id: string,
  ): Promise<Response> {
    this.getAuthenticatedUserId(c);
    this.validateIf(!id, 'Camera ID is required');

    const camera = await this.cameraService.findById(id);
    this.notFoundIf(!camera, 'Camera not found');

    return this.success(c, { camera });
  }

  /**
   * Create a new camera
   */
  @HttpPost('/')
  async createCamera(@HttpContext() c: Context): Promise<Response> {
    const userId = this.getAuthenticatedUserId(c);
    const cameraData = await c.req.json();

    // Validate required fields
    this.validateRequiredFields(cameraData, ['name', 'ip_address', 'location']);

    // Sanitize the data
    const sanitizedData = this.sanitizeData(cameraData);

    // Add the creator ID
    sanitizedData.created_by = userId;

    const camera = await this.cameraService.createCamera(sanitizedData);
    return this.created(c, { camera }, 'Camera created successfully');
  }

  /**
   * Update a camera
   */
  @HttpPut('/:id')
  async updateCamera(
    @HttpContext() c: Context,
    @Param('id') id: string,
  ): Promise<Response> {
    this.getAuthenticatedUserId(c);
    this.validateIf(!id, 'Camera ID is required');

    const cameraData = await c.req.json();

    // Sanitize the data
    const sanitizedData = this.sanitizeData(cameraData);

    const success = await this.cameraService.updateCamera(id, sanitizedData);
    this.validateIf(!success, 'Failed to update camera');

    return this.success(c, { success: true }, 'Camera updated successfully');
  }

  /**
   * Delete a camera
   */
  @HttpDelete('/:id')
  async deleteCamera(
    @HttpContext() c: Context,
    @Param('id') id: string,
  ): Promise<Response> {
    this.getAuthenticatedUserId(c);
    this.validateIf(!id, 'Camera ID is required');

    const success = await this.cameraService.delete(id);
    this.validateIf(!success, 'Failed to delete camera');

    return this.success(c, { success: true }, 'Camera deleted successfully');
  }
}
