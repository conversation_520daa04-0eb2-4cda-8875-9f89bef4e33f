# Global Provider Implementation

## Tổng quan

Hệ thống Global Provider được thiết kế để quản lý toàn bộ trạng thái ứng dụng và đồng bộ hóa logic refresh token giữa các component khác nhau.

## Kiến trúc

### 1. Global Provider (`apps/web/src/providers/global-provider.tsx`)

**Chức năng chính:**
- Quản lý trạng thái toàn cục của ứng dụng
- Đồng bộ hóa refresh token logic
- Theo dõi route hiện tại và ngăn chặn refresh token trên auth pages
- Cung cấp context cho toàn bộ ứng dụng

**Global Refresh State:**
```typescript
export const globalRefreshState = {
  isRefreshing: false,
  refreshPromise: null,
  isOnAuthPage: false,
}
```

### 2. Axios Interceptor (`apps/web/src/configs/axios.ts`)

**<PERSON><PERSON><PERSON> tiến:**
- <PERSON><PERSON><PERSON> tra global refresh state trư<PERSON><PERSON> khi thực hiện refresh
- Không refresh token khi đang ở auth pages
- Sử dụng global refresh function nếu có sẵn
- Fallback về local refresh logic nếu cần

**Logic ưu tiên:**
1. Kiểm tra `globalState.isOnAuthPage` → Skip nếu true
2. Kiểm tra `globalState.isRefreshing` → Chờ promise hiện tại
3. Sử dụng `window.__globalRefreshToken` nếu có
4. Fallback về local refresh logic

### 3. Identity Actions Hook (`apps/web/src/hooks/use-identity-actions.ts`)

**Cải tiến:**
- Tích hợp với global refresh state
- Kiểm tra auth page trước khi refresh
- Đồng bộ với axios interceptor
- Ngăn chặn multiple refresh attempts

## Luồng hoạt động

### 1. Khởi tạo ứng dụng
```
App Start → GlobalProvider → useIdentityActions → Initialize Auth State
```

### 2. Refresh Token Flow
```
API Call (401) → Axios Interceptor → Check Global State → 
  ├─ On Auth Page → Skip Refresh
  ├─ Global Refresh Active → Wait for Promise
  ├─ Global Function Available → Use Global Function
  └─ Fallback → Local Refresh Logic
```

### 3. Route Change
```
Route Change → GlobalProvider → Update globalRefreshState.isOnAuthPage
```

## Tính năng chính

### 1. Ngăn chặn Refresh Token trên Auth Pages
- Tự động detect auth routes (`/auth/*`)
- Skip refresh token khi đang ở login/register pages
- Tránh infinite loops và conflicts

### 2. Đồng bộ hóa Refresh Logic
- Shared state giữa axios và hooks
- Prevent multiple simultaneous refresh attempts
- Queue failed requests during refresh

### 3. Global State Management
- Centralized auth state
- Route tracking
- Loading states synchronization

## Sử dụng

### 1. Trong Components
```typescript
import { useAuth, useGlobalContext } from '@/providers/global-provider'

function MyComponent() {
  const auth = useAuth() // Backward compatible
  const global = useGlobalContext() // Full global state
  
  return (
    <div>
      <p>Authenticated: {auth.isAuthenticated}</p>
      <p>Current Route: {global.currentRoute}</p>
    </div>
  )
}
```

### 2. Trong API Calls
```typescript
// Axios tự động handle refresh token
const response = await api.get('/protected-endpoint')
```

## Lợi ích

1. **Tránh Duplicate Refresh Calls**: Chỉ một refresh token call tại một thời điểm
2. **Auth Page Protection**: Không refresh token khi đang ở trang login
3. **Centralized State**: Quản lý trạng thái tập trung
4. **Better UX**: Smooth authentication flow
5. **Error Handling**: Robust error handling và fallback logic

## Testing

Sử dụng `TestGlobalProvider` component để test:
```typescript
import TestGlobalProvider from '@/test-global-provider'

// Render trong app để test functionality
<TestGlobalProvider />
```

## Migration Notes

- `AuthProvider` được thay thế bởi `GlobalProvider`
- `useIdentityActions` vẫn hoạt động nhưng nên dùng `useAuth`
- Backward compatibility được đảm bảo
- Không cần thay đổi existing API calls
