# Global Provider Implementation (Refactored)

## Tổng quan

Hệ thống được tái cấu trúc để phân tách trách nhiệm rõ ràng giữa Global Provider và Auth Provider, với Global Provider chỉ quản lý đồng bộ hóa refresh token và app state, không can thiệp vào auth logic.

## Kiến trúc mới

### Cấu trúc lồng nhau:
```
GlobalProvider (App-wide state & refresh sync)
  └── AppProviders (QueryClient)
      └── AuthProvider (Auth state & logic)
          └── AuthIntegration (Bridge between auth & global)
              └── App Components
```

### 1. Global Provider (`apps/web/src/providers/global-provider.tsx`)

**Chức năng:**
- Quản lý app-wide state (route, initialization)
- Đồng bộ hóa refresh token calls
- Theo dõi auth pages để skip refresh
- KHÔNG quản lý auth state

**Global Refresh State:**
```typescript
export const globalRefreshState = {
  isRefreshing: false,
  refreshPromise: null,
  isOnAuthPage: false,
}
```

### 2. Auth Provider (`apps/web/src/contexts/auth-context.tsx`)

**Chức năng:**
- Quản lý auth state và actions
- Sử dụng useIdentityActions hook
- Cung cấp auth context cho components

### 3. App Providers (`apps/web/src/providers/auth-provider.tsx`)

**Chức năng:**
- Setup QueryClient
- Wrap AuthProvider
- AuthIntegration component để bridge auth với global

### 2. Axios Interceptor (`apps/web/src/configs/axios.ts`)

**Cải tiến:**
- Kiểm tra global refresh state trước khi thực hiện refresh
- Không refresh token khi đang ở auth pages
- Sử dụng global refresh function nếu có sẵn
- Fallback về local refresh logic nếu cần

**Logic ưu tiên:**
1. Kiểm tra `globalState.isOnAuthPage` → Skip nếu true
2. Kiểm tra `globalState.isRefreshing` → Chờ promise hiện tại
3. Sử dụng `window.__globalRefreshToken` nếu có
4. Fallback về local refresh logic

### 3. Identity Actions Hook (`apps/web/src/hooks/use-identity-actions.ts`)

**Cải tiến:**
- Tích hợp với global refresh state
- Kiểm tra auth page trước khi refresh
- Đồng bộ với axios interceptor
- Ngăn chặn multiple refresh attempts

## Luồng hoạt động

### 1. Khởi tạo ứng dụng
```
App Start → GlobalProvider → useIdentityActions → Initialize Auth State
```

### 2. Refresh Token Flow
```
API Call (401) → Axios Interceptor → Check Global State →
  ├─ On Auth Page → Skip Refresh
  ├─ Global Refresh Active → Wait for Promise
  ├─ Global Function Available → Use Global Function
  └─ Fallback → Local Refresh Logic
```

### 3. Route Change
```
Route Change → GlobalProvider → Update globalRefreshState.isOnAuthPage
```

## Tính năng chính

### 1. Ngăn chặn Refresh Token trên Auth Pages
- Tự động detect auth routes (`/auth/*`)
- Skip refresh token khi đang ở login/register pages
- Tránh infinite loops và conflicts

### 2. Đồng bộ hóa Refresh Logic
- Shared state giữa axios và hooks
- Prevent multiple simultaneous refresh attempts
- Queue failed requests during refresh

### 3. Global State Management
- Centralized auth state
- Route tracking
- Loading states synchronization

## Sử dụng

### 1. Trong Components
```typescript
import { useAuth } from '@/contexts/auth-context'
import { useGlobalContext } from '@/providers/global-provider'

function MyComponent() {
  const auth = useAuth() // Auth state & actions
  const global = useGlobalContext() // Global app state

  return (
    <div>
      <p>Authenticated: {auth.isAuthenticated}</p>
      <p>Current Route: {global.currentRoute}</p>
      <p>Global Refreshing: {global.isRefreshingToken}</p>
    </div>
  )
}
```

### 2. Refresh Token Synchronization
```typescript
// From anywhere in the app
const global = useGlobalContext()

// This will use the synchronized refresh logic
await global.synchronizedRefreshToken()

// Or from auth context
const auth = useAuth()
await auth.refreshAccessToken() // This gets synchronized automatically
```

### 2. Trong API Calls
```typescript
// Axios tự động handle refresh token
const response = await api.get('/protected-endpoint')
```

## Lợi ích

1. **Tránh Duplicate Refresh Calls**: Chỉ một refresh token call tại một thời điểm
2. **Auth Page Protection**: Không refresh token khi đang ở trang login
3. **Centralized State**: Quản lý trạng thái tập trung
4. **Better UX**: Smooth authentication flow
5. **Error Handling**: Robust error handling và fallback logic

## Testing

Sử dụng `TestGlobalProvider` component để test:
```typescript
import TestGlobalProvider from '@/test-global-provider'

// Render trong app để test functionality
<TestGlobalProvider />
```

## Migration Notes

- `AuthProvider` được thay thế bởi `GlobalProvider`
- `useIdentityActions` vẫn hoạt động nhưng nên dùng `useAuth`
- Backward compatibility được đảm bảo
- Không cần thay đổi existing API calls
