import CameraModel, {
  CameraDocument,
} from '@/database/entities/CameraModel';
import { Repository, Injectable } from '@c-cam/core';

/**
 * Repository for managing cameras
 * Extends the BaseRepository with CameraDocument type
 */
@Injectable()
class CameraRepository extends Repository<CameraDocument> {
  constructor() {
    super(CameraModel);
  }

  /**
   * Find cameras by name
   * @param name The camera name to search for
   * @returns A promise that resolves to an array of cameras
   */
  async findByName(name: string): Promise<CameraDocument[]> {
    return this.find({ name });
  }

  /**
   * Find cameras by IP address
   * @param ipAddress The IP address to search for
   * @returns A promise that resolves to a camera or null if not found
   */
  async findByIpAddress(ipAddress: string): Promise<CameraDocument | null> {
    return this.findOne({ ip_address: ipAddress });
  }

  /**
   * Find cameras by type
   * @param type The camera type to search for
   * @returns A promise that resolves to an array of cameras
   */
  async findByType(type: string): Promise<CameraDocument[]> {
    return this.find({ type });
  }

  /**
   * Find cameras by status
   * @param status The camera status to search for
   * @returns A promise that resolves to an array of cameras
   */
  async findByStatus(status: string): Promise<CameraDocument[]> {
    return this.find({ status });
  }

  /**
   * Find cameras by location
   * @param location The camera location to search for
   * @returns A promise that resolves to an array of cameras
   */
  async findByLocation(location: string): Promise<CameraDocument[]> {
    return this.find({ location });
  }

  /**
   * Find cameras by created by
   * @param createdBy The creator ID to search for
   * @returns A promise that resolves to an array of cameras
   */
  async findByCreatedBy(createdBy: string): Promise<CameraDocument[]> {
    return this.find({ created_by: createdBy });
  }
}

export default CameraRepository;
