import { Document } from 'mongoose';
import { createSchema, createModel } from '@c-cam/core';
import { MemberRoleAttributes } from '@c-cam/types';

/**
 * Member Role Document Interface
 * Extends the MemberRoleAttributes (excluding id) and Document
 */
export interface MemberRoleDocument
  extends Omit<MemberRoleAttributes, 'id'>,
    Document {}

/**
 * Member Role Schema
 * Defines the MongoDB schema for member roles
 */
const MemberRoleSchema = createSchema({
  role_id: {
    type: String,
    ref: 'role',
    required: true,
  },
  user_id: {
    type: String,
    ref: 'users',
    required: true,
  },
  created_by: { type: String, required: true },
});

// Add indexes
MemberRoleSchema.index({ user_id: 1, role_id: 1 }, { unique: true });

// Create and export the model
const MemberRoleModel = createModel<MemberRoleDocument>(
  'member_role',
  MemberRoleSchema,
);

export default MemberRoleModel;
