import { useIdentityActions } from '@/hooks/use-identity-actions'
import {
  hasAllPermissions,
  hasAnyPermission,
  hasAnyRole,
  hasPermission,
  hasRole,
} from '@/utils/auth'

/**
 * Hook for checking user permissions and roles
 */
export const usePermissions = () => {
  const { user, isAuthenticated } = useIdentityActions()

  const userPermissions = user?.permissions || []
  const userRoles = user?.roles || []

  return {
    // User info
    user,
    isAuthenticated,
    userPermissions,
    userRoles,

    // Permission checks
    hasPermission: (permission: string) =>
      isAuthenticated && hasPermission(userPermissions, permission),

    hasAnyPermission: (permissions: Array<string>) =>
      isAuthenticated && hasAnyPermission(userPermissions, permissions),

    hasAllPermissions: (permissions: Array<string>) =>
      isAuthenticated && hasAllPermissions(userPermissions, permissions),

    // Role checks
    hasRole: (role: string) => isAuthenticated && hasRole(userRoles, role),

    hasAnyRole: (roles: Array<string>) =>
      isAuthenticated && hasAnyRole(userRoles, roles),

    // Common role checks
    isAdmin: () => isAuthenticated && hasRole(userRoles, 'admin'),
    isManager: () => isAuthenticated && hasRole(userRoles, 'manager'),
    isUser: () => isAuthenticated && hasRole(userRoles, 'user'),

    // Permission-based checks (examples)
    canViewUsers: () =>
      isAuthenticated && hasPermission(userPermissions, 'users.view'),
    canCreateUsers: () =>
      isAuthenticated && hasPermission(userPermissions, 'users.create'),
    canEditUsers: () =>
      isAuthenticated && hasPermission(userPermissions, 'users.edit'),
    canDeleteUsers: () =>
      isAuthenticated && hasPermission(userPermissions, 'users.delete'),

    canViewDevices: () =>
      isAuthenticated && hasPermission(userPermissions, 'devices.view'),
    canManageDevices: () =>
      isAuthenticated && hasPermission(userPermissions, 'devices.manage'),

    canViewReports: () =>
      isAuthenticated && hasPermission(userPermissions, 'reports.view'),
    canExportReports: () =>
      isAuthenticated && hasPermission(userPermissions, 'reports.export'),

    canManageSystem: () =>
      isAuthenticated && hasPermission(userPermissions, 'system.manage'),
    canViewLogs: () =>
      isAuthenticated && hasPermission(userPermissions, 'logs.view'),
  }
}

/**
 * Hook for protecting components based on permissions
 */
export const usePermissionGuard = () => {
  const permissions = usePermissions()

  /**
   * Render component only if user has required permission
   */
  const withPermission = (
    permission: string,
    component: React.ReactNode,
    fallback?: React.ReactNode,
  ) => {
    if (permissions.hasPermission(permission)) {
      return component
    }
    return fallback || null
  }

  /**
   * Render component only if user has any of the required permissions
   */
  const withAnyPermission = (
    permissionList: Array<string>,
    component: React.ReactNode,
    fallback?: React.ReactNode,
  ) => {
    if (permissions.hasAnyPermission(permissionList)) {
      return component
    }
    return fallback || null
  }

  /**
   * Render component only if user has required role
   */
  const withRole = (
    role: string,
    component: React.ReactNode,
    fallback?: React.ReactNode,
  ) => {
    if (permissions.hasRole(role)) {
      return component
    }
    return fallback || null
  }

  /**
   * Render component only if user has any of the required roles
   */
  const withAnyRole = (
    roles: Array<string>,
    component: React.ReactNode,
    fallback?: React.ReactNode,
  ) => {
    if (permissions.hasAnyRole(roles)) {
      return component
    }
    return fallback || null
  }

  return {
    ...permissions,
    withPermission,
    withAnyPermission,
    withRole,
    withAnyRole,
  }
}

export default usePermissions
