import {
  KafkaConnection,
  KafkaConnectionOptions,
  KafkaHealthStatus,
  KafkaClient,
  IKafkaClient,
} from './index.js';

/**
 * Example of how to use the enhanced Kafka integration with health monitoring
 */
async function kafkaExample() {
  // 1. Configure and initialize the Kafka connection with enhanced features
  const kafkaOptions: KafkaConnectionOptions = {
    client: {
      clientId: 'example-client',
      brokers: ['localhost:9092'],
      // Authentication if needed
      // sasl: {
      //   mechanism: 'plain',
      //   username: 'user',
      //   password: 'pass',
      // },
      // ssl: true,
    },
    consumer: {
      groupId: 'example-group',
    },
    producer: {
      allowAutoTopicCreation: true,
      // Other producer options
    },
    admin: {
      // Admin options
    },
    // Enhanced health monitoring
    healthCheck: {
      enabled: true,
      interval: 30000, // 30 seconds
      timeout: 5000, // 5 seconds
    },
    // Enhanced error handling
    errorHandling: {
      throwOnError: false, // Log errors instead of throwing
      maxRetries: 3,
      retryDelay: 1000,
    },
  };

  // Get the Kafka connection manager
  const kafkaConnection = KafkaConnection.getInstance();

  // Initialize the connection
  await kafkaConnection.initialize(kafkaOptions);

  // Check connection health
  const healthStatus: KafkaHealthStatus = kafkaConnection.getHealthStatus();
  console.log('Kafka Health Status:', healthStatus);

  // Perform a health check
  const isHealthy = await kafkaConnection.healthCheck();
  console.log('Kafka is healthy:', isHealthy);

  // Check if connected
  console.log('Is connected:', kafkaConnection.isConnected());

  // Create a client instance
  const client: IKafkaClient = new KafkaClient({
    topicPrefix: 'app',
    fromBeginning: false,
  });

  // Basic Kafka operations

  // Create a topic
  await client.createTopic('events', 3, 1);

  // List all topics
  const topics = await client.listTopics();
  console.log('Available topics:', topics);

  // Subscribe to a topic
  await client.subscribe('events');

  // Register a message handler
  client.onMessage('events', async (topic, partition, message) => {
    console.log(`Received message on topic ${topic}, partition ${partition}:`);
    console.log('Key:', message.key);
    console.log('Value:', message.value ? message.value.toString() : null);
    console.log('Headers:', message.headers);
    console.log('Timestamp:', message.timestamp);
    console.log('Offset:', message.offset);
  });

  // Produce a message
  await client.produce('events', 'Hello, Kafka!', 'message-key', {
    contentType: 'text/plain',
  });

  // Produce an object (will be converted to JSON)
  await client.produce(
    'events',
    {
      id: 123,
      name: 'Test Event',
      timestamp: new Date().toISOString(),
    },
    'event-123',
    {
      contentType: 'application/json',
    },
  );

  // 5. Advanced usage

  // Subscribe to a wildcard topic (not directly supported in Kafka, but our client handles it)
  client.onMessage('app.events.*', async (topic, partition, message) => {
    console.log(
      `Received message on wildcard topic ${topic}, partition ${partition}:`,
    );
    console.log('Value:', message.value ? message.value.toString() : null);
  });

  // Produce to different topics that match the wildcard
  await client.produce('events.created', 'Event created');
  await client.produce('events.updated', 'Event updated');
  await client.produce('events.deleted', 'Event deleted');

  // Wait for a while to receive messages
  await new Promise((resolve) => setTimeout(resolve, 5000));

  // 6. Health monitoring and connection management

  // Get final health status
  const finalHealthStatus = kafkaConnection.getHealthStatus();
  console.log('Final Health Status:', finalHealthStatus);

  // Check connection status
  console.log('Final connection status:', kafkaConnection.isConnected());

  // Close the connection gracefully when done
  await kafkaConnection.close();
}

// This is just an example and won't be executed
// kafkaExample().catch(console.error);

// Export the example function for documentation purposes
export { kafkaExample };
