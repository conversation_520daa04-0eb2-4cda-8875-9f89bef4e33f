import { Document } from 'mongoose';
import { createSchema, createModel } from '@c-cam/core';
import { EdgeDeviceLogsAttributes } from '@c-cam/types';

/**
 * Edge Device Logs Document Interface
 * Extends the EdgeDeviceLogsAttributes (excluding id) and Document
 */
export interface EdgeDeviceLogsDocument
  extends Omit<EdgeDeviceLogsAttributes, 'id'>,
    Document {}

/**
 * Edge Device Logs Schema
 * Defines the MongoDB schema for edge device logs
 */
const EdgeDeviceLogsSchema = createSchema({
  edge_device_id: {
    type: String,
    ref: 'edge_device',
    required: true,
  },
  event_type: { type: String, required: true },
  description: { type: String, required: true },
  created_by: { type: String, required: true },
});

// Add indexes
EdgeDeviceLogsSchema.index({ edge_device_id: 1, created_at: -1 });

// Create and export the model
const EdgeDeviceLogsModel = createModel<EdgeDeviceLogsDocument>(
  'edge_device_logs',
  EdgeDeviceLogsSchema,
);

export default EdgeDeviceLogsModel;
