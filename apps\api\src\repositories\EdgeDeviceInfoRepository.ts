import EdgeDeviceInfoModel, {
  EdgeDeviceInfoDocument,
} from '@/database/entities/EdgeDeviceInfoModel';
import { Repository, Injectable } from '@c-cam/core';

/**
 * Repository for managing edge device information
 * Extends the BaseRepository with EdgeDeviceInfoDocument type
 */
@Injectable()
class EdgeDeviceInfoRepository extends Repository<EdgeDeviceInfoDocument> {
  constructor() {
    super(EdgeDeviceInfoModel);
  }

  /**
   * Find edge device info by edge device ID
   * @param edgeDeviceId The edge device ID to search for
   * @returns A promise that resolves to an array of edge device info
   */
  async findByEdgeDeviceId(
    edgeDeviceId: string,
  ): Promise<EdgeDeviceInfoDocument[]> {
    return this.find({ edge_device_id: edgeDeviceId });
  }

  /**
   * Find edge device info by CPU model
   * @param cpuModel The CPU model to search for
   * @returns A promise that resolves to an array of edge device info
   */
  async findByCpuModel(cpuModel: string): Promise<EdgeDeviceInfoDocument[]> {
    return this.find({ cpu_model: cpuModel });
  }

  /**
   * Find edge device info by operating system
   * @param os The operating system to search for
   * @returns A promise that resolves to an array of edge device info
   */
  async findByOperatingSystem(os: string): Promise<EdgeDeviceInfoDocument[]> {
    return this.find({ operating_system: os });
  }

  /**
   * Find edge device info by date range
   * @param startDate The start date of the range
   * @param endDate The end date of the range
   * @returns A promise that resolves to an array of edge device info
   */
  async findByDateRange(
    startDate: Date,
    endDate: Date,
  ): Promise<EdgeDeviceInfoDocument[]> {
    return this.find({
      created_at: { $gte: startDate, $lte: endDate },
    });
  }

  /**
   * Delete edge device info by edge device ID
   * @param edgeDeviceId The edge device ID to delete info for
   * @returns A promise that resolves to true if any info was deleted, false otherwise
   */
  async deleteByEdgeDeviceId(edgeDeviceId: string): Promise<boolean> {
    const result = await this.getModel()
      .deleteMany({ edge_device_id: edgeDeviceId })
      .exec();
    return result.deletedCount > 0;
  }
}

export default EdgeDeviceInfoRepository;
