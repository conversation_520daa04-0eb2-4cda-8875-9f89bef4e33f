import { useEffect } from 'react'
import { useNavigate } from '@tanstack/react-router'
import HeaderLayout from '../shared/header'
import { SiderLayout } from '../shared/sider'
import { SidebarProvider } from '../ui/sidebar'
import { BuildingIcon } from '../shared/icons/components/building-icon'
import { SmartphoneWifiIcon } from '../shared/icons/components/smartphone-wifi-icon'
import { SettingIcon } from '../shared/icons/components/setting-icon'
import { TimeManagementCircleIcon } from '../shared/icons/components/time-management-circle-icon'
import { cn } from '@/utils/cn'
import { useIdentityActions } from '@/hooks/use-identity-actions'

interface Props extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode
}

export default function ProtectedLayout({
  children,
  className,
  ...props
}: Props) {
  const { isAuthenticated, isLoading } = useIdentityActions()
  const navigate = useNavigate()

  // Redirect to login if not authenticated
  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      navigate({ to: '/auth/login' })
    }
  }, [isAuthenticated, isLoading, navigate])

  // Show loading while checking authentication
  if (isLoading) {
    return (
      <div className="h-screen flex items-center justify-center">
        <div className="flex flex-col items-center gap-4">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <p className="text-sm text-gray-600">Đang kiểm tra xác thực...</p>
        </div>
      </div>
    )
  }

  // Don't render if not authenticated
  if (!isAuthenticated) {
    return null
  }

  return (
    <div
      className={cn('h-screen flex flex-col overflow-hidden', className)}
      {...props}
    >
      {/* Header section - full width, positioned at the top */}
      <div className="w-full z-50 flex-shrink-0">
        <HeaderLayout />
      </div>

      {/* Body section with fixed height */}
      <div className="flex flex-1 overflow-hidden">
        <SidebarProvider>
          {/* Sidebar and content container */}
          <div className="flex w-full h-full">
            {/* Sidebar with fixed height */}
            <div className="h-full flex-shrink-0">
              <SiderLayout
                items={[
                  {
                    title: 'Tổ chức',
                    url: '#',
                    icon: BuildingIcon,
                    isActive: true,
                  },
                  {
                    title: 'Quản lý thiết bị',
                    url: '#',
                    icon: SmartphoneWifiIcon,
                  },
                  {
                    title: 'Thiết lập hệ thống',
                    url: '#',
                    icon: SettingIcon,
                    items: [
                      {
                        title: 'Vai trò',
                        url: '#',
                      },
                    ],
                  },
                  {
                    title: 'Theo dõi hệ thống',
                    url: '#',
                    icon: TimeManagementCircleIcon,
                  },
                ]}
              />
            </div>

            {/* Main content - only this will scroll */}
            <main className="flex-1 overflow-auto bg-[#f5f6f7] transition-all duration-200 pb-[65px]">
              {children}
            </main>
          </div>
        </SidebarProvider>
      </div>
    </div>
  )
}
