import { Context } from 'hono';
import {
  Controller,
  ControllerBase,
  HttpContext,
  HttpGet,
  HttpPost,
  HttpPut,
  <PERSON>ttpDelete,
  Inject,
} from '@c-cam/core';
import TenantService from '../services/TenantService';

@Controller('/api/tenants')
export class TenantController extends ControllerBase {
  constructor(
    @Inject(TenantService) private tenantService: TenantService,
  ) {
    super();
  }

  /**
   * Get all organizations
   */
  @HttpGet('/')
  async getOrganizations(@HttpContext() c: Context): Promise<Response> {
    try {
      const userId = c.get('userId');
      if (!userId) {
        return c.json({ error: 'Unauthorized' }, 401);
      }

      const { limit, skip, sortBy, sortDirection } = c.req.query();

      const organizations = await this.tenantService.find({
        limit: limit ? parseInt(limit) : undefined,
        skip: skip ? parseInt(skip) : undefined,
        sortBy,
        sortDirection,
      });

      return c.json({ organizations });
    } catch (error: any) {
      return c.json({ error: error.message }, 500);
    }
  }

  /**
   * Get an organization by ID
   */
  @HttpGet('/:id')
  async getOrganizationById(@HttpContext() c: Context): Promise<Response> {
    try {
      const userId = c.get('userId');
      if (!userId) {
        return c.json({ error: 'Unauthorized' }, 401);
      }

      const { id } = c.req.param();
      if (!id) {
        return c.json({ error: 'Organization ID is required' }, 400);
      }
      const organization = await this.tenantService.findById(id);

      if (!organization) {
        return c.json({ error: 'Organization not found' }, 404);
      }

      return c.json({ organization });
    } catch (error: any) {
      return c.json({ error: error.message }, 500);
    }
  }

  /**
   * Create a new organization
   */
  @HttpPost('/')
  async createOrganization(@HttpContext() c: Context): Promise<Response> {
    try {
      const userId = c.get('userId');
      if (!userId) {
        return c.json({ error: 'Unauthorized' }, 401);
      }

      const { name } = await c.req.json();

      if (!name) {
        return c.json({ error: 'Organization name is required' }, 400);
      }

      const organization = await this.tenantService.createOrganization(name, userId);
      return c.json({ organization }, 201);
    } catch (error: any) {
      return c.json({ error: error.message }, 500);
    }
  }

  /**
   * Update an organization
   */
  @HttpPut('/:id')
  async updateOrganization(@HttpContext() c: Context): Promise<Response> {
    try {
      const userId = c.get('userId');
      if (!userId) {
        return c.json({ error: 'Unauthorized' }, 401);
      }

      const { id } = c.req.param();
      if (!id) {
        return c.json({ error: 'Organization ID is required' }, 400);
      }
      const { name } = await c.req.json();

      if (!name) {
        return c.json({ error: 'Organization name is required' }, 400);
      }

      const success = await this.tenantService.updateOrganization(id, name);

      if (!success) {
        return c.json({ error: 'Failed to update organization' }, 400);
      }

      return c.json({ success: true });
    } catch (error: any) {
      return c.json({ error: error.message }, 500);
    }
  }

  /**
   * Delete an organization
   */
  @HttpDelete('/:id')
  async deleteOrganization(@HttpContext() c: Context): Promise<Response> {
    try {
      const userId = c.get('userId');
      if (!userId) {
        return c.json({ error: 'Unauthorized' }, 401);
      }

      const { id } = c.req.param();
      if (!id) {
        return c.json({ error: 'Organization ID is required' }, 400);
      }
      const success = await this.tenantService.delete(id);

      if (!success) {
        return c.json({ error: 'Failed to delete organization' }, 400);
      }

      return c.json({ success: true });
    } catch (error: any) {
      return c.json({ error: error.message }, 500);
    }
  }

  /**
   * Find organizations by name
   */
  @HttpGet('/name/:name')
  async getOrganizationByName(@HttpContext() c: Context): Promise<Response> {
    try {
      const userId = c.get('userId');
      if (!userId) {
        return c.json({ error: 'Unauthorized' }, 401);
      }

      const { name } = c.req.param();
      if (!name) {
        return c.json({ error: 'Organization name is required' }, 400);
      }
      const organization = await this.tenantService.findByName(name);

      if (!organization) {
        return c.json({ error: 'Organization not found' }, 404);
      }

      return c.json({ organization });
    } catch (error: any) {
      return c.json({ error: error.message }, 500);
    }
  }

  /**
   * Find organizations created by a specific user
   */
  @HttpGet('/created-by/:createdBy')
  async getOrganizationsByCreator(@HttpContext() c: Context): Promise<Response> {
    try {
      const userId = c.get('userId');
      if (!userId) {
        return c.json({ error: 'Unauthorized' }, 401);
      }

      const { createdBy } = c.req.param();
      if (!createdBy) {
        return c.json({ error: 'Created by is required' }, 400);
      }
      const organizations = await this.tenantService.findByCreatedBy(createdBy);

      return c.json({ organizations });
    } catch (error: any) {
      return c.json({ error: error.message }, 500);
    }
  }
}
