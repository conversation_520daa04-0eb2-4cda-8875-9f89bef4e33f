import { Injectable, Repository } from '@c-cam/core';
import AuditTrailLogsModel, {
  AuditTrailLogsDocument,
} from '@/database/entities/AuditTrailLogsModel';

/**
 * Repository for managing audit trail logs
 * Extends the BaseRepository with AuditTrailLogsDocument type
 */
@Injectable()
class AuditTrailLogsRepository extends Repository<AuditTrailLogsDocument> {
  constructor() {
    super(AuditTrailLogsModel);
  }

  /**
   * Find audit logs by user ID
   * @param userId The user ID to search for
   * @returns A promise that resolves to an array of audit logs
   */
  async findByUserId(userId: string): Promise<AuditTrailLogsDocument[]> {
    return this.find({ user_id: userId });
  }

  /**
   * Find audit logs by module
   * @param module The module to search for
   * @returns A promise that resolves to an array of audit logs
   */
  async findByModule(module: string): Promise<AuditTrailLogsDocument[]> {
    return this.find({ module });
  }

  /**
   * Find audit logs by action
   * @param action The action to search for
   * @returns A promise that resolves to an array of audit logs
   */
  async findByAction(action: string): Promise<AuditTrailLogsDocument[]> {
    return this.find({ action });
  }

  /**
   * Find audit logs by date range
   * @param startDate The start date of the range
   * @param endDate The end date of the range
   * @returns A promise that resolves to an array of audit logs
   */
  async findByDateRange(
    startDate: Date,
    endDate: Date,
  ): Promise<AuditTrailLogsDocument[]> {
    return this.find({
      created_at: { $gte: startDate, $lte: endDate },
    });
  }

  /**
   * Find audit logs by user ID and date range
   * @param userId The user ID to search for
   * @param startDate The start date of the range
   * @param endDate The end date of the range
   * @returns A promise that resolves to an array of audit logs
   */
  async findByUserIdAndDateRange(
    userId: string,
    startDate: Date,
    endDate: Date,
  ): Promise<AuditTrailLogsDocument[]> {
    return this.find({
      user_id: userId,
      created_at: { $gte: startDate, $lte: endDate },
    });
  }

  /**
   * Find audit logs by module, action, and date range
   * @param module The module to search for
   * @param action The action to search for
   * @param startDate The start date of the range
   * @param endDate The end date of the range
   * @returns A promise that resolves to an array of audit logs
   */
  async findByModuleActionAndDateRange(
    module: string,
    action: string,
    startDate: Date,
    endDate: Date,
  ): Promise<AuditTrailLogsDocument[]> {
    return this.find({
      module,
      action,
      created_at: { $gte: startDate, $lte: endDate },
    });
  }
}

export default AuditTrailLogsRepository;
