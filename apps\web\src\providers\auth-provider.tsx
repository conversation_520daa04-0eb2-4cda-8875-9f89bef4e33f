import React, { useEffect } from 'react'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { AuthProvider } from '@/contexts/auth-context'
import { useIdentityActions } from '@/hooks/use-identity-actions'

// Create a client
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 1,
      refetchOnWindowFocus: false,
      staleTime: 5 * 60 * 1000, // 5 minutes
    },
    mutations: {
      retry: 1,
    },
  },
})

interface AppProvidersProps {
  children: React.ReactNode
}

/**
 * Auth integration component that connects auth actions to global refresh
 */
const AuthIntegration: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const authActions = useIdentityActions()

  // Expose auth refresh function globally for synchronization
  useEffect(() => {
    ;(window as any).__authRefreshToken = authActions.refreshAccessToken
  }, [authActions.refreshAccessToken])

  return <>{children}</>
}

/**
 * Root provider component that wraps the app with all necessary providers
 * Structure: QueryClient > Auth > AuthIntegration > children
 */
export const AppProviders: React.FC<AppProvidersProps> = ({ children }) => {
  return (
    <QueryClientProvider client={queryClient}>
      <AuthProvider>
        <AuthIntegration>
          {children}
        </AuthIntegration>
      </AuthProvider>
    </QueryClientProvider>
  )
}

export default AppProviders
