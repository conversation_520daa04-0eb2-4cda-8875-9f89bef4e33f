/**
 * Test file to verify signed cookies functionality
 * This test demonstrates the usage of signed cookies with Hono
 */

import { Hono } from 'hono';
import { 
  getSignedCookie, 
  setSignedCookie, 
  deleteCookie 
} from 'hono/cookie';

// Test secret (in production, use environment.jwt.secret)
const TEST_SECRET = 'test-secret-for-signed-cookies-123';

// Create a test Hono app
const testApp = new Hono();

// Test route to set a signed cookie
testApp.post('/test/set-signed-cookie', async (c) => {
  const { name, value } = await c.req.json();
  
  await setSignedCookie(c, name, value, TEST_SECRET, {
    httpOnly: true,
    secure: true,
    sameSite: 'Strict',
    path: '/',
    maxAge: 3600, // 1 hour
  });
  
  return c.json({ success: true, message: 'Signed cookie set' });
});

// Test route to get a signed cookie
testApp.get('/test/get-signed-cookie/:name', async (c) => {
  const name = c.req.param('name');
  
  const value = await getSignedCookie(c, TEST_SECRET, name);
  
  if (value === false) {
    return c.json({ 
      success: false, 
      message: '<PERSON>ie signature is invalid or tampered' 
    }, 400);
  }
  
  if (value === undefined) {
    return c.json({ 
      success: false, 
      message: 'Cookie not found' 
    }, 404);
  }
  
  return c.json({ 
    success: true, 
    value,
    message: 'Signed cookie retrieved successfully' 
  });
});

// Test route to delete a signed cookie
testApp.delete('/test/delete-signed-cookie/:name', async (c) => {
  const name = c.req.param('name');
  
  deleteCookie(c, name, {
    path: '/',
  });
  
  return c.json({ 
    success: true, 
    message: 'Signed cookie deleted' 
  });
});

// Test route to demonstrate cookie security
testApp.get('/test/cookie-security-demo', async (c) => {
  // Set multiple cookies with different security levels
  
  // Regular cookie (not recommended for sensitive data)
  c.header('Set-Cookie', 'regular_cookie=regular_value; Path=/');
  
  // Signed cookie (recommended for sensitive data)
  await setSignedCookie(c, 'signed_cookie', 'sensitive_value', TEST_SECRET, {
    httpOnly: true,
    secure: true,
    sameSite: 'Strict',
    path: '/',
    maxAge: 3600,
  });
  
  return c.json({
    message: 'Cookies set for security demonstration',
    explanation: {
      regular_cookie: 'Can be easily read and modified by client',
      signed_cookie: 'Protected by HMAC signature, tampering will be detected'
    }
  });
});

export { testApp, TEST_SECRET };

/**
 * Usage examples:
 * 
 * 1. Set a signed cookie:
 *    POST /test/set-signed-cookie
 *    Body: { "name": "user_session", "value": "abc123" }
 * 
 * 2. Get a signed cookie:
 *    GET /test/get-signed-cookie/user_session
 * 
 * 3. Delete a signed cookie:
 *    DELETE /test/delete-signed-cookie/user_session
 * 
 * 4. Security demonstration:
 *    GET /test/cookie-security-demo
 */
