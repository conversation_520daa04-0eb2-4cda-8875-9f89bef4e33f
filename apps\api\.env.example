# Environment Configuration
# Cấu hình Môi trường
NODE_ENV=DEVELOPMENT

# Server Configuration
# Cấu hình Server
DEVELOPMENT_SERVER_HOST=localhost
DEVELOPMENT_SERVER_PORT=5000

# Database Configuration (MongoDB)
# Cấu hình Cơ sở dữ liệu (MongoDB)
DEVELOPMENT_MONGO_CONNECTION_TEMPLATE=${protocol}://${username}:${password}@${host}/${database}?retryWrites=${retryWrites}
DEVELOPMENT_MONGO_PROTOCOL=mongodb+srv
DEVELOPMENT_MONGO_HOST=csmart.0nhat1q.mongodb.net
DEVELOPMENT_MONGO_PORT=27017
DEVELOPMENT_MONGO_USERNAME=csmart
DEVELOPMENT_MONGO_PASSWORD=c9AiflkH6zUwjUXf
DEVELOPMENT_MONGO_DATABASE_NAME=c-storage
DEVELOPMENT_MONGO_RETRY_WRITES=true

# Extended MongoDB Configuration (Optional)
# Cấu hình MongoDB Mở rộng (Tùy chọn)
DEVELOPMENT_MONGO_MAX_POOL_SIZE=10
DEVELOPMENT_MONGO_MIN_POOL_SIZE=2
DEVELOPMENT_MONGO_MAX_IDLE_TIME_MS=30000
DEVELOPMENT_MONGO_CONNECT_TIMEOUT_MS=10000
DEVELOPMENT_MONGO_SERVER_SELECTION_TIMEOUT_MS=5000
DEVELOPMENT_MONGO_SOCKET_TIMEOUT_MS=45000
DEVELOPMENT_MONGO_READ_PREFERENCE=primary
DEVELOPMENT_MONGO_WRITE_CONCERN_W=majority
DEVELOPMENT_MONGO_WRITE_CONCERN_J=true
DEVELOPMENT_MONGO_WRITE_CONCERN_WTIMEOUT=10000
DEVELOPMENT_MONGO_READ_CONCERN_LEVEL=majority
DEVELOPMENT_MONGO_HEALTH_CHECK_ENABLED=true
DEVELOPMENT_MONGO_HEALTH_CHECK_INTERVAL=30000
DEVELOPMENT_MONGO_HEALTH_CHECK_TIMEOUT=5000
DEVELOPMENT_MONGO_MONITORING_ENABLED=true
DEVELOPMENT_MONGO_COMMAND_MONITORING=true
DEVELOPMENT_MONGO_POOL_MONITORING=true

# Redis Configuration (for BullMQ)
# Cấu hình Redis (cho BullMQ)
DEVELOPMENT_REDIS_HOST=localhost
DEVELOPMENT_REDIS_PORT=6379
DEVELOPMENT_REDIS_PASSWORD=
DEVELOPMENT_REDIS_DB=0
DEVELOPMENT_REDIS_MAX_RETRIES_PER_REQUEST=3

# JWT Configuration
# Cấu hình JWT
DEVELOPMENT_JWT_SECRET=your-super-secret-jwt-key-here
DEVELOPMENT_JWT_EXPIRES_IN=7d

# Storage Configuration
# Cấu hình Lưu trữ
DEVELOPMENT_STORAGE_TYPE=local
DEVELOPMENT_STORAGE_PATH=./storage
DEVELOPMENT_MAX_FILE_SIZE=100MB

# BullMQ Configuration
# Cấu hình BullMQ
DEVELOPMENT_BULLMQ_DEFAULT_JOB_OPTIONS_REMOVE_ON_COMPLETE=10
DEVELOPMENT_BULLMQ_DEFAULT_JOB_OPTIONS_REMOVE_ON_FAIL=50
DEVELOPMENT_BULLMQ_DEFAULT_JOB_OPTIONS_ATTEMPTS=3
DEVELOPMENT_BULLMQ_DEFAULT_JOB_OPTIONS_BACKOFF_TYPE=exponential
DEVELOPMENT_BULLMQ_DEFAULT_JOB_OPTIONS_BACKOFF_DELAY=2000

# File Upload Limits
# Giới hạn Tải lên File
DEVELOPMENT_MAX_VIDEO_FILE_SIZE=2GB
DEVELOPMENT_ALLOWED_VIDEO_FORMATS=mp4,avi,mov,mkv,webm,flv,wmv,m4v

# Admin Configuration
# Cấu hình Admin
DEVELOPMENT_ADMIN_EMAIL=<EMAIL>
DEVELOPMENT_ADMIN_PASSWORD=admin123

# Logging Configuration
# Cấu hình Logging
DEVELOPMENT_LOG_LEVEL=info
DEVELOPMENT_LOG_FILE=./logs/app.log

# CORS Configuration
# Cấu hình CORS
DEVELOPMENT_CORS_ORIGIN=http://localhost:3000,http://localhost:3001,http://localhost:5173

# Rate Limiting
# Giới hạn Tốc độ
DEVELOPMENT_RATE_LIMIT_WINDOW_MS=900000
DEVELOPMENT_RATE_LIMIT_MAX_REQUESTS=100

# Monitoring and Health Check
# Giám sát và Kiểm tra Sức khỏe
DEVELOPMENT_HEALTH_CHECK_INTERVAL=30000
DEVELOPMENT_METRICS_ENABLED=true

# Optional: FFmpeg Path (uncomment if needed)
# Tùy chọn: Đường dẫn FFmpeg (bỏ comment nếu cần)
# DEVELOPMENT_FFMPEG_PATH=/usr/local/bin/ffmpeg

# Optional: GPU Device Index (for multi-GPU systems)
# Tùy chọn: Chỉ số Thiết bị GPU (cho hệ thống đa GPU)
# DEVELOPMENT_GPU_DEVICE_INDEX=0

# Optional: Email Notifications
# Tùy chọn: Thông báo Email
# DEVELOPMENT_SMTP_HOST=smtp.gmail.com
# DEVELOPMENT_SMTP_PORT=587
# DEVELOPMENT_SMTP_USER=<EMAIL>
# DEVELOPMENT_SMTP_PASS=your-app-password
# DEVELOPMENT_NOTIFICATION_EMAIL=<EMAIL>

# Optional: Webhook Notifications
# Tùy chọn: Thông báo Webhook
# DEVELOPMENT_WEBHOOK_URL=https://your-webhook-endpoint.com/video-processing
# DEVELOPMENT_WEBHOOK_SECRET=your-webhook-secret

# Production Environment Example (uncomment and modify for production)
# Ví dụ Môi trường Production (bỏ comment và sửa đổi cho production)
# NODE_ENV=PRODUCTION
# PRODUCTION_SERVER_HOST=0.0.0.0
# PRODUCTION_SERVER_PORT=3000
# PRODUCTION_MONGO_PROTOCOL=mongodb+srv
# PRODUCTION_MONGO_HOST=your-production-mongodb-host.mongodb.net
# PRODUCTION_MONGO_USERNAME=your-production-username
# PRODUCTION_MONGO_PASSWORD=your-production-password
# PRODUCTION_MONGO_DATABASE_NAME=c-storage-prod
# PRODUCTION_VIDEO_OUTPUT_DIR=/var/video_output
# PRODUCTION_MAX_CONCURRENT_VIDEO_JOBS=4
# PRODUCTION_VIDEO_CONCURRENCY_LIMIT=4
# PRODUCTION_ENABLE_HARDWARE_ACCELERATION=true
# PRODUCTION_PREFERRED_HARDWARE_ACCELERATION=auto
# PRODUCTION_VIDEO_CLEANUP_DAYS=7
# PRODUCTION_DEFAULT_VIDEO_CRF=23
# PRODUCTION_DEFAULT_VIDEO_PRESET=medium
# PRODUCTION_CORS_ORIGIN=https://your-domain.com
# PRODUCTION_RATE_LIMIT_MAX_REQUESTS=1000
# PRODUCTION_METRICS_ENABLED=true
