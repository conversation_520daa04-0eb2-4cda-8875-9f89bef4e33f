# Enhanced getCurrentUser Implementation

## Overview

The `getCurrentUser` method in `ControllerBase` has been enhanced to parse access tokens from both Authorization headers and signed cookies, with built-in user validation from the database.

## Key Features

### 1. **Multiple Token Sources**
- **Authorization Header**: `Bearer <token>`
- **Signed Cookie**: `access_token` cookie with HMAC signature
- **Fallback Support**: Tries header first, then cookie

### 2. **Token Verification**
- JWT signature validation using environment secret
- Token expiration checking
- Database token validation (if service available)

### 3. **User Validation**
- Verifies user exists in database
- Returns complete user profile
- Caches user data in request context

### 4. **Security Features**
- Signed cookies prevent tampering
- Automatic ClaimsPrincipal creation
- Error handling and logging

## Implementation Details

### Method Signature
```typescript
protected async getCurrentUser(req: Request | Context): Promise<any | null>
```

### Token Resolution Flow

1. **Check Existing User**
   ```typescript
   const existingUser = (req as any).user;
   if (existingUser) return existingUser;
   ```

2. **Authorization Header**
   ```typescript
   const authHeader = req.header?.('Authorization');
   if (authHeader && authHeader.startsWith('Bearer ')) {
     token = authHeader.substring(7);
   }
   ```

3. **Signed Cookie Fallback**
   ```typescript
   if (!token && 'get' in req) {
     const context = req as Context;
     const accessTokenFromCookie = await getSignedCookie(
       context, 
       this.getJwtSecret(), 
       'access_token'
     );
     if (accessTokenFromCookie) token = accessTokenFromCookie;
   }
   ```

4. **Token Verification**
   ```typescript
   const payload = await this.verifyAndDecodeToken(token);
   ```

5. **User Validation**
   ```typescript
   const user = await this.validateUserFromToken(payload);
   ```

6. **Context Attachment**
   ```typescript
   const claimsPrincipal = createClaimsPrincipalFromJwt(payload);
   (req as any).claimsPrincipal = claimsPrincipal;
   (req as any).user = { ...payload, ...user };
   ```

## Override Methods

### getJwtSecret()
```typescript
protected getJwtSecret(): string | null {
  return environment.jwt.secret;
}
```

### verifyAndDecodeToken()
```typescript
protected async verifyAndDecodeToken(token: string): Promise<any | null> {
  return this.identityService.verifyToken(token);
}
```

### validateUserFromToken()
```typescript
protected async validateUserFromToken(payload: any): Promise<any | null> {
  const user = await this.identityService.getUserById(payload.sub);
  return user;
}
```

## Usage Examples

### Basic Usage
```typescript
@HttpGet('/me')
async getMe(@HttpContext() context: Context): Promise<Response> {
  const currentUser = await this.getCurrentUser(context.req);
  if (!currentUser) {
    throw new UnauthorizedError('Authentication required');
  }
  
  return this.success(context, { user: currentUser });
}
```

### With Authorization Header
```bash
curl -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..." \
     http://localhost:5000/api/identity/me
```

### With Signed Cookie
```bash
# First set the cookie
curl -X POST http://localhost:5000/api/identity/login \
     -d '{"username":"user","password":"pass"}' \
     -H "Content-Type: application/json"

# Then access protected endpoint (cookie automatically included)
curl -b cookies.txt http://localhost:5000/api/identity/me
```

## Security Benefits

### Before Enhancement
```typescript
// Only supported middleware-attached user
protected getCurrentUser(req: Request): any | null {
  return (req as any).user || null;
}
```

### After Enhancement
```typescript
// Supports multiple token sources with validation
protected async getCurrentUser(req: Request | Context): Promise<any | null> {
  // 1. Check existing user
  // 2. Parse from Authorization header
  // 3. Parse from signed cookie
  // 4. Verify JWT token
  // 5. Validate user in database
  // 6. Create ClaimsPrincipal
  // 7. Cache in request context
}
```

## Error Handling

The method handles various error scenarios gracefully:

- **Invalid Token**: Returns `null`
- **Expired Token**: Returns `null`
- **User Not Found**: Returns `null`
- **Database Error**: Logs error, returns `null`
- **Cookie Tampering**: Returns `null` (signature validation fails)

## Performance Considerations

1. **Caching**: User data is cached in request context
2. **Lazy Loading**: Only validates when needed
3. **Service Discovery**: Uses DI container to find services
4. **Fallback Logic**: Minimal overhead when services unavailable

## Testing

Use the test file `apps/api/src/tests/current-user.test.ts` to verify functionality:

```bash
# Test with Authorization header
curl -H "Authorization: Bearer valid-token" \
     http://localhost:5000/test/current-user-header

# Test with signed cookie
curl -X POST http://localhost:5000/test/set-access-token \
     -d '{"token":"valid-token"}' && \
curl -b cookies.txt http://localhost:5000/test/current-user-cookie
```

## Migration Notes

### Breaking Changes
- `getCurrentUser` is now `async` - requires `await`
- Method signature accepts `Request | Context`

### Backward Compatibility
- Existing middleware-attached users still work
- Graceful fallback when services unavailable

### Update Required
```typescript
// Before
const userId = this.getCurrentUser(context.req)?.id;

// After
const currentUser = await this.getCurrentUser(context.req);
const userId = currentUser?.id;
```
