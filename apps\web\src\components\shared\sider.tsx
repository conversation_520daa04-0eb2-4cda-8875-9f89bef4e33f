import { ChevronRight } from 'lucide-react'
import type { LucideIcon } from 'lucide-react'

import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '@/components/ui/collapsible'
import {
  SidebarGroup,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
} from '@/components/ui/sidebar'

export interface NavigationItem {
  title: string
  url: string
  icon?: React.FC | LucideIcon
  isActive?: boolean
  items?: Array<NavigationItem>
}

export function SiderLayout({ items }: { items: Array<NavigationItem> }) {
  return (
    <SidebarGroup>
      <SidebarGroupLabel>QUẢN TRỊ HỆ THỐNG</SidebarGroupLabel>
      <SidebarMenu>
        {items.map(({ title, url, icon: Icon, isActive, items: children }) =>
          children?.length ? (
            <Collapsible
              key={title}
              asChild
              defaultOpen={isActive}
              className="group/collapsible"
            >
              <SidebarMenuItem>
                <CollapsibleTrigger asChild>
                  <SidebarMenuButton tooltip={title}>
                    {Icon && <Icon className="w-4 h-4" />}
                    <span>{title}</span>
                    <ChevronRight className="ml-auto transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90" />
                  </SidebarMenuButton>
                </CollapsibleTrigger>
                <CollapsibleContent>
                  <SidebarMenuSub>
                    {children.map((sub) => (
                      <SidebarMenuSubItem key={sub.title}>
                        <SidebarMenuSubButton asChild>
                          <a href={sub.url}>{sub.title}</a>
                        </SidebarMenuSubButton>
                      </SidebarMenuSubItem>
                    ))}
                  </SidebarMenuSub>
                </CollapsibleContent>
              </SidebarMenuItem>
            </Collapsible>
          ) : (
            <SidebarMenuItem key={title}>
              <SidebarMenuButton asChild tooltip={title}>
                <a href={url} className="flex items-center w-full">
                  {Icon && <Icon className="w-4 h-4" />}
                  <span>{title}</span>
                </a>
              </SidebarMenuButton>
            </SidebarMenuItem>
          ),
        )}
      </SidebarMenu>
    </SidebarGroup>
  )
}
