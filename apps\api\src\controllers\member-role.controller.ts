import { Context } from 'hono';
import {
  Controller,
  ControllerBase,
  HttpContext,
  HttpGet,
  HttpPost,
  HttpDelete,
  Inject,
  Param,
  Query,
  UnauthorizedError,
} from '@c-cam/core';
import MemberRoleService from '../services/MemberRoleService';

@Controller('/api/member-roles')
export class MemberRoleController extends ControllerBase {
  constructor(
    @Inject(MemberRoleService) private memberRoleService: MemberRoleService,
  ) {
    super();
  }

  /**
   * Helper method to get authenticated user ID
   */
  private getAuthenticatedUserId(c: Context): string {
    const userId = c.get('userId');
    if (!userId) {
      throw new UnauthorizedError('You must be authenticated to access this resource');
    }
    return userId;
  }

  /**
   * Get all member roles
   */
  @HttpGet('/')
  async getMemberRoles(
    @HttpContext() c: Context,
    @Query('limit') limit?: string,
    @Query('skip') skip?: string,
    @Query('sortBy') sortBy?: string,
    @Query('sortDirection') sortDirection?: string,
  ): Promise<Response> {
    this.getAuthenticatedUserId(c);

    const memberRoles = await this.memberRoleService.find({
      limit: limit ? parseInt(limit) : undefined,
      skip: skip ? parseInt(skip) : undefined,
      sortBy,
      sortDirection,
    });

    return this.success(c, { memberRoles });
  }

  /**
   * Get a member role by ID
   */
  @HttpGet('/:id')
  async getMemberRoleById(
    @HttpContext() c: Context,
    @Param('id') id: string,
  ): Promise<Response> {
    this.getAuthenticatedUserId(c);
    this.validateIf(!id, 'Member role ID is required');

    const memberRole = await this.memberRoleService.findById(id);
    this.notFoundIf(!memberRole, 'Member role not found');

    return this.success(c, { memberRole });
  }

  /**
   * Assign a role to a user
   */
  @HttpPost('/assign')
  async assignRoleToUser(@HttpContext() c: Context): Promise<Response> {
    const userId = this.getAuthenticatedUserId(c);
    const { userIdToAssign, roleId } = await c.req.json();

    // Validate required fields
    this.validateRequiredFields(
      { userIdToAssign, roleId },
      ['userIdToAssign', 'roleId']
    );

    const memberRole = await this.memberRoleService.assignRoleToUser(
      userIdToAssign,
      roleId,
      userId
    );

    return this.created(c, { memberRole }, 'Role assigned to user successfully');
  }

  /**
   * Remove a role from a user
   */
  @HttpDelete('/user/:userId/role/:roleId')
  async removeRoleFromUser(
    @HttpContext() c: Context,
    @Param('userId') userId: string,
    @Param('roleId') roleId: string,
  ): Promise<Response> {
    this.getAuthenticatedUserId(c);
    this.validateIf(!userId || !roleId, 'User ID and Role ID are required');

    const success = await this.memberRoleService.removeRoleFromUser(userId, roleId);
    this.validateIf(!success, 'Failed to remove role from user');

    return this.success(c, { success: true }, 'Role removed from user successfully');
  }

  /**
   * Get all roles for a user
   */
  @HttpGet('/user/:userId')
  async getRolesForUser(
    @HttpContext() c: Context,
    @Param('userId') userId: string,
  ): Promise<Response> {
    this.getAuthenticatedUserId(c);
    this.validateIf(!userId, 'User ID is required');

    const memberRoles = await this.memberRoleService.getRolesForUser(userId);
    return this.success(c, { memberRoles });
  }

  /**
   * Get all users with a specific role
   */
  @HttpGet('/role/:roleId')
  async getUsersWithRole(
    @HttpContext() c: Context,
    @Param('roleId') roleId: string,
  ): Promise<Response> {
    this.getAuthenticatedUserId(c);
    this.validateIf(!roleId, 'Role ID is required');

    const memberRoles = await this.memberRoleService.getUsersWithRole(roleId);
    return this.success(c, { memberRoles });
  }

  /**
   * Remove all roles from a user
   */
  @HttpDelete('/user/:userId/roles')
  async removeAllRolesFromUser(
    @HttpContext() c: Context,
    @Param('userId') userId: string,
  ): Promise<Response> {
    this.getAuthenticatedUserId(c);
    this.validateIf(!userId, 'User ID is required');

    const success = await this.memberRoleService.removeAllRolesFromUser(userId);
    this.validateIf(!success, 'Failed to remove roles from user');

    return this.success(c, { success: true }, 'All roles removed from user successfully');
  }

  /**
   * Remove a role from all users
   */
  @HttpDelete('/role/:roleId/users')
  async removeRoleFromAllUsers(
    @HttpContext() c: Context,
    @Param('roleId') roleId: string,
  ): Promise<Response> {
    this.getAuthenticatedUserId(c);
    this.validateIf(!roleId, 'Role ID is required');

    const success = await this.memberRoleService.removeRoleFromAllUsers(roleId);
    this.validateIf(!success, 'Failed to remove role from users');

    return this.success(c, { success: true }, 'Role removed from all users successfully');
  }
}
