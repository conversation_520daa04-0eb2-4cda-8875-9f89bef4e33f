# Secure Authentication Implementation

## Overview

Hệ thống xác thực đã được chuẩn hóa theo các tiêu chuẩn bảo mật cao nhất, bao gồm:

- **Rotating Refresh Tokens**: Chống tấn công token reuse
- **HttpOnly Cookies**: Bảo vệ chống XSS attacks
- **Memory Storage**: Access tokens được lưu trong memory thay vì localStorage
- **Auto Token Refresh**: Tự động refresh trước khi hết hạn
- **Security Tracking**: Theo dõi và phát hiện các cuộc tấn công

## Architecture Changes

### 1. Database Schema Updates (`apps/api/src/database/entities/TokenModel.ts`)

**New Fields Added:**
```typescript
refresh_expires_at: Date        // Thời hạn của refresh token (7 ngày)
last_used_at?: Date            // Lần cuối sử dụng token
rotation_count: number         // Số lần token đã được rotate
parent_token_id?: string       // ID của token cha (để tracking rotation)
is_compromised: boolean        // Đ<PERSON>h dấu token bị compromise
compromised_at?: Date          // Thời điểm phát hiện compromise
session_id: string             // ID phiên làm việc
user_agent?: string            // User agent của client
```

### 2. Enhanced Token Repository (`apps/api/src/repositories/TokenRepository.ts`)

**New Methods:**
- `revokeRefreshToken()`: Revoke refresh token
- `markTokenAsCompromised()`: Đánh dấu token bị compromise và revoke toàn bộ chain
- `isRefreshTokenReused()`: Phát hiện token reuse attack
- `getActiveSessions()`: Lấy danh sách phiên hoạt động
- `updateLastUsed()`: Cập nhật thời gian sử dụng cuối

### 3. Secure Token Service (`apps/api/src/services/IdentityService.ts`)

**Enhanced Features:**
- **Rotating Refresh Tokens**: Mỗi lần refresh sẽ tạo token mới và revoke token cũ
- **Token Reuse Detection**: Phát hiện và xử lý khi refresh token bị reuse
- **Security Logging**: Log các hoạt động bảo mật
- **IP/User-Agent Validation**: Kiểm tra thông tin client

### 4. Secure API Endpoints (`apps/api/src/controllers/identity.controller.ts`)

**Updated Endpoints:**

#### POST `/api/identity/login`
- **Response**: Chỉ trả về access token trong body
- **Cookies**: Refresh token được set trong HttpOnly cookie
- **Security**: Secure, SameSite=Strict, 7 ngày expiry

#### POST `/api/identity/refresh-token`
- **Input**: Refresh token từ HttpOnly cookie (không qua body)
- **Response**: Access token mới trong body
- **Cookies**: Refresh token mới trong HttpOnly cookie
- **Security**: Rotating refresh tokens, revoke token cũ

#### POST `/api/identity/logout`
- **Action**: Revoke cả access token và refresh token
- **Cookies**: Clear refresh token cookie

## Frontend Security Implementation

### 1. Secure Token Storage (`apps/web/src/utils/auth.ts`)

**Memory Storage for Access Tokens:**
```typescript
// In-memory storage (không thể bị XSS tấn công)
let accessTokenMemory: string | null = null

export const setAccessToken = (token: string) => {
  accessTokenMemory = token
}

export const getAccessToken = (): string | null => {
  return accessTokenMemory
}
```

**No Refresh Token Storage:**
- Refresh tokens chỉ tồn tại trong HttpOnly cookies
- JavaScript không thể truy cập được

### 2. Separated Hooks Architecture

#### `use-identity-query.ts` - API Interactions Only
- Chỉ chứa các React Query hooks để gọi API
- Không chứa business logic
- Sử dụng `withCredentials: true` cho HttpOnly cookies

#### `use-identity-actions.ts` - UI Actions & Logic
- Chứa toàn bộ business logic cho auth
- Auto refresh token scheduling
- Error handling và navigation
- State management

### 3. Auto Token Refresh (`apps/web/src/hooks/use-identity-actions.ts`)

**Features:**
- **Proactive Refresh**: Tự động refresh 2 phút trước khi hết hạn
- **JWT Decode**: Decode token để lấy expiry time
- **Timeout Scheduling**: Sử dụng setTimeout để schedule refresh
- **Error Handling**: Logout user nếu refresh fail

### 4. Secure Axios Configuration (`apps/web/src/configs/axios.ts`)

**Enhanced Interceptors:**
- **Request**: Tự động thêm Authorization header từ memory
- **Response**: Handle 401 errors với auto refresh
- **Queue Management**: Queue requests khi đang refresh
- **Credentials**: `withCredentials: true` cho tất cả requests

## Security Features

### 1. Token Rotation & Reuse Detection

**How it works:**
1. Mỗi lần refresh token được sử dụng, nó sẽ bị revoke
2. Token mới được tạo với `parent_token_id` trỏ đến token cũ
3. Nếu token cũ được sử dụng lại → phát hiện attack
4. Toàn bộ token chain sẽ bị revoke

### 2. XSS Protection

**Multiple Layers:**
- Access tokens trong memory (không thể steal qua XSS)
- Refresh tokens trong HttpOnly cookies (JavaScript không truy cập được)
- SameSite=Strict cookies (chống CSRF)
- Secure cookies (chỉ qua HTTPS)

### 3. Session Management

**Features:**
- Mỗi login tạo unique session ID
- Track multiple devices/sessions
- Logout from all devices functionality
- Session expiry management

### 4. Security Monitoring

**Logging & Tracking:**
- Token rotation events
- Failed refresh attempts
- Suspicious activities (IP changes, etc.)
- Compromise detection

## Usage Examples

### 1. Login Flow
```typescript
const { login } = useIdentityActions()

await login({
  username: '<EMAIL>',
  password: 'password123'
})
// Access token stored in memory
// Refresh token stored in HttpOnly cookie
// Auto refresh scheduled
```

### 2. Protected Routes
```typescript
function ProtectedPage() {
  const { isAuthenticated, isLoading } = useIdentityActions()

  if (isLoading) return <Loading />
  if (!isAuthenticated) return <Redirect to="/login" />

  return <SecureContent />
}
```

### 3. API Calls
```typescript
// Automatically includes access token from memory
const response = await axiosClient.get('/api/protected-endpoint')
// Auto refresh if 401 error
// Queue requests during refresh
```

## Migration Guide

### From Old System:
1. **Remove localStorage token storage**
2. **Update login flow** to handle HttpOnly cookies
3. **Replace auth context** with new hooks
4. **Update API calls** to use new axios client
5. **Test token refresh flow**

### Database Migration:
```sql
-- Add new fields to tokens collection
db.tokens.updateMany({}, {
  $set: {
    refresh_expires_at: new Date(Date.now() + 7*24*60*60*1000),
    rotation_count: 0,
    is_compromised: false,
    session_id: new ObjectId().toString()
  }
})
```

## Testing

### Security Tests:
1. **Token Reuse Attack**: Verify old refresh tokens are rejected
2. **XSS Simulation**: Confirm tokens not accessible via JavaScript
3. **CSRF Protection**: Test SameSite cookie behavior
4. **Auto Refresh**: Verify proactive token refresh
5. **Session Management**: Test multi-device scenarios

### Performance Tests:
1. **Memory Usage**: Monitor access token memory footprint
2. **Request Queuing**: Test concurrent requests during refresh
3. **Timeout Handling**: Verify proper cleanup of timeouts

## Security Checklist

- ✅ Access tokens stored in memory only
- ✅ Refresh tokens in HttpOnly cookies only
- ✅ Rotating refresh tokens implemented
- ✅ Token reuse detection active
- ✅ Auto refresh before expiry
- ✅ Secure cookie settings (HttpOnly, Secure, SameSite)
- ✅ Session tracking and management
- ✅ Security event logging
- ✅ IP/User-Agent validation
- ✅ Compromise detection and response

## Monitoring & Alerts

### Key Metrics:
- Token refresh success rate
- Failed authentication attempts
- Token reuse detection events
- Session duration statistics
- Security incident frequency

### Alert Conditions:
- Multiple failed refresh attempts
- Token reuse detection
- Unusual session patterns
- High number of compromised tokens
