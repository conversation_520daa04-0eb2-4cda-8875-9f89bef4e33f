import FaceRecognitionLogsModel, {
  FaceRecognitionLogsDocument,
} from '@/database/entities/FaceRecognitionLogsModel';
import { Repository, Injectable } from '@c-cam/core';

/**
 * Repository for managing face recognition logs
 * Extends the BaseRepository with FaceRecognitionLogsDocument type
 */
@Injectable()
class FaceRecognitionLogsRepository extends Repository<FaceRecognitionLogsDocument> {
  constructor() {
    super(FaceRecognitionLogsModel);
  }

  /**
   * Find face recognition logs by user ID
   * @param userId The user ID to search for
   * @returns A promise that resolves to an array of face recognition logs
   */
  async findByUserId(userId: string): Promise<FaceRecognitionLogsDocument[]> {
    return this.find({ user_id: userId });
  }

  /**
   * Find face recognition logs by camera ID
   * @param cameraId The camera ID to search for
   * @returns A promise that resolves to an array of face recognition logs
   */
  async findByCameraId(
    cameraId: string,
  ): Promise<FaceRecognitionLogsDocument[]> {
    return this.find({ camera_id: cameraId });
  }

  /**
   * Find face recognition logs by edge device ID
   * @param edgeDeviceId The edge device ID to search for
   * @returns A promise that resolves to an array of face recognition logs
   */
  async findByEdgeDeviceId(
    edgeDeviceId: string,
  ): Promise<FaceRecognitionLogsDocument[]> {
    return this.find({ edge_device_id: edgeDeviceId });
  }

  /**
   * Find face recognition logs by recognition status
   * @param recognitionStatus The recognition status to search for
   * @returns A promise that resolves to an array of face recognition logs
   */
  async findByRecognitionStatus(
    recognitionStatus: string,
  ): Promise<FaceRecognitionLogsDocument[]> {
    return this.find({ recognition_status: recognitionStatus });
  }

  /**
   * Find face recognition logs by confidence score range
   * @param minConfidence The minimum confidence score
   * @param maxConfidence The maximum confidence score
   * @returns A promise that resolves to an array of face recognition logs
   */
  async findByConfidenceRange(
    minConfidence: number,
    maxConfidence: number,
  ): Promise<FaceRecognitionLogsDocument[]> {
    return this.find({
      confidence_score: { $gte: minConfidence, $lte: maxConfidence },
    });
  }

  /**
   * Find face recognition logs by date range
   * @param startDate The start date of the range
   * @param endDate The end date of the range
   * @returns A promise that resolves to an array of face recognition logs
   */
  async findByDateRange(
    startDate: Date,
    endDate: Date,
  ): Promise<FaceRecognitionLogsDocument[]> {
    return this.find({
      created_at: { $gte: startDate, $lte: endDate },
    });
  }

  /**
   * Find face recognition logs by user ID and date range
   * @param userId The user ID to search for
   * @param startDate The start date of the range
   * @param endDate The end date of the range
   * @returns A promise that resolves to an array of face recognition logs
   */
  async findByUserIdAndDateRange(
    userId: string,
    startDate: Date,
    endDate: Date,
  ): Promise<FaceRecognitionLogsDocument[]> {
    return this.find({
      user_id: userId,
      created_at: { $gte: startDate, $lte: endDate },
    });
  }
}

export default FaceRecognitionLogsRepository;
