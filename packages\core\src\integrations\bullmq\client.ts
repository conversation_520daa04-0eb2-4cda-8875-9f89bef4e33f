import {
  Queue,
  Worker,
  Job,
  JobsOptions,
  QueueOptions,
  WorkerOptions,
} from 'bullmq';
import { BullMQConnection, JobProcessor, JobEventHandlers } from './bullmq.js';
import { logger } from '@c-cam/logger';

/**
 * BullMQ client options
 */
export interface BullMQClientOptions {
  /**
   * Queue name prefix for all operations
   */
  queuePrefix?: string;

  /**
   * Default job options
   */
  defaultJobOptions?: Partial<JobsOptions>;

  /**
   * Default queue options
   */
  defaultQueueOptions?: Partial<QueueOptions>;

  /**
   * Default worker options
   */
  defaultWorkerOptions?: Partial<WorkerOptions>;
}

/**
 * Job data interface
 */
export interface JobData {
  /**
   * Job type identifier
   */
  type: string;

  /**
   * Job payload data
   */
  payload: any;

  /**
   * Optional job ID
   */
  id?: string;

  /**
   * Optional metadata
   */
  metadata?: Record<string, any>;
}

/**
 * BullMQ client interface
 */
export interface IBullMQClient {
  /**
   * Create a queue
   * @param name Queue name
   * @param options Queue options
   */
  createQueue(name: string, options?: Partial<QueueOptions>): Promise<Queue>;

  /**
   * Create a worker for a queue
   * @param queueName Queue name
   * @param processor Job processor function
   * @param options Worker options
   * @param eventHandlers Event handlers
   */
  createWorker<T = any>(
    queueName: string,
    processor: JobProcessor<T>,
    options?: Partial<WorkerOptions>,
    eventHandlers?: JobEventHandlers,
  ): Promise<Worker>;

  /**
   * Add a job to a queue
   * @param queueName Queue name
   * @param jobData Job data
   * @param options Job options
   */
  addJob(
    queueName: string,
    jobData: JobData,
    options?: Partial<JobsOptions>,
  ): Promise<Job>;

  /**
   * Get a queue by name
   * @param name Queue name
   */
  getQueue(name: string): Queue | undefined;

  /**
   * Get a worker by queue name
   * @param queueName Queue name
   */
  getWorker(queueName: string): Worker | undefined;

  /**
   * Get job by ID from a queue
   * @param queueName Queue name
   * @param jobId Job ID
   */
  getJob(queueName: string, jobId: string): Promise<Job | undefined>;

  /**
   * Get waiting jobs from a queue
   * @param queueName Queue name
   * @param start Start index
   * @param end End index
   */
  getWaitingJobs(
    queueName: string,
    start?: number,
    end?: number,
  ): Promise<Job[]>;

  /**
   * Get active jobs from a queue
   * @param queueName Queue name
   * @param start Start index
   * @param end End index
   */
  getActiveJobs(
    queueName: string,
    start?: number,
    end?: number,
  ): Promise<Job[]>;

  /**
   * Get completed jobs from a queue
   * @param queueName Queue name
   * @param start Start index
   * @param end End index
   */
  getCompletedJobs(
    queueName: string,
    start?: number,
    end?: number,
  ): Promise<Job[]>;

  /**
   * Get failed jobs from a queue
   * @param queueName Queue name
   * @param start Start index
   * @param end End index
   */
  getFailedJobs(
    queueName: string,
    start?: number,
    end?: number,
  ): Promise<Job[]>;

  /**
   * Pause a queue
   * @param queueName Queue name
   */
  pauseQueue(queueName: string): Promise<void>;

  /**
   * Resume a queue
   * @param queueName Queue name
   */
  resumeQueue(queueName: string): Promise<void>;

  /**
   * Clean a queue
   * @param queueName Queue name
   * @param grace Grace period in milliseconds
   * @param type Job type to clean
   * @param limit Maximum number of jobs to clean
   */
  cleanQueue(
    queueName: string,
    grace: number,
    type: 'completed' | 'failed' | 'active' | 'wait',
    limit?: number,
  ): Promise<string[]>;

  /**
   * Get queue statistics
   * @param queueName Queue name
   */
  getQueueStats(queueName: string): Promise<{
    waiting: number;
    active: number;
    completed: number;
    failed: number;
    delayed: number;
    paused: number;
  }>;
}

/**
 * BullMQ client implementation
 */
export class BullMQClient implements IBullMQClient {
  private readonly connection: BullMQConnection;
  private readonly options: BullMQClientOptions;

  /**
   * Create a new BullMQ client
   * @param options Client options
   */
  constructor(options: BullMQClientOptions = {}) {
    this.options = {
      queuePrefix: '',
      defaultJobOptions: {},
      defaultQueueOptions: {},
      defaultWorkerOptions: {
        concurrency: 1,
      },
      ...options,
    };

    // Get the BullMQ connection
    this.connection = BullMQConnection.getInstance();
  }

  /**
   * Format a queue name with the prefix
   * @param name The queue name
   * @returns The formatted queue name
   */
  private formatQueueName(name: string): string {
    return this.options.queuePrefix
      ? `${this.options.queuePrefix}-${name}`
      : name;
  }

  /**
   * Create a queue
   * @param name Queue name
   * @param options Queue options
   */
  public async createQueue(
    name: string,
    options?: Partial<QueueOptions>,
  ): Promise<Queue> {
    const formattedName = this.formatQueueName(name);
    const mergedOptions = {
      ...this.options.defaultQueueOptions,
      ...options,
    };

    return this.connection.createQueue(formattedName, mergedOptions);
  }

  /**
   * Create a worker for a queue
   * @param queueName Queue name
   * @param processor Job processor function
   * @param options Worker options
   * @param eventHandlers Event handlers
   */
  public async createWorker<T = any>(
    queueName: string,
    processor: JobProcessor<T>,
    options?: Partial<WorkerOptions>,
    eventHandlers?: JobEventHandlers,
  ): Promise<Worker> {
    const formattedName = this.formatQueueName(queueName);
    const mergedOptions = {
      ...this.options.defaultWorkerOptions,
      ...options,
    };

    return this.connection.createWorker(
      formattedName,
      processor,
      mergedOptions,
      eventHandlers,
    );
  }

  /**
   * Add a job to a queue
   * @param queueName Queue name
   * @param jobData Job data
   * @param options Job options
   */
  public async addJob(
    queueName: string,
    jobData: JobData,
    options?: Partial<JobsOptions>,
  ): Promise<Job> {
    const formattedName = this.formatQueueName(queueName);
    const queue = this.connection.getQueue(formattedName);

    if (!queue) {
      throw new Error(
        `Queue '${formattedName}' not found. Create the queue first.`,
      );
    }

    const mergedOptions = {
      ...this.options.defaultJobOptions,
      ...options,
    };

    // Use job ID if provided
    if (jobData.id) {
      mergedOptions.jobId = jobData.id;
    }

    try {
      const job = await queue.add(jobData.type, jobData.payload, mergedOptions);

      logger.debug(`Job added to queue '${formattedName}'`, {
        jobId: job.id,
        jobType: jobData.type,
        queueName: formattedName,
      });

      return job;
    } catch (error) {
      logger.error(`Failed to add job to queue '${formattedName}'`, {
        error: error instanceof Error ? error.message : String(error),
        jobType: jobData.type,
        queueName: formattedName,
      });

      throw error;
    }
  }

  /**
   * Get a queue by name
   * @param name Queue name
   */
  public getQueue(name: string): Queue | undefined {
    const formattedName = this.formatQueueName(name);
    return this.connection.getQueue(formattedName);
  }

  /**
   * Get a worker by queue name
   * @param queueName Queue name
   */
  public getWorker(queueName: string): Worker | undefined {
    const formattedName = this.formatQueueName(queueName);
    return this.connection.getWorker(formattedName);
  }

  /**
   * Get job by ID from a queue
   * @param queueName Queue name
   * @param jobId Job ID
   */
  public async getJob(
    queueName: string,
    jobId: string,
  ): Promise<Job | undefined> {
    const queue = this.getQueue(queueName);
    if (!queue) {
      throw new Error(`Queue '${queueName}' not found`);
    }

    return queue.getJob(jobId);
  }

  /**
   * Get waiting jobs from a queue
   * @param queueName Queue name
   * @param start Start index
   * @param end End index
   */
  public async getWaitingJobs(
    queueName: string,
    start = 0,
    end = -1,
  ): Promise<Job[]> {
    const queue = this.getQueue(queueName);
    if (!queue) {
      throw new Error(`Queue '${queueName}' not found`);
    }

    return queue.getWaiting(start, end);
  }

  /**
   * Get active jobs from a queue
   * @param queueName Queue name
   * @param start Start index
   * @param end End index
   */
  public async getActiveJobs(
    queueName: string,
    start = 0,
    end = -1,
  ): Promise<Job[]> {
    const queue = this.getQueue(queueName);
    if (!queue) {
      throw new Error(`Queue '${queueName}' not found`);
    }

    return queue.getActive(start, end);
  }

  /**
   * Get completed jobs from a queue
   * @param queueName Queue name
   * @param start Start index
   * @param end End index
   */
  public async getCompletedJobs(
    queueName: string,
    start = 0,
    end = -1,
  ): Promise<Job[]> {
    const queue = this.getQueue(queueName);
    if (!queue) {
      throw new Error(`Queue '${queueName}' not found`);
    }

    return queue.getCompleted(start, end);
  }

  /**
   * Get failed jobs from a queue
   * @param queueName Queue name
   * @param start Start index
   * @param end End index
   */
  public async getFailedJobs(
    queueName: string,
    start = 0,
    end = -1,
  ): Promise<Job[]> {
    const queue = this.getQueue(queueName);
    if (!queue) {
      throw new Error(`Queue '${queueName}' not found`);
    }

    return queue.getFailed(start, end);
  }

  /**
   * Pause a queue
   * @param queueName Queue name
   */
  public async pauseQueue(queueName: string): Promise<void> {
    const queue = this.getQueue(queueName);
    if (!queue) {
      throw new Error(`Queue '${queueName}' not found`);
    }

    await queue.pause();

    logger.info(`Queue '${queueName}' paused`, {
      queueName,
    });
  }

  /**
   * Resume a queue
   * @param queueName Queue name
   */
  public async resumeQueue(queueName: string): Promise<void> {
    const queue = this.getQueue(queueName);
    if (!queue) {
      throw new Error(`Queue '${queueName}' not found`);
    }

    await queue.resume();

    logger.info(`Queue '${queueName}' resumed`, {
      queueName,
    });
  }

  /**
   * Clean a queue
   * @param queueName Queue name
   * @param grace Grace period in milliseconds
   * @param type Job type to clean
   * @param limit Maximum number of jobs to clean
   */
  public async cleanQueue(
    queueName: string,
    grace: number,
    type: 'completed' | 'failed' | 'active' | 'wait',
    limit = 100,
  ): Promise<string[]> {
    const queue = this.getQueue(queueName);
    if (!queue) {
      throw new Error(`Queue '${queueName}' not found`);
    }

    const cleanedJobs = await queue.clean(grace, limit, type);

    logger.info(`Queue '${queueName}' cleaned`, {
      queueName,
      type,
      cleanedCount: cleanedJobs.length,
      grace,
      limit,
    });

    return cleanedJobs;
  }

  /**
   * Get queue statistics
   * @param queueName Queue name
   */
  public async getQueueStats(queueName: string): Promise<{
    waiting: number;
    active: number;
    completed: number;
    failed: number;
    delayed: number;
    paused: number;
  }> {
    const queue = this.getQueue(queueName);
    if (!queue) {
      throw new Error(`Queue '${queueName}' not found`);
    }

    const [waiting, active, completed, failed, delayed, paused] =
      await Promise.all([
        queue.getWaiting(),
        queue.getActive(),
        queue.getCompleted(),
        queue.getFailed(),
        queue.getDelayed(),
        queue.isPaused(),
      ]);

    return {
      waiting: waiting.length,
      active: active.length,
      completed: completed.length,
      failed: failed.length,
      delayed: delayed.length,
      paused: paused ? 1 : 0,
    };
  }
}
