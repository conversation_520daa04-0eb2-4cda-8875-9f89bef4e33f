import { Document } from 'mongoose';
import { createSchema, createModel } from '@c-cam/core';
import { CameraAttributes } from '@c-cam/types';

/**
 * Camera Document Interface
 * Extends the CameraAttributes (excluding id) and Document
 */
export interface CameraDocument extends Omit<CameraAttributes, 'id'>, Document {}

/**
 * Camera Schema
 * Defines the MongoDB schema for cameras
 */
const CameraSchema = createSchema({
  name: { type: String, required: true },
  ip_address: { type: String, required: true, unique: true },
  rtsp_url: { type: String, required: true },
  preview_image_url: { type: String, required: false },
  created_by: { type: String, required: true },
});

// Create and export the model
const CameraModel = createModel<CameraDocument>('camera', CameraSchema);

export default CameraModel;
