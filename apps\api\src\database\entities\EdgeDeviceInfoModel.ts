import { Document } from 'mongoose';
import { createSchema, createModel } from '@c-cam/core';
import { EdgeDeviceInfoAttributes } from '@c-cam/types';

/**
 * Edge Device Info Document Interface
 * Extends the EdgeDeviceInfoAttributes (excluding id) and Document
 */
export interface EdgeDeviceInfoDocument
  extends Omit<EdgeDeviceInfoAttributes, 'id'>,
    Document {}

/**
 * Edge Device Info Schema
 * Defines the MongoDB schema for edge device information
 */
const EdgeDeviceInfoSchema = createSchema({
  edge_device_id: {
    type: String,
    ref: 'edge_device',
    required: true,
  },
  ram_usage: { type: Number, required: true },
  cpu_usage: { type: Number, required: true },
  disk_usage: { type: Number, required: true },
  total_disk: { type: Number, required: true },
  last_signal: { type: Date, required: true },
  uptime_hours: { type: Number, required: true },
  sync_percent: { type: Number, required: true },
  created_by: { type: String, required: true },
});

// Create and export the model
const EdgeDeviceInfoModel = createModel<EdgeDeviceInfoDocument>(
  'edge_device_info',
  EdgeDeviceInfoSchema,
);

export default EdgeDeviceInfoModel;
