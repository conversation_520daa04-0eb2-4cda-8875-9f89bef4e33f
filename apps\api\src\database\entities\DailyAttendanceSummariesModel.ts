import { Document } from 'mongoose';
import { createSchema, createModel } from '@c-cam/core';
import { DailyAttendanceSummariesAttributes } from '@c-cam/types';

/**
 * Daily Attendance Summaries Document Interface
 * Extends the DailyAttendanceSummariesAttributes (excluding id) and Document
 */
export interface DailyAttendanceSummariesDocument
  extends Omit<DailyAttendanceSummariesAttributes, 'id'>,
    Document {}

/**
 * Daily Attendance Summaries Schema
 * Defines the MongoDB schema for daily attendance summaries
 */
const DailyAttendanceSummariesSchema = createSchema({
  user_id: {
    type: String,
    ref: 'users',
    required: true,
  },
  shift_id: {
    type: String,
    ref: 'shift',
    required: true,
  },
  holiday_id: {
    type: String,
    ref: 'holiday',
    required: false,
  },
  work_date: { type: Date, required: true },
  is_late: { type: Boolean, default: false },
  is_early_leave: { type: Boolean, default: false },
  checkin_time: { type: Date, required: false },
  checkout_time: { type: Date, required: false },
  late_minutes: { type: Number, default: 0 },
  early_leave_minutes: { type: Number, default: 0 },
  expected_work_minutes: { type: Number, required: true },
  total_work_minutes: { type: Number, default: 0 },
  note: { type: String, required: false },
  created_by: { type: String, required: true },
});

// Add indexes
DailyAttendanceSummariesSchema.index(
  { user_id: 1, work_date: 1 },
  { unique: true },
);

// Create and export the model
const DailyAttendanceSummariesModel =
  createModel<DailyAttendanceSummariesDocument>(
    'daily_attendance_summaries',
    DailyAttendanceSummariesSchema,
  );

export default DailyAttendanceSummariesModel;
