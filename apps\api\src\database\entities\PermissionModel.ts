import { Document } from 'mongoose';
import { createSchema, createModel } from '@c-cam/core';
import { PermissionAttributes } from '@c-cam/types';

/**
 * Permission Document Interface
 * Extends the PermissionAttributes (excluding id) and Document
 */
export interface PermissionDocument
  extends Omit<PermissionAttributes, 'id'>,
    Document {}

/**
 * Permission Schema
 * Defines the MongoDB schema for permissions
 */
const PermissionSchema = createSchema({
  role_id: {
    type: String,
    ref: 'role',
    required: true,
  },
  module: { type: String, required: true },
  feature: { type: String, required: true },
  action: { type: String, required: true },
  created_by: { type: String, required: true },
});

// Add indexes
PermissionSchema.index(
  { role_id: 1, module: 1, feature: 1, action: 1 },
  { unique: true },
);

// Create and export the model
const PermissionModel = createModel<PermissionDocument>(
  'permission',
  PermissionSchema,
);

export default PermissionModel;
