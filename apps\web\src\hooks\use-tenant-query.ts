import { useApiQuery } from '@/shared/hooks/use-api-query'

// Types for tenant data
export interface Tenant {
  id: string
  name: string
  description?: string
  status?: string
  created_at?: string
  updated_at?: string
  created_by?: string
}

export interface TenantQueryParams {
  limit?: number
  skip?: number
  sortBy?: string
  sortDirection?: 'asc' | 'desc'
}

export interface TenantListResponse {
  tenants: Array<Tenant>
}

export interface TenantResponse {
  tenant: Tenant
}

/**
 * Hook to fetch all tenants with optional pagination and sorting
 */
export const useTenantsQuery = (params?: TenantQueryParams) => {
  return useApiQuery<TenantListResponse>(
    ['tenants', ...(params ? [JSON.stringify(params)] : [])],
    '/api/tenants',
    params,
  )
}

/**
 * Hook to fetch a single tenant by ID
 */
export const useTenantQuery = (id: string, enabled = true) => {
  return useApiQuery<TenantResponse>(
    ['tenants', id],
    `/api/tenants/${id}`,
    undefined,
    { enabled: enabled && !!id },
  )
}

/**
 * Hook to fetch tenant by name
 */
export const useTenantByNameQuery = (name: string, enabled = true) => {
  return useApiQuery<TenantResponse>(
    ['tenants', 'name', name],
    `/api/tenants/name/${name}`,
    undefined,
    { enabled: enabled && !!name },
  )
}

/**
 * Hook to fetch tenants by status
 */
export const useTenantsByStatusQuery = (status: string, enabled = true) => {
  return useApiQuery<TenantListResponse>(
    ['tenants', 'status', status],
    `/api/tenants/status/${status}`,
    undefined,
    { enabled: enabled && !!status },
  )
}

/**
 * Hook to fetch tenants created by a specific user
 */
export const useTenantsByCreatorQuery = (createdBy: string, enabled = true) => {
  return useApiQuery<TenantListResponse>(
    ['tenants', 'created-by', createdBy],
    `/api/tenants/created-by/${createdBy}`,
    undefined,
    { enabled: enabled && !!createdBy },
  )
}
