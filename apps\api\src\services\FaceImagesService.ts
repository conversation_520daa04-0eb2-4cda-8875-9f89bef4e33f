import { BaseModel, Inject, Injectable } from '@c-cam/core';
import { FaceImagesDocument } from '@/database/entities/FaceImagesModel';
import FaceImagesRepository from '@/repositories/FaceImagesRepository';

/**
 * Service for managing face images
 * Extends the BaseModel with FaceImagesDocument type
 */
@Injectable()
class FaceImagesService extends BaseModel<FaceImagesDocument> {
  constructor(
    @Inject(FaceImagesRepository)
    repository: FaceImagesRepository,
  ) {
    super(repository);
  }

  /**
   * Create a new face image
   * @param imageData The face image data
   * @returns The newly created face image
   */
  async createFaceImage(imageData: {
    user_id: string;
    image_url: string;
    image_angle?: string;
    created_by: string;
  }): Promise<FaceImagesDocument> {
    // Validate image URL (simple check for URL or base64)
    if (
      !imageData.image_url.startsWith('http') &&
      !imageData.image_url.startsWith('data:image')
    ) {
      throw new Error(
        'Invalid image URL format. Must be a valid URL or base64 encoded image data',
      );
    }

    // Validate image angle if provided
    if (imageData.image_angle) {
      const validAngles = ['front', 'left', 'right', 'top', 'bottom'];
      if (!validAngles.includes(imageData.image_angle.toLowerCase())) {
        throw new Error(
          `Invalid image angle. Must be one of: ${validAngles.join(', ')}`,
        );
      }
    }

    // Create the new face image
    return this.create({
      user_id: imageData.user_id,
      image_url: imageData.image_url,
      image_angle: imageData.image_angle?.toLowerCase(),
      created_by: imageData.created_by,
    });
  }

  /**
   * Update a face image
   * @param id The face image ID
   * @param imageData The data to update
   * @returns True if the face image was updated, false otherwise
   */
  async updateFaceImage(
    id: string,
    imageData: Partial<{
      user_id: string;
      image_url: string;
      image_angle: string;
    }>,
  ): Promise<boolean> {
    // Check if the face image exists
    const faceImage = await this.findById(id);
    if (!faceImage) {
      throw new Error(`Face image with ID '${id}' not found`);
    }

    // Validate image URL if provided
    if (
      imageData.image_url &&
      !imageData.image_url.startsWith('http') &&
      !imageData.image_url.startsWith('data:image')
    ) {
      throw new Error(
        'Invalid image URL format. Must be a valid URL or base64 encoded image data',
      );
    }

    // Validate image angle if provided
    if (imageData.image_angle) {
      const validAngles = ['front', 'left', 'right', 'top', 'bottom'];
      if (!validAngles.includes(imageData.image_angle.toLowerCase())) {
        throw new Error(
          `Invalid image angle. Must be one of: ${validAngles.join(', ')}`,
        );
      }
      imageData.image_angle = imageData.image_angle.toLowerCase();
    }

    // Update the face image
    return this.update(id, imageData);
  }

  /**
   * Find face images by user ID
   * @param userId The user ID
   * @returns An array of face images
   */
  async findByUserId(userId: string): Promise<FaceImagesDocument[]> {
    return (this.repository as FaceImagesRepository).findByUserId(userId);
  }

  /**
   * Find face images by image angle
   * @param imageAngle The image angle
   * @returns An array of face images
   */
  async findByImageAngle(imageAngle: string): Promise<FaceImagesDocument[]> {
    // Validate image angle
    const validAngles = ['front', 'left', 'right', 'top', 'bottom'];
    if (!validAngles.includes(imageAngle.toLowerCase())) {
      throw new Error(
        `Invalid image angle. Must be one of: ${validAngles.join(', ')}`,
      );
    }

    // Use the repository's method to find by image angle
    // Note: If this method doesn't exist in the repository, it needs to be implemented
    return this.find({ image_angle: imageAngle.toLowerCase() });
  }

  /**
   * Find face images by date range
   * @param startDate The start date of the range
   * @param endDate The end date of the range
   * @returns An array of face images
   */
  async findByDateRange(
    startDate: Date,
    endDate: Date,
  ): Promise<FaceImagesDocument[]> {
    return (this.repository as FaceImagesRepository).findByDateRange(
      startDate,
      endDate,
    );
  }

  /**
   * Find face images by user ID and image angle
   * @param userId The user ID
   * @param imageAngle The image angle
   * @returns An array of face images
   */
  async findByUserIdAndImageAngle(
    userId: string,
    imageAngle: string,
  ): Promise<FaceImagesDocument[]> {
    // Validate image angle
    const validAngles = ['front', 'left', 'right', 'top', 'bottom'];
    if (!validAngles.includes(imageAngle.toLowerCase())) {
      throw new Error(
        `Invalid image angle. Must be one of: ${validAngles.join(', ')}`,
      );
    }

    // Use the repository's method to find by user ID and image angle
    // Note: If this method doesn't exist in the repository, it needs to be implemented
    return this.find({
      user_id: userId,
      image_angle: imageAngle.toLowerCase(),
    });
  }

  /**
   * Delete face images by user ID
   * @param userId The user ID
   * @returns True if any face images were deleted, false otherwise
   */
  async deleteByUserId(userId: string): Promise<boolean> {
    return (this.repository as FaceImagesRepository).deleteByUserId(userId);
  }

  /**
   * Get the latest front-facing image for a user
   * @param userId The user ID
   * @returns The latest front-facing image or null if not found
   */
  async getLatestFrontImage(
    userId: string,
  ): Promise<FaceImagesDocument | null> {
    // Find all front-facing images for the user
    const frontImages = await this.find({
      user_id: userId,
      image_angle: 'front',
    });

    if (frontImages.length === 0) {
      return null;
    }

    // Sort by creation date (descending) and return the most recent image
    const sortedImages = frontImages.sort((a, b) => {
      return (
        new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
      );
    });

    return sortedImages[0] || null;
  }
}

export default FaceImagesService;
